# XX交易终端 - <PERSON><PERSON> + React + Python Sidecar
# 这是一个 Tauri 应用项目，不是 Python 包
# Python 依赖通过 uv 管理，但不作为可安装包发布

[project]
name = "futunn-trading-platform"
version = "1.0.0"
description = "XX交易终端 - <PERSON>ri + React + Python Sidecar"
requires-python = ">=3.8"
dependencies = [
    "futu-api>=6.0.0",
    "pandas>=1.3.0",
]

[tool.uv]
# 开发依赖 - 开发和测试需要的包
dev-dependencies = [
    "futu-api>=6.0.0",
    "pandas>=1.3.0",
    "pillow>=10.4.0",
    "pytest>=6.0",
    "black>=21.0",
    "flake8>=3.8",
]

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # Python directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | __pycache__
  
  # Node.js directories (Tauri frontend)
  | node_modules
  | \.next
  | \.nuxt
  
  # Tauri directories
  | src-tauri/target
  | src-tauri/gen
  
  # Temporary directories
  | temp
  | tmp
  | logs
  | data
)/
'''

[tool.pytest.ini_options]
testpaths = ["src-python"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
