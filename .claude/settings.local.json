{"permissions": {"allow": ["Bash(ls:*)", "Bash(./FutuOpenD.app/Contents/MacOS/FutuOpenD:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./fixrun.sh:*)", "Bash(python test_opend_connection.py:*)", "Bash(pip install:*)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(uv run:*)", "Bash(grep:*)", "<PERSON><PERSON>(timeout:*)", "Bash(sqlite3:*)", "Bash(uv add:*)", "Bash(gtimeout 30 uv run src/trading_software_gui_fixed.py)", "<PERSON><PERSON>(pkill:*)", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "<PERSON><PERSON>(python:*)", "Bash(rm:*)", "Bash(cp:*)", "Bash(find:*)", "Bash(PYTHONPATH=../adapters uv run python src/trading_software_gui_fixed.py)", "Bash(uv sync:*)", "Bash(node:*)", "Bash(npm:*)", "Bash(cargo install:*)", "<PERSON><PERSON>(source:*)", "Bash(rustc:*)", "Bash(npx:*)", "Bash(cargo --version)", "Bash(cargo tauri:*)", "<PERSON><PERSON>(touch:*)", "mcp__Playwright__browser_navigate", "mcp__Playwright__browser_install", "<PERSON><PERSON>(curl:*)", "WebFetch(domain:localhost)", "mcp__Playwright__browser_console_messages", "mcp__Playwright__browser_close", "<PERSON><PERSON>(time:*)", "Bash(cargo check:*)", "Bash(cargo:*)", "Bash(kill:*)", "<PERSON><PERSON>(true)", "Bash(gemini:*)", "Bash(./scripts/build-python-binaries.sh:*)", "Bash(yarn tauri:*)", "<PERSON><PERSON>(open:*)", "Bash(./counter-service --help)", "Bash(./counter-service)", "Bash(yarn build)", "<PERSON><PERSON>(cat:*)", "Bash(/Users/<USER>/Desktop/stock/src-tauri/target/release/bundle/macos/富途交易平台.app/Contents/MacOS/counter-service)", "Bash(./scripts/auto-test-build.sh:*)", "Bash(./scripts/diagnose-tauri-cache.sh:*)", "Bash(pgrep:*)", "Bash(./scripts/build-production.sh:*)", "Ba<PERSON>(log show:*)", "Bash(log stream:*)", "Bash(ps:*)", "<PERSON><PERSON>(wait)", "Bash(./testing/test_packaged_app.py \"/Users/<USER>/Desktop/stock/src-tauri/target/release/bundle/macos/富途交易平台.app\")", "<PERSON><PERSON>(dos2unix:*)", "Bash(yarn dev)", "mcp__Playwright__browser_click", "mcp__Playwright__browser_snapshot", "mcp__Playwright__browser_press_key", "mcp__Playwright__browser_wait_for", "mcp__Playwright__browser_evaluate", "mcp__Playwright__browser_take_screenshot", "mcp__Playwright__browser_hover", "Bash(yarn add:*)", "Bash(yarn tailwindcss init:*)", "mcp__Playwright__browser_type", "<PERSON><PERSON>(sed:*)", "mcp__Playwright__browser_resize", "Bash(yarn list:*)", "Bash(yarn:*)", "Bash(NODE_ENV=development npx electron .)", "<PERSON><PERSON>(tsc:*)", "<PERSON><PERSON>(electron-builder:*)", "Bash(osascript:*)", "<PERSON><PERSON>(screencapture:*)", "Bash(\"dist/mac/量化交易终端.app/Contents/MacOS/量化交易终端\")", "Bash(NODE_ENV=development node dist/testing/test-ipc-handlers-cli.js)", "Bash(NODE_ENV=development node dist/testing/testing/test-ipc-handlers-cli.js)", "Bash(NODE_ENV=development ts-node testing/test-ipc-handlers-cli.ts)", "Bash(ts-node:*)", "Bash(./test-ipc.sh:*)", "Bash(NODE_ENV=development ts-node -r tsconfig-paths/register testing/test-ipc-handlers-cli.ts)", "Bash(NODE_ENV=development ts-node -r tsconfig-paths/register testing/automated-test.ts)", "<PERSON><PERSON>(gtimeout:*)", "Bash(ln:*)"], "deny": []}}