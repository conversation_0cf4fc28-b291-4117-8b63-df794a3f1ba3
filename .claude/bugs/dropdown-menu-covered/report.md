# Bug 报告：首页策略任务卡片更多按钮弹出层被遮挡

## Bug 概要
首页策略任务中的卡片项的"更多"按钮（⋯）的下拉菜单被下方的卡片项遮挡，导致用户无法看到完整的菜单选项，影响功能使用。

## Bug 详情

### 预期行为
- 点击任务卡片的"更多"按钮（⋯）时，应该显示完整的下拉菜单
- 下拉菜单应该显示在所有其他元素之上，不被遮挡
- 用户应该能够看到并点击所有菜单选项：编辑、复制、查看日志、清仓、删除等

### 实际行为
- 点击"更多"按钮后，下拉菜单出现但被下方的任务卡片遮挡
- 只能看到菜单的上半部分，下半部分的菜单项被遮住
- 用户无法访问被遮挡的菜单选项

### 复现步骤
1. 打开应用首页
2. 确保页面显示多个策略任务卡片（至少两行）
3. 点击第一行任一任务卡片的"更多"按钮（⋯）
4. 观察下拉菜单显示效果

### 环境信息
- 平台：macOS Darwin 21.6.0
- 浏览器：Chrome (通过 Tauri)
- 项目：股票交易终端应用
- 组件：Dashboard > TaskCard > DropdownMenu
- 开发服务器：http://localhost:1420

## 影响评估

### 严重级别
**Medium** - 影响用户体验但不阻断核心功能

### 受影响用户
- 所有使用策略任务管理功能的用户
- 特别是有多个任务卡片的用户

### 受影响功能
- 任务编辑功能（菜单项可能被遮挡）
- 任务复制功能
- 任务日志查看功能
- 任务清仓功能
- 任务删除功能

## 初步分析

### 疑似根本原因
CSS z-index 层级问题：
- DropdownMenu 组件的 z-index 值可能不够高
- 容器元素的 z-index 设置可能存在冲突
- 下方卡片的堆叠上下文可能覆盖了下拉菜单

### 涉及组件
- `/src/components/Dashboard/DropdownMenu.tsx` - 下拉菜单组件
- `/src/components/Dashboard/TaskCard.tsx` - 任务卡片组件
- `/src/components/Dashboard/TaskList.tsx` - 任务列表容器
- `/src/pages/Dashboard/Dashboard.tsx` - 主页面组件

### 相关样式文件
- DropdownMenu 组件内联样式
- TaskCard 组件的定位和层级样式

## 技术背景

### 项目技术栈
- React 18 + TypeScript
- Tauri 桌面应用框架
- 内联样式系统
- 弹出框风格设计规范

### 相关设计规范
根据项目的 UI 设计风格规范：
- 使用层次清晰的信息架构
- 交互元素需要适当的视觉反馈
- 弹出层需要确保在最顶层显示

## 备注
此问题在之前的开发过程中已被发现和修复，但可能在后续更改中重新出现或修复不完整。需要验证当前的修复状态并确保问题彻底解决。