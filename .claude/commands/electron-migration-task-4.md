# Task: 实现华盛通 JavaScript SDK 集成

## Overview
将 Python 华盛通接口转换为 TypeScript，实现账户管理、订单执行和持仓查询功能

## Context
华盛通提供交易执行服务。需要将现有的 Python 代码 (src-python/huasheng_client) 转换为 TypeScript。

## Instructions

1. **分析现有 Python 代码**
   - 查看 `src-python/huasheng_client/src/Server/ApiServer/api_trade_server.py`
   - 理解 API 调用模式和数据结构
   - 记录所有需要实现的接口

2. **创建华盛通适配器** `electron-main/src/trading/adapters/HuashengAdapter.ts`
   - 实现 ITradingAdapter 接口
   - 转换 Python API 调用为 TypeScript
   - 保持相同的功能和接口

3. **实现核心交易功能**
   - 账户登录和认证
   - 获取账户信息
   - 下单接口
   - 撤单接口
   - 查询持仓
   - 查询订单

4. **实现 WebSocket 连接**
   - 转换 TCP 客户端为 WebSocket
   - 处理实时交易状态推送
   - 实现断线重连机制

5. **添加错误处理和日志**
   - 捕获所有可能的错误
   - 记录详细的交易日志
   - 实现重试机制

## Code Reference
```python
# 参考 Python 实现
# src-python/huasheng_client/src/Server/ServerImp/api_tcp_client.py
```

## Success Criteria
- [ ] 所有交易接口功能正常
- [ ] 能够成功连接华盛通服务器
- [ ] 订单执行准确无误
- [ ] 错误处理完善

## Related Requirements
- REQ-2.2: 接口迁移需求
- REQ-3.2: 系统维护