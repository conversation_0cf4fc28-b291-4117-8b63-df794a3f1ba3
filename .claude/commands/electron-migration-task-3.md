# Task: 实现 Electron 主进程基础结构

## Overview
创建主进程入口文件，实现窗口管理器，配置进程安全设置，实现基础 IPC 通信

## Context
这是 Electron 应用的核心部分。主进程负责管理应用生命周期、创建窗口和处理系统级操作。

## Instructions

1. **创建主进程入口文件** `electron-main/src/index.ts`
   - 实现 app 生命周期管理
   - 处理应用启动和退出
   - 配置安全策略

2. **实现窗口管理器** `electron-main/src/windowManager.ts`
   - 创建主窗口
   - 配置窗口安全选项
   - 实现窗口状态管理

3. **配置安全设置**
   - 禁用 nodeIntegration
   - 启用 contextIsolation
   - 配置 CSP (Content Security Policy)

4. **创建 preload 脚本** `electron-main/src/preload.ts`
   - 使用 contextBridge 暴露安全 API
   - 定义渲染进程可用的方法

5. **实现基础 IPC 处理器** `electron-main/src/ipc/handlers.ts`
   - 注册基础 IPC 通道
   - 实现错误处理
   - 添加日志记录

## Code Structure
```typescript
// electron-main/src/index.ts
import { app, BrowserWindow } from 'electron';
import { WindowManager } from './windowManager';
import { registerIPCHandlers } from './ipc/handlers';

// 主进程逻辑...
```

## Success Criteria
- [ ] Electron 应用能够启动
- [ ] 主窗口正确显示
- [ ] 安全配置正确
- [ ] IPC 通信正常工作

## Related Requirements
- REQ-2.1: 核心功能保持
- REQ-3.1: 开发者体验