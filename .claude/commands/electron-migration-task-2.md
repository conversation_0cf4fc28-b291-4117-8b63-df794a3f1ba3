# Task: 安装核心依赖

## Overview
安装 Electron 25+、electron-builder、React 18 相关依赖和开发工具依赖。使用 yarn 作为包管理器。

## Context
在创建项目结构后，需要安装所有必要的依赖包。特别注意使用 yarn 而不是 npm。

## Instructions

1. **安装 Electron 核心依赖**
   ```bash
   yarn add --dev electron@^25.0.0
   yarn add --dev electron-builder@^24.0.0
   ```

2. **安装 TypeScript 和类型定义**
   ```bash
   yarn add --dev @types/node
   yarn add --dev @electron/types
   ```

3. **安装构建工具**
   ```bash
   yarn add --dev concurrently
   yarn add --dev cross-env
   yarn add --dev wait-on
   ```

4. **安装 Electron 开发工具**
   ```bash
   yarn add --dev electron-devtools-installer
   yarn add --dev electron-debug
   ```

5. **安装安全相关依赖**
   ```bash
   yarn add electron-store
   yarn add --dev @electron/notarize
   ```

## Success Criteria
- [ ] 所有依赖安装成功
- [ ] package.json 包含所有必要的 Electron 依赖
- [ ] yarn.lock 文件已更新
- [ ] 没有版本冲突

## Related Requirements
- REQ-2.3: 架构改进需求
- REQ-5.1: 技术约束