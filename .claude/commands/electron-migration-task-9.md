# Task: 迁移前端组件到 Electron

## Overview
迁移 React 组件到 Electron 渲染进程，更新组件的 IPC 调用方式，保持 UI 一致性，更新状态管理逻辑

## Context
将现有的 React 前端代码适配到 Electron 环境，主要是更新通信方式从 Tauri API 到 Electron IPC。

## Instructions

1. **更新通信层** 
   - 创建新的 `src/communication/electronClient.ts`
   - 实现 ElectronTradingClient 类
   - 保持与现有 TradingClientBase 接口兼容
   - 使用 window.electronAPI 替代 Tauri API

2. **更新现有客户端**
   ```typescript
   // src/communication/futuClient.ts
   export class FutuClient extends ElectronTradingClient {
       constructor() {
           super('futu');
       }
   }
   ```

3. **创建 Electron 预加载脚本接口**
   ```typescript
   // src/types/electron.d.ts
   export interface IElectronAPI {
       trading: {
           connect: (adapter: string, config: any) => Promise<ApiResponse>;
           getQuote: (adapter: string, stockCode: string) => Promise<ApiResponse>;
           // ... 其他方法
       };
       onRealtimeData: (callback: (data: any) => void) => void;
   }
   ```

4. **更新组件中的 API 调用**
   - 搜索所有使用 `@tauri-apps/api` 的地方
   - 替换为 Electron IPC 调用
   - 保持相同的异步模式

5. **更新事件监听**
   - 将 Tauri 事件监听改为 Electron 事件
   - 确保事件名称映射正确
   - 处理事件清理

6. **测试所有功能**
   - Dashboard 页面正常显示
   - 任务卡片功能正常
   - 实时数据更新正常
   - 交易功能正常

## Migration Checklist
- [ ] communication/index.ts
- [ ] communication/tradingClient.ts
- [ ] communication/futuClient.ts
- [ ] communication/huashengClient.ts
- [ ] pages/Dashboard/Dashboard.tsx
- [ ] hooks/useDashboard.ts

## Success Criteria
- [ ] 所有组件正常渲染
- [ ] IPC 通信正常工作
- [ ] 实时数据更新流畅
- [ ] 无 Tauri 相关错误

## Related Requirements
- REQ-2.1: 核心功能保持
- REQ-3.3: 用户功能