# Task: 实现富途行情适配器

## Overview
实现连接管理、实时数据订阅、数据获取接口和推送数据处理

## Context
基于上一步的 SDK 研究结果，实现富途行情数据适配器。这是获取实时市场数据的核心组件。

## Instructions

1. **创建富途适配器** `electron-main/src/trading/adapters/FutuAdapter.ts`
   - 实现 IMarketAdapter 接口
   - 配置连接参数（host, port, key）

2. **实现连接管理**
   - 连接到 OpenD 网关
   - 实现心跳保持
   - 处理断线重连
   - 监控连接状态

3. **实现数据订阅功能**
   ```typescript
   async subscribe(stockCode: string, dataTypes: DataType[]): Promise<string>
   async unsubscribe(stockCode: string, dataTypes: DataType[]): Promise<void>
   ```

4. **实现数据获取接口**
   - getQuote: 获取实时报价
   - getOrderBook: 获取买卖盘（10档）
   - getTicker: 获取逐笔成交
   - getBrokerQueue: 获取经纪队列

5. **处理实时推送数据**
   - 设置推送回调处理器
   - 解析不同类型的推送数据
   - 转换为统一的内部格式
   - 触发数据更新事件

6. **添加数据转换层**
   - 将富途数据格式转换为内部格式
   - 处理数据字段映射
   - 确保数据一致性

## Code Structure
```typescript
class FutuMarketAdapter implements IMarketAdapter {
    private client: any; // SDK client
    private subscriptions: Map<string, string>;
    
    async connect(config: FutuConfig): Promise<void> {
        // 连接实现
    }
    
    // 其他方法实现...
}
```

## Success Criteria
- [ ] 成功连接到 OpenD
- [ ] 实时数据推送正常
- [ ] 所有数据接口工作正常
- [ ] 断线重连机制有效

## Related Requirements
- REQ-2.2: 接口迁移需求
- REQ-3.3: 用户功能