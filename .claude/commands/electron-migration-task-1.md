# Task: 创建 Electron 项目结构

## Overview
初始化新的 Electron 项目，配置 TypeScript 支持，设置项目目录结构，配置 ESLint 和 Prettier

## Context
这是 Electron 迁移的第一步，需要创建基础的项目结构。使用 yarn 作为包管理器。

## Instructions

1. **创建 electron 分支**
   ```bash
   git checkout -b electron
   ```

2. **创建 Electron 项目目录结构**
   ```bash
   mkdir -p electron-main/src/{ipc,trading,services,utils}
   mkdir -p electron-main/src/trading/adapters
   ```

3. **初始化 TypeScript 配置**
   - 创建 `electron-main/tsconfig.json` 配置文件
   - 配置 Node.js 和 Electron 类型支持

4. **配置 ESLint 和 Prettier**
   - 更新 `.eslintrc` 支持 Electron 主进程代码
   - 确保 Prettier 配置一致

5. **更新 package.json**
   - 添加 Electron 相关脚本
   - 保持现有依赖不变

## Success Criteria
- [ ] electron-main 目录结构创建完成
- [ ] TypeScript 配置文件就绪
- [ ] ESLint 能正确检查 Electron 代码
- [ ] 项目结构清晰，符合设计文档

## Related Requirements
- REQ-2.3: 架构改进需求
- REQ-5.1: 技术约束