# Task: 实现任务管理器

## Overview
创建 TaskManager 类，实现任务创建和配置，任务生命周期管理，任务状态持久化

## Context
任务管理器是整个系统的核心，负责管理所有策略任务的创建、运行和监控。

## Instructions

1. **创建任务管理器** `electron-main/src/task/TaskManager.ts`
   ```typescript
   class TaskManager {
       private tasks: Map<string, StrategyTask>;
       private marketDataManager: MarketDataManager;
       private tradingManager: TradingManager;
       private strategyEngine: StrategyEngine;
   }
   ```

2. **实现任务创建**
   - createTask: 创建新任务
   - 生成唯一任务ID
   - 创建策略实例
   - 订阅所需市场数据
   - 初始化任务状态

3. **实现任务生命周期管理**
   - startTask: 启动任务
   - pauseTask: 暂停任务
   - stopTask: 停止任务
   - deleteTask: 删除任务
   - 处理任务状态转换

4. **实现任务执行逻辑**
   - 接收市场数据更新
   - 调用策略处理数据
   - 执行交易信号
   - 更新任务状态
   - 处理异常情况

5. **实现任务监控**
   - 监控任务运行状态
   - 记录任务性能指标
   - 检测任务异常
   - 实现任务自动恢复

6. **实现状态持久化**
   - 保存任务配置
   - 保存任务状态
   - 保存持仓信息
   - 实现崩溃恢复

## Task State Management
```typescript
enum TaskStatus {
    CREATED = 'created',
    RUNNING = 'running',
    PAUSED = 'paused',
    STOPPED = 'stopped',
    ERROR = 'error'
}
```

## Success Criteria
- [ ] 任务创建和配置功能完整
- [ ] 任务状态转换正确
- [ ] 策略执行稳定可靠
- [ ] 状态持久化和恢复正常

## Related Requirements
- REQ-2.1: 核心功能保持