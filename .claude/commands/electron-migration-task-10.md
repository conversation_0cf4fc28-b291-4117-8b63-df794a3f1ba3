# Task: 实现配置管理服务

## Overview
使用 electron-store 存储配置，实现敏感信息加密存储，实现配置迁移工具，添加配置验证

## Context
配置管理是系统的基础服务，需要安全地存储用户配置、API 密钥等敏感信息。

## Instructions

1. **创建配置服务** `electron-main/src/services/ConfigService.ts`
   ```typescript
   import Store from 'electron-store';
   import { safeStorage } from 'electron';
   
   class ConfigService {
       private store: Store;
       private encryptedKeys: string[] = ['apiKey', 'apiSecret'];
   }
   ```

2. **定义配置结构**
   ```typescript
   interface AppConfig {
       general: {
           language: string;
           theme: 'light' | 'dark';
           autoStart: boolean;
       };
       trading: {
           futu: {
               host: string;
               port: number;
               encryptKey?: string;
           };
           huasheng: {
               apiUrl: string;
               accountId: string;
               apiKey?: string; // 加密存储
               apiSecret?: string; // 加密存储
           };
       };
       strategies: StrategyConfig[];
   }
   ```

3. **实现安全存储**
   - 使用 safeStorage API 加密敏感数据
   - 实现加密/解密方法
   - 处理不支持加密的情况
   - 添加数据完整性检查

4. **实现配置迁移工具**
   - 读取旧的 Tauri 配置文件
   - 转换配置格式
   - 迁移到 Electron 存储
   - 备份原始配置

5. **添加配置验证**
   - 使用 JSON Schema 验证配置
   - 检查必填字段
   - 验证数据类型和格式
   - 提供默认值

6. **实现配置管理 API**
   - get: 获取配置项
   - set: 设置配置项
   - delete: 删除配置项
   - reset: 重置为默认值
   - export/import: 导出导入配置

## Security Considerations
- API 密钥必须加密存储
- 导出配置时排除敏感信息
- 实现访问权限控制

## Success Criteria
- [ ] 配置安全存储
- [ ] 敏感信息已加密
- [ ] 配置迁移工具可用
- [ ] 验证机制正常工作

## Related Requirements
- REQ-4.4: 兼容性要求
- REQ-5.2: 接口约束