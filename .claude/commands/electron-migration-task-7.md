# Task: 实现行情数据管理器

## Overview
创建 MarketDataManager 类，实现多任务数据共享机制，实现引用计数管理和数据缓存策略

## Context
这是实现"同一支股票多任务共享数据"的核心组件。避免重复订阅，优化性能。

## Instructions

1. **创建数据管理器** `electron-main/src/market/MarketDataManager.ts`
   ```typescript
   class MarketDataManager {
       private subscriptions: Map<string, SubscriptionInfo>;
       private dataCache: MarketDataCache;
       private taskSubscriptions: Map<string, Set<string>>;
   }
   ```

2. **实现订阅管理**
   - subscribeForTask: 为任务订阅数据
   - unsubscribeForTask: 取消任务订阅
   - 使用引用计数跟踪订阅使用情况
   - 最后一个任务取消时才真正取消订阅

3. **实现数据缓存** `electron-main/src/market/MarketDataCache.ts`
   - 缓存最新报价数据
   - 缓存买卖盘数据
   - 缓存最近N条逐笔数据
   - 实现缓存过期机制

4. **实现数据分发机制**
   - 接收适配器推送的数据
   - 更新缓存
   - 找出订阅该股票的所有任务
   - 通知相关任务数据更新

5. **优化性能**
   - 批量处理数据更新
   - 使用防抖减少更新频率
   - 实现数据压缩（如果需要）

6. **添加监控和日志**
   - 记录订阅数量
   - 监控缓存命中率
   - 记录数据延迟

## Key Features
- 多任务共享同一订阅
- 智能引用计数
- 高效数据缓存
- 低延迟数据分发

## Success Criteria
- [ ] 多任务订阅同一股票只产生一个订阅
- [ ] 引用计数正确管理
- [ ] 数据缓存有效工作
- [ ] 数据分发延迟 < 10ms

## Related Requirements
- REQ-2.1: 核心功能保持
- REQ-3.3: 用户功能