# 项目结构与开发规范

## 目录结构

### 根目录结构
```
stock/
├── .claude/                    # Claude AI指导文档
│   └── steering/              # 项目指导文档
├── config/                    # 配置文件目录
│   ├── development.json       # 开发环境配置
│   ├── production.json        # 生产环境配置
│   └── user.json.example      # 用户配置模板
├── docs/                      # 项目文档
├── scripts/                   # 构建和部署脚本
├── src/                       # React前端源码
├── src-tauri/                 # Tauri后端源码
├── src-python/                # Python适配器源码
├── testing/                   # 测试相关文件
├── dist/                      # 构建输出目录
├── node_modules/              # Node.js依赖
├── package.json               # 前端依赖配置
├── pyproject.toml             # Python依赖配置
├── tsconfig.json              # TypeScript配置
├── vite.config.ts             # Vite构建配置
└── README.md                  # 项目说明文档
```

### 前端源码结构 (src/)
```
src/
├── components/                # 可复用组件
│   ├── Dashboard/            # 仪表盘相关组件
│   │   ├── GlobalControls.tsx    # 全局控制组件
│   │   ├── TaskCard.tsx          # 任务卡片组件
│   │   ├── TaskList.tsx          # 任务列表组件
│   │   ├── TaskConfigModal.tsx   # 任务配置弹窗
│   │   └── TaskDetailsModal.tsx  # 任务详情弹窗
│   ├── LoadingOverlay.tsx    # 加载遮罩组件
│   └── index.ts              # 组件统一导出
├── pages/                    # 页面组件
│   ├── Dashboard/            # 仪表盘页面
│   │   ├── Dashboard.tsx     # 主仪表盘
│   │   └── index.ts          # 页面导出
│   └── index.ts              # 页面统一导出
├── hooks/                    # 自定义Hooks
│   ├── useDashboard.ts       # 仪表盘业务逻辑
│   └── index.ts              # Hooks统一导出
├── communication/            # 通信封装
│   ├── futuClient.ts         # 富途API客户端
│   ├── huashengClient.ts     # 华盛API客户端
│   └── index.ts              # 通信模块导出
├── types/                    # 类型定义
│   └── index.ts              # 类型统一导出
├── styles/                   # 样式文件
│   └── Dashboard.css         # 仪表盘样式
├── utils/                    # 工具函数
│   └── index.ts              # 工具函数导出
├── data/                     # 静态数据
│   └── strategyTemplates.ts  # 策略模板数据
├── services/                 # 业务服务
│   └── brokerManager.ts      # 券商管理服务
├── App.tsx                   # 应用根组件
├── main.tsx                  # 应用入口
└── styles.css                # 全局样式
```

### 后端源码结构 (src-tauri/)
```
src-tauri/
├── src/
│   ├── communication/        # 通信模块
│   │   ├── mod.rs           # 模块声明
│   │   ├── message_types.rs  # 消息类型定义
│   │   ├── sidecar_client.rs # Sidecar客户端
│   │   └── trading_client.rs # 交易客户端
│   ├── types/               # 类型定义
│   │   └── mod.rs           # 类型模块
│   ├── main.rs              # 应用入口
│   ├── sidecar_manager.rs   # Sidecar管理器
│   └── multi_sidecar_manager.rs # 多Sidecar管理
├── Cargo.toml               # Rust依赖配置
├── tauri.conf.json          # Tauri配置
├── build.rs                 # 构建脚本
└── icons/                   # 应用图标
```

### Python适配器结构 (src-python/)
```
src-python/
├── adapters/                # 标准化适配器
│   ├── config_manager.py    # 配置管理
│   ├── tauri_communication.py # 通信基础类
│   ├── futu_adapter_enhanced.py # 富途适配器
│   └── huasheng_adapter_enhanced.py # 华盛适配器
├── futu_client/             # 富途客户端
│   ├── src/                 # 源码
│   ├── docs/                # 文档
│   └── OpenD/               # OpenD相关文件
├── huasheng_client/         # 华盛客户端
│   ├── src/                 # 源码
│   └── docs/                # 文档
└── test_services/           # 测试服务
    ├── counter_service.py   # 计数器服务
    └── run_tests.py         # 测试运行器
```

## 命名约定

### 文件命名
- **React组件**: PascalCase (例: `TaskCard.tsx`)
- **Hooks**: camelCase, 以use开头 (例: `useDashboard.ts`)
- **工具函数**: camelCase (例: `formatCurrency.ts`)
- **类型文件**: camelCase (例: `index.ts`)
- **样式文件**: PascalCase (例: `Dashboard.css`)
- **Python文件**: snake_case (例: `futu_adapter_enhanced.py`)
- **Rust文件**: snake_case (例: `sidecar_manager.rs`)

### 变量命名
- **React组件名**: PascalCase (例: `TaskCard`, `GlobalControls`)
- **函数名**: camelCase (例: `handleSubmit`, `formatPrice`)
- **变量名**: camelCase (例: `taskList`, `isLoading`)
- **常量**: SCREAMING_SNAKE_CASE (例: `DEFAULT_CONFIG`, `API_ENDPOINTS`)
- **枚举**: PascalCase (例: `TaskStatus`, `ConnectionStatus`)

### CSS类名
- **组件样式**: kebab-case, 以组件名为前缀 (例: `.task-card`, `.global-controls`)
- **状态类**: kebab-case (例: `.is-loading`, `.is-active`)
- **工具类**: kebab-case (例: `.btn-primary`, `.text-center`)

## 代码组织模式

### React组件结构
```typescript
// 1. 导入语句
import React, { useState, useEffect } from 'react';
import { Task } from '../../types';

// 2. 接口定义
interface TaskCardProps {
    task: Task;
    onToggle: (taskId: string) => void;
    onEdit: (taskId: string) => void;
}

// 3. 组件实现
export const TaskCard: React.FC<TaskCardProps> = ({
    task,
    onToggle,
    onEdit
}) => {
    // 4. 状态定义
    const [isLoading, setIsLoading] = useState(false);

    // 5. 副作用
    useEffect(() => {
        // 副作用逻辑
    }, [task.id]);

    // 6. 事件处理函数
    const handleToggle = () => {
        onToggle(task.id);
    };

    // 7. 渲染逻辑
    return (
        <div className="task-card">
            {/* JSX内容 */}
        </div>
    );
};
```

### 自定义Hooks模式
```typescript
// 1. 导入和类型定义
import { useState, useEffect } from 'react';
import { Task, GlobalStatus } from '../types';

interface UseDashboardReturn {
    // 状态
    tasks: Task[];
    isLoading: boolean;
    
    // 操作函数
    addTask: (task: Task) => Promise<void>;
    deleteTask: (taskId: string) => Promise<void>;
}

// 2. Hook实现
export const useDashboard = (): UseDashboardReturn => {
    // 3. 状态管理
    const [tasks, setTasks] = useState<Task[]>([]);
    const [isLoading, setIsLoading] = useState(false);

    // 4. 业务逻辑函数
    const addTask = async (task: Task) => {
        // 实现逻辑
    };

    // 5. 副作用
    useEffect(() => {
        // 初始化逻辑
    }, []);

    // 6. 返回接口
    return {
        tasks,
        isLoading,
        addTask,
        deleteTask
    };
};
```

### Python适配器模式
```python
# 1. 导入语句
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import json

# 2. 基础接口定义
class TradingAdapter(ABC):
    """交易适配器基础接口"""
    
    @abstractmethod
    async def connect(self) -> bool:
        """连接到交易平台"""
        pass
    
    @abstractmethod
    async def get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        pass

# 3. 具体实现
class FutuAdapter(TradingAdapter):
    """富途交易适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.client = None
    
    async def connect(self) -> bool:
        # 连接实现
        return True
```

## UI设计规范

### "弹出框风格"设计系统
基于项目CLAUDE.md中定义的UI设计风格，严格遵循以下规范：

#### 布局结构
- **主容器**: 灰色背景 (`#f8f9fa`) + 边框 + 圆角
- **内容区域**: 白色背景的卡片式设计
- **信息层次**: 通过容器嵌套和色彩对比创建清晰层次

#### 色彩系统
- **主要文字**: `#495057` (标题), `#666` (描述), `#6c757d` (次要文字)
- **背景色**: `#f8f9fa` (主容器), `white` (卡片)
- **边框色**: `#e9ecef`, `#dee2e6` (不同层级)
- **状态色**: 绿色(运行中), 黄色(暂停), 红色(错误), 蓝色(已清仓)

#### 间距规范
- **区域间隔**: `marginTop: '30px'` + 分隔线 + `paddingTop: '20px'`
- **容器内边距**: `padding: '20px'` (主容器), `padding: '15px'` (卡片)
- **元素间距**: `marginBottom: '15px'` (表单字段)

### 组件设计原则
1. **状态优先**: 系统核心状态具有最高可视化优先级
2. **数据为王**: 界面布局围绕关键数据展开
3. **减少噪音**: 只显示决策相关的关键信息
4. **控制在手**: 自动化为主，但手动干预权限必须直接可靠

## 开发工作流

### 分支管理
- **main**: 主分支，生产就绪代码
- **develop**: 开发分支，集成新功能
- **feature/***: 功能分支，单一功能开发
- **hotfix/***: 紧急修复分支

### 提交规范
```
<type>(<scope>): <subject>

<body>

<footer>
```

类型定义:
- **feat**: 新功能
- **fix**: 修复
- **docs**: 文档
- **style**: 格式化
- **refactor**: 重构
- **test**: 测试
- **chore**: 构建过程或辅助工具变动

### 代码审查清单
- [ ] 代码符合命名约定
- [ ] 组件结构清晰，职责单一
- [ ] 类型定义完整，无any类型
- [ ] 错误处理完善
- [ ] 性能考虑（避免不必要的重渲染）
- [ ] UI符合设计规范
- [ ] 测试覆盖关键逻辑
- [ ] 文档更新及时

### 构建和部署
- **开发构建**: `yarn tauri dev`
- **生产构建**: `./scripts/build-production.sh`
- **测试**: `yarn test` (前端) + `python -m pytest` (后端)
- **代码检查**: `yarn lint` + `cargo clippy`