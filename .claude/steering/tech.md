# 技术栈与架构决策

## 核心技术栈

### 前端技术
- **React 18**: 现代化前端框架，提供高效的组件化开发
- **TypeScript**: 类型安全的JavaScript，确保代码质量和可维护性
- **Vite**: 快速构建工具，提供优秀的开发体验
- **Lucide React**: 现代化图标库，提供一致的视觉元素
- **Recharts**: 数据可视化图表库，用于实时数据展示

### 桌面应用框架
- **Tauri**: 基于Rust的跨平台桌面应用框架
  - 高性能：原生性能，低内存占用
  - 安全性：Rust的内存安全特性
  - 跨平台：Windows、macOS、Linux全支持
  - 现代化：Web技术构建UI，Rust处理系统调用

### 后端与数据适配
- **Rust**: Tauri后端语言，处理系统调用和进程管理
- **Python**: 交易接口适配器开发语言
  - 富途OpenAPI: 港股行情数据接入
  - 华盛ProAPI: 交易功能集成
  - 标准化适配器设计，易于扩展新平台

### 开发工具链
- **Playwright**: 自动化测试框架
- **ESLint**: 代码质量检查工具
- **uv**: Python包管理器，快速依赖管理
- **Yarn**: Node.js包管理器

## 架构设计原则

### 模块化分层架构
```
┌─────────────────────────────────────┐
│           React Frontend            │  ← 用户界面层
├─────────────────────────────────────┤
│         Tauri Commands              │  ← API抽象层
├─────────────────────────────────────┤
│          Rust Backend              │  ← 系统服务层
├─────────────────────────────────────┤
│       Sidecar Communication        │  ← 进程通信层
├─────────────────────────────────────┤
│       Python Adapters             │  ← 数据适配层
└─────────────────────────────────────┘
```

### 核心设计决策

#### 1. Tauri Sidecar架构
- **选择原因**: 需要集成Python生态的交易接口
- **实现方式**: Python脚本作为独立进程运行，通过标准输入输出通信
- **优势**: 语言解耦、进程隔离、易于调试和维护

#### 2. React组件化设计
- **状态管理**: 使用React Hooks进行本地状态管理
- **组件分类**: 
  - 页面组件(Pages): 路由级别的顶层组件
  - 功能组件(Components): 可复用的业务组件
  - 工具组件(Utils): 纯展示和工具类组件

#### 3. TypeScript严格模式
- **类型定义**: 完整的业务对象类型定义
- **接口约束**: 严格的API接口类型检查
- **编译时检查**: 消除运行时类型错误

## 技术选型对比

### 桌面框架选择: Tauri vs Electron
| 特性 | Tauri | Electron |
|------|-------|----------|
| 性能 | ✅ 原生性能 | ❌ 较重 |
| 安全性 | ✅ Rust内存安全 | ⚠️ 需额外配置 |
| 包体积 | ✅ <10MB | ❌ >100MB |
| 开发体验 | ✅ 现代化工具链 | ✅ 成熟生态 |
| 学习成本 | ⚠️ 需学习Rust | ✅ 纯Web技术 |

**选择Tauri的原因**: 性能优先，安全性要求高，包体积敏感

### 前端框架选择: React vs Vue vs Svelte
| 特性 | React | Vue | Svelte |
|------|-------|-----|--------|
| 生态成熟度 | ✅ 最丰富 | ✅ 良好 | ⚠️ 相对较新 |
| TypeScript支持 | ✅ 原生支持 | ✅ 良好支持 | ✅ 内置支持 |
| 学习曲线 | ⚠️ 中等 | ✅ 平缓 | ✅ 平缓 |
| 性能 | ✅ 良好 | ✅ 良好 | ✅ 优秀 |
| 开发体验 | ✅ 优秀 | ✅ 优秀 | ✅ 优秀 |

**选择React的原因**: 生态最成熟，TypeScript支持最好，团队熟悉度高

## 性能要求与约束

### 性能指标
- **UI响应时间**: <100ms
- **数据更新频率**: 实时推送，延迟<500ms
- **内存使用**: <200MB
- **CPU使用**: <10%（空闲时）

### 技术约束
- **平台兼容**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Python版本**: 3.8+
- **Node.js版本**: 18+
- **Rust版本**: 1.70+

### 依赖管理
- **前端依赖**: 通过Yarn管理，锁定版本确保一致性
- **Python依赖**: 通过uv管理，使用pyproject.toml定义
- **系统依赖**: 通过构建脚本自动检查和安装

## 数据流架构

### 实时数据流
```
富途/华盛API → Python适配器 → Sidecar通信 → Tauri后端 → React前端
```

### 用户操作流
```
React组件 → Tauri命令 → Rust处理 → Python适配器 → 交易平台API
```

### 状态同步机制
- **前端状态**: React useState/useReducer管理组件状态
- **全局状态**: 通过Context或自定义Hooks共享
- **后端状态**: Rust中维护应用级状态
- **数据持久化**: JSON配置文件存储用户设置

## 安全与可靠性

### 安全措施
- **API密钥管理**: 加密存储，不提交到版本控制
- **网络通信**: HTTPS/WSS加密传输
- **进程隔离**: Python适配器独立进程运行
- **权限控制**: Tauri严格限制系统API访问

### 错误处理策略
- **网络错误**: 自动重连机制，指数退避算法
- **API错误**: 详细错误日志，用户友好提示
- **系统错误**: 优雅降级，核心功能保持可用
- **数据错误**: 数据校验，防止脏数据传播

### 监控与调试
- **日志系统**: 分级日志记录，便于问题定位
- **性能监控**: 关键操作耗时统计
- **错误追踪**: 完整的错误堆栈信息
- **健康检查**: 定期检查各组件状态