# 需求文档：Electron 架构迁移方案

## 1. 需求概述

### 1.1 项目背景
当前系统采用 **Tauri + Python Sidecar** 架构：
- **Tauri (Rust)** 作为桌面应用框架
- **Python Sidecar** 处理富途/华盛通交易接口（因为没有 Rust SDK）
- 通过标准 I/O 进行进程间通信

### 1.2 迁移动机
- 用户熟悉 **TypeScript/JavaScript** 开发
- 数据接口和交易接口都有 **JavaScript SDK** 可用
- 希望简化架构，避免跨语言通信的复杂性
- 统一技术栈，提高开发效率

### 1.3 目标
- 将桌面应用框架从 **Tauri** 迁移到 **Electron**
- 直接使用 **JavaScript SDK** 替代 Python Sidecar
- 保持现有功能完整性和用户体验
- 提升系统可维护性和开发效率

## 2. 功能需求

### 2.1 核心功能保持
- **多任务管理**：支持多个独立的策略任务并行运行
- **实时数据监控**：接收并处理实时行情数据
- **自动化交易**：根据策略信号自动执行交易
- **风险控制**：多条件应急清仓机制
- **状态管理**：持仓状态持久化和恢复

### 2.2 接口迁移需求
- **富途牛牛接口**（行情数据）
  - 从 Python SDK 迁移到 JavaScript SDK
  - 保持实时数据推送能力
  - 支持 L2 深度报价、逐笔成交等数据类型
  
- **华盛通接口**（交易执行）
  - 从 Python SDK 迁移到 JavaScript SDK
  - 支持账户管理、订单执行、持仓查询
  - 保持交易执行的可靠性

### 2.3 架构改进需求
- **简化进程管理**：无需管理 Python Sidecar 进程
- **统一开发语言**：全栈使用 TypeScript
- **提升调试体验**：利用 Electron DevTools
- **优化打包流程**：简化应用分发

## 3. 用户故事

### 3.1 开发者体验
**作为开发者，我希望：**
- 使用熟悉的 TypeScript 进行全栈开发，减少语言切换
- 能够直接调试交易接口代码，无需跨进程调试
- 简化的构建和部署流程

**验收标准：**
- WHEN 开发新功能时，IF 需要调用交易接口，THEN 可以直接在 TypeScript 中调用
- WHEN 调试问题时，IF 涉及交易接口，THEN 可以在同一调试器中追踪
- WHEN 构建应用时，THEN 无需处理 Python 依赖打包

### 3.2 系统维护
**作为系统维护者，我希望：**
- 减少系统组件数量，降低复杂度
- 统一的错误处理和日志机制
- 更简单的版本管理和依赖更新

**验收标准：**
- WHEN 系统出现错误时，THEN 所有错误日志集中在一个地方
- WHEN 更新依赖时，THEN 只需要管理 JavaScript 生态的包
- WHEN 部署新版本时，THEN 构建产物是单一的 Electron 应用

### 3.3 用户功能
**作为交易用户，我希望：**
- 保持现有的所有交易功能
- 系统启动速度和响应速度不降低
- 数据实时性和交易执行可靠性不受影响

**验收标准：**
- WHEN 启动应用时，THEN 启动时间不超过 3 秒
- WHEN 执行交易时，THEN 订单执行延迟不超过 500ms
- WHEN 监控实时数据时，THEN 数据推送延迟不超过 100ms

## 4. 非功能性需求

### 4.1 性能要求
- **启动时间**：< 3 秒
- **内存占用**：< 300MB（Electron 基础开销）
- **CPU 占用**：空闲时 < 5%
- **数据延迟**：< 100ms

### 4.2 可靠性要求
- **自动重连**：网络断开后自动重连交易接口
- **状态恢复**：应用崩溃后能恢复任务状态
- **错误处理**：完善的错误捕获和用户提示

### 4.3 安全性要求
- **凭证管理**：API 密钥安全存储（使用 Electron safeStorage）
- **进程隔离**：渲染进程与主进程隔离
- **网络安全**：只连接可信的交易服务器

### 4.4 兼容性要求
- **操作系统**：支持 Windows 10+、macOS 10.15+
- **数据格式**：保持与现有系统的数据格式兼容
- **配置文件**：支持现有配置文件格式

## 5. 约束条件

### 5.1 技术约束
- 必须使用 **Electron 25+** 版本（支持最新安全特性）
- 必须使用 **TypeScript 5+** 进行开发
- 必须支持 **React 18+** 作为 UI 框架

### 5.2 接口约束
- 必须使用官方提供的 JavaScript SDK
- 必须遵循交易所的 API 使用限制
- 必须实现完整的错误处理机制

### 5.3 迁移约束
- 迁移过程中保持数据兼容性
- 提供配置迁移工具
- 支持并行运行（迁移期间）

## 6. 风险评估

### 6.1 技术风险
- **SDK 功能差异**：JavaScript SDK 可能与 Python SDK 存在功能差异
- **性能差异**：Electron 相比 Tauri 有更高的资源占用
- **打包体积**：Electron 应用体积较大

### 6.2 迁移风险
- **数据迁移**：确保历史数据正确迁移
- **功能回归**：确保所有功能正常工作
- **用户适应**：UI 保持一致，减少用户学习成本

## 7. 成功标准

### 7.1 功能完整性
- 所有现有功能在 Electron 版本中正常工作
- 实时数据推送稳定可靠
- 交易执行准确无误

### 7.2 性能指标
- 满足所有性能要求指标
- 用户体验流畅无卡顿
- 系统资源占用可接受

### 7.3 开发效率
- 新功能开发时间减少 30%
- 调试问题时间减少 50%
- 部署流程时间减少 40%

## 8. 边界条件

### 8.1 包含范围
- Electron 主进程和渲染进程架构
- JavaScript 交易 SDK 集成
- 现有 UI 组件迁移
- 配置管理系统
- 日志和错误处理系统

### 8.2 不包含范围
- UI 界面重新设计
- 新增交易策略功能
- 移动端支持
- Web 版本开发