# 设计文档：Electron 架构迁移方案

## 1. 架构概述

### 1.1 当前架构（Tauri + Python Sidecar）

```mermaid
graph TB
    subgraph "前端层"
        React[React UI]
        TC[Trading Client Base]
    end
    
    subgraph "Tauri 层"
        IPC[Tauri IPC]
        SC[Sidecar Client]
        PM[Process Manager]
    end
    
    subgraph "Python 层"
        PS[Python Sidecar]
        FA[富途适配器]
        HA[华盛适配器]
    end
    
    subgraph "外部服务"
        FT[富途服务器]
        HS[华盛服务器]
    end
    
    React --> TC
    TC --> IPC
    IPC --> SC
    SC --> PM
    PM --> PS
    PS --> FA
    PS --> HA
    FA --> FT
    HA --> HS
```

### 1.2 目标架构（Electron + JavaScript SDK）

```mermaid
graph TB
    subgraph "渲染进程"
        React[React UI]
        TC[Trading Client]
    end
    
    subgraph "主进程"
        IPC[IPC Bridge]
        TM[Trading Manager]
        FA[富途 JS SDK]
        HA[华盛 JS SDK]
    end
    
    subgraph "外部服务"
        FT[富途服务器]
        HS[华盛服务器]
    end
    
    React --> TC
    TC --> IPC
    IPC --> TM
    TM --> FA
    TM --> HA
    FA --> FT
    HA --> HS
```

### 1.3 架构改进点

1. **消除进程间通信复杂性**：直接在 Node.js 环境调用 SDK
2. **统一技术栈**：全栈 TypeScript
3. **简化调试**：单进程调试环境
4. **减少序列化开销**：无需 JSON 序列化/反序列化

## 2. 系统组件设计

### 2.1 Electron 主进程架构

```typescript
// 主进程模块结构
electron-main/
├── index.ts              // 主进程入口
├── ipc/                  // IPC 通信层
│   ├── handlers.ts       // IPC 处理器注册
│   ├── channels.ts       // IPC 通道定义
│   └── types.ts          // 通信类型定义
├── trading/              // 交易核心模块
│   ├── TradingManager.ts // 交易管理器
│   ├── adapters/         // SDK 适配器
│   │   ├── FutuAdapter.ts
│   │   └── HuashengAdapter.ts
│   └── interfaces.ts     // 交易接口定义
├── services/             // 系统服务
│   ├── ConfigService.ts  // 配置管理
│   ├── LogService.ts     // 日志服务
│   └── StateService.ts   // 状态持久化
└── utils/                // 工具函数
```

### 2.2 核心组件详细设计

#### 2.2.1 TradingManager（交易管理器）

```typescript
interface TradingManager {
    // 适配器管理
    registerAdapter(name: string, adapter: ITradingAdapter): void;
    getAdapter(name: string): ITradingAdapter;
    
    // 连接管理
    connect(adapterName: string, config: ConnectionConfig): Promise<void>;
    disconnect(adapterName: string): Promise<void>;
    
    // 数据获取
    getQuote(adapterName: string, stockCode: string): Promise<Quote>;
    getOrderBook(adapterName: string, stockCode: string): Promise<OrderBook>;
    
    // 实时数据订阅
    subscribe(adapterName: string, stockCode: string, types: DataType[]): Promise<void>;
    unsubscribe(adapterName: string, stockCode: string, types: DataType[]): Promise<void>;
    
    // 交易执行
    placeOrder(adapterName: string, order: OrderRequest): Promise<OrderResponse>;
    cancelOrder(adapterName: string, orderId: string): Promise<void>;
}
```

#### 2.2.2 交易适配器接口

```typescript
interface ITradingAdapter {
    // 连接管理
    connect(config: ConnectionConfig): Promise<void>;
    disconnect(): Promise<void>;
    isConnected(): boolean;
    
    // 行情数据
    getQuote(stockCode: string): Promise<Quote>;
    getOrderBook(stockCode: string): Promise<OrderBook>;
    getTicker(stockCode: string, count: number): Promise<Ticker[]>;
    getBrokerQueue(stockCode: string): Promise<BrokerQueue>;
    
    // 实时数据
    subscribe(stockCode: string, types: DataType[]): Promise<void>;
    unsubscribe(stockCode: string, types: DataType[]): Promise<void>;
    onData(callback: (data: RealtimeData) => void): void;
    
    // 交易功能
    getAccounts(): Promise<Account[]>;
    getPositions(accountId: string): Promise<Position[]>;
    placeOrder(order: OrderRequest): Promise<OrderResponse>;
    cancelOrder(orderId: string): Promise<void>;
    getOrders(filter: OrderFilter): Promise<Order[]>;
}
```

### 2.3 IPC 通信设计

#### 2.3.1 通道定义

```typescript
// IPC 通道枚举
enum IPCChannel {
    // 连接管理
    CONNECT = 'trading:connect',
    DISCONNECT = 'trading:disconnect',
    GET_STATUS = 'trading:getStatus',
    
    // 行情数据
    GET_QUOTE = 'market:getQuote',
    GET_ORDER_BOOK = 'market:getOrderBook',
    GET_TICKER = 'market:getTicker',
    
    // 实时订阅
    SUBSCRIBE = 'realtime:subscribe',
    UNSUBSCRIBE = 'realtime:unsubscribe',
    
    // 交易操作
    PLACE_ORDER = 'trade:placeOrder',
    CANCEL_ORDER = 'trade:cancelOrder',
    GET_POSITIONS = 'trade:getPositions',
    
    // 事件推送
    REALTIME_DATA = 'event:realtimeData',
    CONNECTION_STATUS = 'event:connectionStatus',
    ERROR = 'event:error'
}
```

#### 2.3.2 通信模式

```mermaid
sequenceDiagram
    participant R as 渲染进程
    participant M as 主进程
    participant SDK as Trading SDK
    participant S as 交易服务器
    
    R->>M: ipcRenderer.invoke('trading:connect')
    M->>SDK: adapter.connect()
    SDK->>S: 建立连接
    S-->>SDK: 连接成功
    SDK-->>M: 返回结果
    M-->>R: 返回连接状态
    
    R->>M: ipcRenderer.invoke('realtime:subscribe')
    M->>SDK: adapter.subscribe()
    SDK->>S: 订阅数据
    
    loop 实时数据推送
        S-->>SDK: 推送数据
        SDK-->>M: 数据回调
        M-->>R: ipcMain.send('event:realtimeData')
    end
```

### 2.4 数据流设计

#### 2.4.1 实时数据流

```typescript
// 主进程数据流处理
class RealtimeDataStream {
    private subscribers: Map<string, Set<string>> = new Map();
    
    // 处理实时数据
    handleRealtimeData(adapterName: string, data: RealtimeData) {
        // 数据验证
        if (!this.validateData(data)) return;
        
        // 数据转换
        const normalizedData = this.normalizeData(data);
        
        // 推送到渲染进程
        this.broadcast('event:realtimeData', {
            adapter: adapterName,
            data: normalizedData,
            timestamp: Date.now()
        });
        
        // 更新内部状态
        this.updateInternalState(normalizedData);
    }
    
    // 广播数据到所有窗口
    private broadcast(channel: string, data: any) {
        BrowserWindow.getAllWindows().forEach(window => {
            window.webContents.send(channel, data);
        });
    }
}
```

#### 2.4.2 状态管理

```mermaid
stateDiagram-v2
    [*] --> Disconnected
    Disconnected --> Connecting: connect()
    Connecting --> Connected: 连接成功
    Connecting --> Error: 连接失败
    Connected --> Disconnected: disconnect()
    Connected --> Error: 连接断开
    Error --> Connecting: 重连
    Error --> Disconnected: 放弃重连
```

### 2.5 前端层适配

#### 2.5.1 新的 Trading Client

```typescript
// 渲染进程的交易客户端
export class ElectronTradingClient implements ITradingClient {
    private listeners: Map<string, Function[]> = new Map();
    
    constructor(private adapterName: string) {
        this.setupEventListeners();
    }
    
    // 连接管理
    async connect(config?: any): Promise<ApiResponse> {
        return await ipcRenderer.invoke(IPCChannel.CONNECT, {
            adapter: this.adapterName,
            config
        });
    }
    
    // 获取实时数据
    async getQuote(stockCode: string): Promise<ApiResponse<Quote>> {
        return await ipcRenderer.invoke(IPCChannel.GET_QUOTE, {
            adapter: this.adapterName,
            stockCode
        });
    }
    
    // 订阅实时数据
    async subscribe(stockCode: string, types: DataType[]): Promise<ApiResponse> {
        return await ipcRenderer.invoke(IPCChannel.SUBSCRIBE, {
            adapter: this.adapterName,
            stockCode,
            types
        });
    }
    
    // 监听实时数据
    onRealtimeData(callback: (data: RealtimeData) => void) {
        ipcRenderer.on(IPCChannel.REALTIME_DATA, (event, payload) => {
            if (payload.adapter === this.adapterName) {
                callback(payload.data);
            }
        });
    }
}
```

### 2.6 安全设计

#### 2.6.1 进程隔离

```typescript
// preload.ts - 暴露安全的 API
import { contextBridge, ipcRenderer } from 'electron';

contextBridge.exposeInMainWorld('tradingAPI', {
    // 只暴露必要的方法
    connect: (adapter: string, config: any) => 
        ipcRenderer.invoke('trading:connect', { adapter, config }),
    
    getQuote: (adapter: string, stockCode: string) =>
        ipcRenderer.invoke('market:getQuote', { adapter, stockCode }),
    
    // 事件监听
    onRealtimeData: (callback: Function) => {
        ipcRenderer.on('event:realtimeData', (event, data) => callback(data));
    }
});
```

#### 2.6.2 权限控制

```typescript
// 主进程安全配置
const mainWindow = new BrowserWindow({
    webPreferences: {
        nodeIntegration: false,        // 禁用 Node.js 集成
        contextIsolation: true,        // 启用上下文隔离
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true              // 启用 web 安全
    }
});
```

### 2.7 配置管理

#### 2.7.1 配置结构

```typescript
interface AppConfig {
    // 通用配置
    general: {
        language: string;
        theme: 'light' | 'dark';
        autoStart: boolean;
    };
    
    // 交易配置
    trading: {
        futu: {
            host: string;
            port: number;
            encryptKey?: string;
        };
        huasheng: {
            apiUrl: string;
            accountId: string;
        };
    };
    
    // 策略配置
    strategies: StrategyConfig[];
}
```

#### 2.7.2 安全存储

```typescript
// 使用 Electron 的 safeStorage API
class SecureConfigService {
    // 存储加密配置
    async saveSecureConfig(key: string, value: any): Promise<void> {
        const encrypted = safeStorage.encryptString(JSON.stringify(value));
        await fs.writeFile(this.getConfigPath(key), encrypted);
    }
    
    // 读取加密配置
    async loadSecureConfig(key: string): Promise<any> {
        const encrypted = await fs.readFile(this.getConfigPath(key));
        const decrypted = safeStorage.decryptString(encrypted);
        return JSON.parse(decrypted);
    }
}
```

## 3. 迁移策略

### 3.1 渐进式迁移

```mermaid
graph LR
    A[现有 Tauri 应用] --> B[创建 Electron 框架]
    B --> C[迁移 UI 组件]
    C --> D[实现 Trading Manager]
    D --> E[集成 JS SDK]
    E --> F[功能测试]
    F --> G[性能优化]
    G --> H[完全迁移]
```

### 3.2 兼容性层设计

```typescript
// 兼容层 - 保持与现有代码的接口一致
export class TradingClientCompat extends TradingClientBase {
    private electronClient: ElectronTradingClient;
    
    constructor(adapterName: string) {
        super(adapterName);
        this.electronClient = new ElectronTradingClient(adapterName);
    }
    
    // 保持原有接口
    async sendCommand(action: string, params?: any): Promise<ApiResponse> {
        // 映射到新的 Electron IPC 调用
        switch(action) {
            case 'connect':
                return this.electronClient.connect(params);
            case 'get_quote':
                return this.electronClient.getQuote(params.stock_code);
            // ... 其他映射
        }
    }
}
```

## 4. 行情数据管理设计

### 4.1 多任务数据共享架构

```mermaid
graph TB
    subgraph "任务层"
        T1[任务1: 腾讯-策略A]
        T2[任务2: 腾讯-策略B]
        T3[任务3: 阿里-策略A]
    end
    
    subgraph "数据管理层"
        DM[MarketDataManager]
        SM[SubscriptionManager]
        DC[DataCache]
    end
    
    subgraph "适配器层"
        FA[富途适配器]
    end
    
    subgraph "外部服务"
        FS[富途服务器]
    end
    
    T1 --> DM
    T2 --> DM
    T3 --> DM
    DM --> SM
    SM --> DC
    SM --> FA
    FA --> FS
```

### 4.2 MarketDataManager（行情数据管理器）

```typescript
// 行情数据管理器 - 统一管理所有任务的数据订阅
class MarketDataManager {
    private subscriptions: Map<string, SubscriptionInfo> = new Map();
    private dataCache: MarketDataCache;
    private taskSubscriptions: Map<string, Set<string>> = new Map(); // taskId -> stockCodes
    
    // 任务订阅数据
    async subscribeForTask(taskId: string, stockCode: string, dataTypes: DataType[]): Promise<void> {
        // 记录任务订阅
        if (!this.taskSubscriptions.has(taskId)) {
            this.taskSubscriptions.set(taskId, new Set());
        }
        this.taskSubscriptions.get(taskId)!.add(stockCode);
        
        // 增加股票订阅引用计数
        const key = this.getSubscriptionKey(stockCode, dataTypes);
        const subscription = this.subscriptions.get(key);
        
        if (subscription) {
            // 已有订阅，增加引用计数
            subscription.refCount++;
            subscription.taskIds.add(taskId);
        } else {
            // 新建订阅
            await this.createSubscription(stockCode, dataTypes, taskId);
        }
    }
    
    // 任务取消订阅
    async unsubscribeForTask(taskId: string, stockCode: string, dataTypes: DataType[]): Promise<void> {
        const key = this.getSubscriptionKey(stockCode, dataTypes);
        const subscription = this.subscriptions.get(key);
        
        if (subscription) {
            subscription.refCount--;
            subscription.taskIds.delete(taskId);
            
            // 如果没有任务使用这个订阅，则取消订阅
            if (subscription.refCount === 0) {
                await this.removeSubscription(stockCode, dataTypes);
                this.subscriptions.delete(key);
            }
        }
        
        // 清理任务记录
        const taskStocks = this.taskSubscriptions.get(taskId);
        if (taskStocks) {
            taskStocks.delete(stockCode);
            if (taskStocks.size === 0) {
                this.taskSubscriptions.delete(taskId);
            }
        }
    }
    
    // 获取任务的实时数据
    getRealtimeDataForTask(taskId: string, stockCode: string): RealtimeData | null {
        return this.dataCache.getLatestData(stockCode);
    }
    
    // 处理实时数据推送
    private handleRealtimeData(stockCode: string, data: any) {
        // 更新缓存
        this.dataCache.updateData(stockCode, data);
        
        // 找出所有订阅这个股票的任务
        const tasksToNotify = new Set<string>();
        for (const [taskId, stocks] of this.taskSubscriptions.entries()) {
            if (stocks.has(stockCode)) {
                tasksToNotify.add(taskId);
            }
        }
        
        // 通知相关任务
        this.notifyTasks(tasksToNotify, stockCode, data);
    }
}

// 订阅信息
interface SubscriptionInfo {
    stockCode: string;
    dataTypes: DataType[];
    refCount: number;        // 引用计数
    taskIds: Set<string>;    // 使用此订阅的任务ID
    subscriptionId?: string; // SDK返回的订阅ID
}
```

### 4.3 富途牛牛 SDK 集成

```typescript
// 富途适配器 - 使用富途牛牛 JS SDK
import { FutuOpenAPI } from 'futu-api-js'; // 假设的SDK包名

class FutuMarketAdapter implements IMarketAdapter {
    private client: FutuOpenAPI;
    private isConnected: boolean = false;
    private dataCallbacks: Map<string, (data: any) => void> = new Map();
    
    async connect(config: FutuConfig): Promise<void> {
        this.client = new FutuOpenAPI({
            host: config.host || '127.0.0.1',
            port: config.port || 11111,
            encryptKey: config.encryptKey
        });
        
        // 连接OpenD
        await this.client.connect();
        
        // 设置推送回调
        this.client.onPush((data) => {
            this.handlePushData(data);
        });
        
        this.isConnected = true;
    }
    
    // 订阅实时数据
    async subscribe(stockCode: string, dataTypes: DataType[]): Promise<string> {
        const subTypes = this.mapDataTypes(dataTypes);
        
        // 调用富途SDK订阅
        const result = await this.client.subscribe({
            stockList: [stockCode],
            subTypeList: subTypes,
            isFirstPush: true
        });
        
        return result.subscriptionId;
    }
    
    // 获取实时报价
    async getQuote(stockCode: string): Promise<Quote> {
        const result = await this.client.getMarketSnapshot({
            stockList: [stockCode]
        });
        
        return this.transformQuote(result.snapshotList[0]);
    }
    
    // 获取买卖盘
    async getOrderBook(stockCode: string): Promise<OrderBook> {
        const result = await this.client.getOrderBook({
            stock: stockCode,
            num: 10 // 10档
        });
        
        return this.transformOrderBook(result);
    }
    
    // 获取逐笔成交
    async getTicker(stockCode: string, count: number): Promise<Ticker[]> {
        const result = await this.client.getTicker({
            stock: stockCode,
            maxRetNum: count
        });
        
        return result.tickerList.map(this.transformTicker);
    }
    
    // 获取经纪队列
    async getBrokerQueue(stockCode: string): Promise<BrokerQueue> {
        const result = await this.client.getBrokerQueue({
            stock: stockCode
        });
        
        return this.transformBrokerQueue(result);
    }
    
    // 处理推送数据
    private handlePushData(pushData: any) {
        switch (pushData.type) {
            case 'Quote':
                this.handleQuotePush(pushData);
                break;
            case 'Ticker':
                this.handleTickerPush(pushData);
                break;
            case 'OrderBook':
                this.handleOrderBookPush(pushData);
                break;
            case 'Broker':
                this.handleBrokerPush(pushData);
                break;
        }
    }
}
```

### 4.4 任务管理器设计

```typescript
// 任务管理器 - 管理所有策略任务
class TaskManager {
    private tasks: Map<string, StrategyTask> = new Map();
    private marketDataManager: MarketDataManager;
    private strategyEngine: StrategyEngine;
    
    // 创建新任务
    async createTask(config: TaskConfig): Promise<string> {
        const taskId = this.generateTaskId();
        
        // 创建策略实例
        const strategy = this.strategyEngine.createStrategy(config.strategyType, config.strategyParams);
        
        // 创建任务
        const task = new StrategyTask({
            id: taskId,
            stockCode: config.stockCode,
            strategy: strategy,
            enabled: false
        });
        
        this.tasks.set(taskId, task);
        
        // 订阅所需数据
        await this.marketDataManager.subscribeForTask(
            taskId,
            config.stockCode,
            strategy.getRequiredDataTypes()
        );
        
        return taskId;
    }
    
    // 启动任务
    async startTask(taskId: string): Promise<void> {
        const task = this.tasks.get(taskId);
        if (!task) throw new Error('Task not found');
        
        task.enabled = true;
        
        // 设置数据回调
        this.marketDataManager.onDataUpdate(taskId, (data) => {
            if (task.enabled) {
                this.processTaskData(task, data);
            }
        });
    }
    
    // 处理任务数据
    private async processTaskData(task: StrategyTask, data: RealtimeData) {
        try {
            // 运行策略
            const signal = await task.strategy.process(data);
            
            // 如果有交易信号
            if (signal) {
                await this.executeTrade(task, signal);
            }
        } catch (error) {
            this.handleTaskError(task, error);
        }
    }
}

// 策略任务
class StrategyTask {
    id: string;
    stockCode: string;
    strategy: IStrategy;
    enabled: boolean;
    position: Position | null = null;
    lastSignal: Signal | null = null;
    
    constructor(config: TaskConfig) {
        Object.assign(this, config);
    }
}
```

### 4.5 数据缓存策略

```typescript
// 市场数据缓存
class MarketDataCache {
    private cache: Map<string, StockDataCache> = new Map();
    private maxTickerCount: number = 1000; // 每只股票最多缓存1000条逐笔
    
    // 更新数据
    updateData(stockCode: string, data: any) {
        if (!this.cache.has(stockCode)) {
            this.cache.set(stockCode, new StockDataCache());
        }
        
        const stockCache = this.cache.get(stockCode)!;
        
        switch (data.type) {
            case 'Quote':
                stockCache.latestQuote = data;
                break;
            case 'OrderBook':
                stockCache.latestOrderBook = data;
                break;
            case 'Ticker':
                this.updateTicker(stockCache, data);
                break;
            case 'BrokerQueue':
                stockCache.latestBrokerQueue = data;
                break;
        }
        
        stockCache.lastUpdateTime = Date.now();
    }
    
    // 获取最新数据
    getLatestData(stockCode: string): RealtimeData | null {
        const stockCache = this.cache.get(stockCode);
        if (!stockCache) return null;
        
        return {
            stockCode,
            quote: stockCache.latestQuote,
            orderBook: stockCache.latestOrderBook,
            brokerQueue: stockCache.latestBrokerQueue,
            recentTickers: stockCache.tickers.slice(-20), // 最近20条
            timestamp: stockCache.lastUpdateTime
        };
    }
    
    // 更新逐笔数据
    private updateTicker(stockCache: StockDataCache, ticker: Ticker) {
        stockCache.tickers.push(ticker);
        
        // 限制缓存大小
        if (stockCache.tickers.length > this.maxTickerCount) {
            stockCache.tickers = stockCache.tickers.slice(-this.maxTickerCount);
        }
    }
}

// 单只股票的缓存数据
class StockDataCache {
    latestQuote: Quote | null = null;
    latestOrderBook: OrderBook | null = null;
    latestBrokerQueue: BrokerQueue | null = null;
    tickers: Ticker[] = [];
    lastUpdateTime: number = 0;
}
```

### 4.6 性能优化

```typescript
// 批量数据请求优化
class BatchDataFetcher {
    private batchQueue: Map<string, Set<string>> = new Map(); // dataType -> stockCodes
    private batchTimer: NodeJS.Timeout | null = null;
    private batchDelay: number = 50; // 50ms批量延迟
    
    // 添加到批量请求队列
    async fetchQuote(stockCode: string): Promise<Quote> {
        return new Promise((resolve, reject) => {
            this.addToBatch('quote', stockCode, { resolve, reject });
        });
    }
    
    // 添加到批量队列
    private addToBatch(dataType: string, stockCode: string, callbacks: any) {
        if (!this.batchQueue.has(dataType)) {
            this.batchQueue.set(dataType, new Set());
        }
        
        this.batchQueue.get(dataType)!.add(stockCode);
        this.pendingCallbacks.set(`${dataType}:${stockCode}`, callbacks);
        
        // 启动批量定时器
        if (!this.batchTimer) {
            this.batchTimer = setTimeout(() => this.processBatch(), this.batchDelay);
        }
    }
    
    // 处理批量请求
    private async processBatch() {
        const batches = new Map(this.batchQueue);
        this.batchQueue.clear();
        this.batchTimer = null;
        
        // 处理每种数据类型的批量请求
        for (const [dataType, stockCodes] of batches) {
            try {
                const results = await this.executeBatchRequest(dataType, Array.from(stockCodes));
                this.resolveBatchResults(dataType, results);
            } catch (error) {
                this.rejectBatchResults(dataType, error);
            }
        }
    }
}
```

## 5. 测试策略

### 5.1 单元测试

```typescript
// Trading Manager 单元测试
describe('TradingManager', () => {
    let manager: TradingManager;
    let mockAdapter: jest.Mocked<ITradingAdapter>;
    
    beforeEach(() => {
        mockAdapter = createMockAdapter();
        manager = new TradingManager();
        manager.registerAdapter('test', mockAdapter);
    });
    
    test('should connect to adapter', async () => {
        await manager.connect('test', { host: 'localhost' });
        expect(mockAdapter.connect).toHaveBeenCalled();
    });
    
    test('should handle connection errors', async () => {
        mockAdapter.connect.mockRejectedValue(new Error('Connection failed'));
        await expect(manager.connect('test', {})).rejects.toThrow();
    });
});
```

### 5.2 集成测试

```typescript
// E2E 测试
describe('Trading Integration', () => {
    let app: Application;
    
    beforeAll(async () => {
        app = await electron.launch({
            args: ['path/to/app']
        });
    });
    
    test('complete trading flow', async () => {
        // 连接
        await app.evaluate(async () => {
            return window.tradingAPI.connect('futu', {});
        });
        
        // 获取报价
        const quote = await app.evaluate(async () => {
            return window.tradingAPI.getQuote('futu', 'HK.00700');
        });
        
        expect(quote.success).toBe(true);
        expect(quote.data.price).toBeGreaterThan(0);
    });
});
```

## 6. 部署架构

### 6.1 构建配置

```javascript
// electron-builder.config.js
module.exports = {
    appId: 'com.quanttrading.terminal',
    productName: '量化交易终端',
    directories: {
        output: 'dist-electron'
    },
    files: [
        'dist/**/*',
        'electron-main/**/*',
        'node_modules/**/*'
    ],
    mac: {
        category: 'public.app-category.finance',
        target: ['dmg', 'zip']
    },
    win: {
        target: ['nsis', 'zip']
    },
    nsis: {
        oneClick: false,
        allowToChangeInstallationDirectory: true
    }
};
```

### 6.2 自动更新

```typescript
// 自动更新服务
class AutoUpdateService {
    constructor(private app: Electron.App) {
        autoUpdater.logger = log;
        autoUpdater.autoDownload = false;
        
        this.setupEventHandlers();
    }
    
    private setupEventHandlers() {
        autoUpdater.on('update-available', (info) => {
            this.notifyUpdateAvailable(info);
        });
        
        autoUpdater.on('update-downloaded', () => {
            this.notifyUpdateReady();
        });
    }
    
    async checkForUpdates() {
        if (!app.isPackaged) return;
        
        try {
            await autoUpdater.checkForUpdates();
        } catch (error) {
            log.error('Update check failed:', error);
        }
    }
}
```

## 7. 监控和日志

### 7.1 日志系统

```typescript
// 统一日志服务
class LogService {
    private logger: winston.Logger;
    
    constructor() {
        this.logger = winston.createLogger({
            level: 'info',
            format: winston.format.json(),
            transports: [
                new winston.transports.File({ 
                    filename: 'error.log', 
                    level: 'error' 
                }),
                new winston.transports.File({ 
                    filename: 'combined.log' 
                })
            ]
        });
    }
    
    // 记录交易日志
    logTrade(action: string, details: any) {
        this.logger.info('Trade Action', {
            action,
            details,
            timestamp: new Date().toISOString()
        });
    }
}
```

### 7.2 性能监控

```typescript
// 性能监控
class PerformanceMonitor {
    private metrics: Map<string, Metric> = new Map();
    
    startTimer(operation: string): () => void {
        const start = performance.now();
        
        return () => {
            const duration = performance.now() - start;
            this.recordMetric(operation, duration);
        };
    }
    
    recordMetric(operation: string, duration: number) {
        const metric = this.metrics.get(operation) || {
            count: 0,
            total: 0,
            min: Infinity,
            max: -Infinity
        };
        
        metric.count++;
        metric.total += duration;
        metric.min = Math.min(metric.min, duration);
        metric.max = Math.max(metric.max, duration);
        
        this.metrics.set(operation, metric);
    }
}
```

## 8. 错误处理

### 8.1 全局错误处理

```typescript
// 主进程错误处理
class ErrorHandler {
    setupHandlers() {
        // 未捕获的异常
        process.on('uncaughtException', (error) => {
            logger.error('Uncaught Exception:', error);
            this.notifyUser('系统错误', error.message);
        });
        
        // Promise 拒绝
        process.on('unhandledRejection', (reason, promise) => {
            logger.error('Unhandled Rejection:', reason);
        });
        
        // IPC 错误处理
        ipcMain.handle('*', async (event, ...args) => {
            try {
                return await this.handleIPC(event, ...args);
            } catch (error) {
                logger.error('IPC Error:', error);
                return { success: false, error: error.message };
            }
        });
    }
}
```

### 8.2 用户友好的错误提示

```typescript
// 错误分类和用户提示
enum ErrorType {
    NETWORK = 'NETWORK',
    AUTH = 'AUTH',
    TRADING = 'TRADING',
    SYSTEM = 'SYSTEM'
}

class UserErrorHandler {
    getErrorMessage(error: any): string {
        const errorType = this.classifyError(error);
        
        switch (errorType) {
            case ErrorType.NETWORK:
                return '网络连接失败，请检查网络设置';
            case ErrorType.AUTH:
                return '认证失败，请检查账户凭证';
            case ErrorType.TRADING:
                return '交易执行失败：' + error.details;
            default:
                return '系统错误，请联系技术支持';
        }
    }
}
```

## 9. 技术决策

### 9.1 选择 Electron 的理由

1. **统一技术栈**：全栈 TypeScript/JavaScript
2. **丰富的生态系统**：大量成熟的 npm 包可用
3. **原生能力**：完整的 Node.js API 访问
4. **开发工具**：Chrome DevTools 调试支持
5. **社区支持**：活跃的社区和丰富的文档

### 9.2 关键技术选型

| 组件 | 技术选择 | 理由 |
|------|---------|------|
| IPC 通信 | Electron IPC | 原生支持，类型安全 |
| 状态管理 | MobX/Zustand | 简单高效，适合实时数据 |
| 日志系统 | Winston | 功能完整，支持多种传输 |
| 数据存储 | SQLite/LevelDB | 轻量级，嵌入式 |
| 配置管理 | electron-store | 专为 Electron 设计 |
| 自动更新 | electron-updater | 官方推荐方案 |