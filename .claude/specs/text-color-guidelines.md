# 文字颜色显示规范 - 避免白字可见性问题

## 🚨 问题背景

在项目开发过程中，**白色文字与浅色背景相近导致肉眼无法辨认的问题已经反复出现多次**，包括但不限于：

1. TaskDetailsModal 基本信息区域
2. 策略配置参数显示
3. 实时数据组件（TickFeed、BrokerQueue）
4. 任务日志弹出框
5. 风控配置参数

这种问题严重影响用户体验，必须建立明确的规范来彻底避免。

## 📋 强制性颜色规范

### 1. 禁止使用的颜色组合

❌ **绝对禁止**以下颜色组合：
- 白色文字 (`#ffffff`, `white`) + 白色背景
- 白色文字 + 浅灰背景 (`#f8f9fa`, `#f1f3f4` 等)
- 浅灰文字 (`#f8f9fa`) + 白色背景
- 任何对比度 < 4.5:1 的文字背景组合

### 2. 强制使用 `!important` 声明

对于所有文字颜色，**必须使用 `!important` 声明**来确保样式不会被意外覆盖：

```css
/* ✅ 正确：明确的颜色声明 + !important */
.text-element {
    color: #495057 !important;
}

/* ❌ 错误：没有 !important，可能被覆盖 */
.text-element {
    color: #495057;
}

/* ❌ 错误：没有明确颜色，依赖继承 */
.text-element {
    /* 没有 color 属性 */
}
```

### 3. 标准颜色模板

#### 主要文字颜色
```css
/* 主要文字（标题、重要内容） */
color: #495057 !important;

/* 次要文字（描述、说明） */
color: #6c757d !important;

/* 辅助文字（时间戳、ID等） */
color: #868e96 !important;

/* 禁用状态文字 */
color: #adb5bd !important;
```

#### 状态文字颜色
```css
/* 成功/盈利状态 */
color: #155724 !important;  /* 深绿 */

/* 警告状态 */
color: #856404 !important;  /* 深黄 */

/* 错误/亏损状态 */
color: #721c24 !important;  /* 深红 */

/* 信息状态 */
color: #0c5460 !important;  /* 深青 */
```

#### 链接文字颜色
```css
/* 普通链接 */
color: #007bff !important;

/* 链接悬停 */
color: #0056b3 !important;

/* 访问过的链接 */
color: #6610f2 !important;
```

## 🔍 开发检查清单

### 每次添加文字元素时必须检查：

- [ ] **1. 明确颜色声明**：每个包含文字的元素都必须有明确的 `color` 属性
- [ ] **2. !important 声明**：所有 `color` 属性都必须使用 `!important`
- [ ] **3. 对比度检查**：确保文字与背景的对比度 ≥ 4.5:1
- [ ] **4. 多场景测试**：在不同背景色下测试文字可见性
- [ ] **5. 继承避免**：不依赖颜色继承，每个组件明确定义

### CSS 编写模板

```css
/* 组件样式模板 */
.component-name {
    /* 背景色 */
    background: #f8f9fa;
    
    /* 边框等其他样式 */
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
}

/* 文字元素样式 - 必须明确声明颜色 */
.component-name .title {
    color: #495057 !important;  /* 主要文字 */
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 10px 0;
}

.component-name .description {
    color: #6c757d !important;  /* 次要文字 */
    font-size: 14px;
    line-height: 1.5;
}

.component-name .meta-info {
    color: #868e96 !important;  /* 辅助文字 */
    font-size: 12px;
}

/* 链接样式 */
.component-name .link {
    color: #007bff !important;
    text-decoration: none;
}

.component-name .link:hover {
    color: #0056b3 !important;
    text-decoration: underline;
}
```

## 🧪 测试要求

### 1. 开发时测试

每个组件开发完成后必须进行以下测试：

1. **白色背景测试**：在纯白背景 (`#ffffff`) 上测试所有文字
2. **浅灰背景测试**：在浅灰背景 (`#f8f9fa`) 上测试所有文字  
3. **深色背景测试**：在深色背景上测试浅色文字
4. **不同屏幕测试**：在不同设备和浏览器上测试

### 2. 对比度检查工具

推荐使用以下工具检查颜色对比度：

- Chrome DevTools：Lighthouse 可访问性检查
- WebAIM Contrast Checker：https://webaim.org/resources/contrastchecker/
- Colour Contrast Analyser (CCA)
- WAVE Web Accessibility Evaluation Tool

### 3. 自动化检查

```javascript
// CSS 样式检查脚本示例
function checkColorContrast(elementSelector) {
    const element = document.querySelector(elementSelector);
    const styles = window.getComputedStyle(element);
    const color = styles.color;
    const backgroundColor = styles.backgroundColor;
    
    // 检查是否有明确的颜色声明
    if (color === 'rgb(255, 255, 255)' || color === '#ffffff') {
        console.warn(`⚠️ 潜在白字问题: ${elementSelector}`, {
            color,
            backgroundColor
        });
    }
}
```

## 📝 组件开发模板

### React 组件颜色样式模板

```tsx
// ComponentName.tsx
import React from 'react';
import './ComponentName.css';

interface ComponentNameProps {
    title: string;
    description?: string;
    status?: 'success' | 'warning' | 'error' | 'info';
}

export const ComponentName: React.FC<ComponentNameProps> = ({
    title,
    description,
    status = 'info'
}) => {
    return (
        <div className="component-container">
            <h3 className="component-title">{title}</h3>
            {description && (
                <p className="component-description">{description}</p>
            )}
            <div className={`component-status status-${status}`}>
                状态信息
            </div>
        </div>
    );
};
```

```css
/* ComponentName.css */
.component-container {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
}

.component-title {
    color: #495057 !important;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 10px 0;
}

.component-description {
    color: #6c757d !important;
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 15px 0;
}

.component-status {
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
}

.status-success {
    background: #d4edda;
    color: #155724 !important;
    border: 1px solid #c3e6cb;
}

.status-warning {
    background: #fff3cd;
    color: #856404 !important;
    border: 1px solid #ffeaa7;
}

.status-error {
    background: #f8d7da;
    color: #721c24 !important;
    border: 1px solid #f5c6cb;
}

.status-info {
    background: #d1ecf1;
    color: #0c5460 !important;
    border: 1px solid #bee5eb;
}
```

## 🚀 实施要求

### 1. 立即执行

- **所有现有组件**必须按此规范检查和修复
- **所有新增组件**必须严格遵循此规范
- **Code Review** 时必须检查颜色对比度问题

### 2. 团队规范

- 每个 PR 必须通过颜色对比度检查
- 设计师提供设计稿时必须标注具体颜色值
- 测试时必须包含可访问性测试

### 3. 文档更新

此规范必须：
- 添加到项目 README
- 在团队会议中宣讲
- 作为新人 onboarding 的必读文档

## 📊 问题追踪

### 已发现和修复的白字问题记录

| 组件/页面 | 问题描述 | 修复状态 | 修复日期 |
|-----------|----------|----------|----------|
| TaskDetailsModal 基本信息 | 白色文字，白色背景 | ✅ 已修复 | 2025-07-29 |
| TaskDetailsModal 策略参数 | 参数值颜色过浅 | ✅ 已修复 | 2025-07-29 |
| TickFeed 组件 | 表格文字颜色未定义 | ✅ 已修复 | 2025-07-29 |
| BrokerQueue 组件 | 队列信息文字颜色问题 | ✅ 已修复 | 2025-07-29 |
| LogSystem 组件 | 日志弹出框多处白字 | ✅ 已修复 | 2025-07-29 |

---

## ⚠️ 重要提醒

**这不是建议，而是强制性规范！**

白字可见性问题已经反复出现，严重影响用户体验。每个开发者都必须：

1. 🔴 **禁止**依赖颜色继承
2. 🟡 **必须**使用 `!important` 声明文字颜色  
3. 🟢 **必须**测试所有背景下的文字可见性
4. 🔵 **必须**使用标准颜色模板

违反此规范的代码将被拒绝合并！