# Design Document

## Overview

The task card more menu enhancement will provide a complete implementation for the "⋯" dropdown menu in TaskCard components, enabling users to perform task management operations including edit, liquidate, delete, copy, and view logs. This design leverages existing UI components and patterns while maintaining consistency with the established architecture.

## Steering Document Alignment

### Technical Standards (tech.md)
- **React Hooks**: Utilizes custom hooks (useDashboard) for state management, following the established pattern
- **TypeScript**: Maintains strict typing throughout all new interfaces and components
- **Component Architecture**: Follows the modular component structure with proper separation of concerns
- **Error Handling**: Implements the project's error handling strategy with user-friendly messages
- **Performance**: Uses React.memo and useCallback where appropriate to prevent unnecessary re-renders

### Project Structure (structure.md)
- **Component Organization**: New components follow the src/components/Dashboard/ structure
- **Modal Components**: Follows existing modal patterns (TaskConfigModal, TaskDetailsModal)
- **Hook Placement**: Task-related logic remains in useDashboard hook
- **Naming Conventions**: PascalCase for components, camelCase for functions and variables

## Code Reuse Analysis

### Existing Components to Leverage
1. **DropdownMenu Component**: Already integrated in TaskCard with full UI implementation
2. **ConfirmDialog Component**: Provides consistent confirmation UI with animation support
3. **TaskConfigModal**: Can be reused for the edit functionality with minor adjustments
4. **LogSystem Component**: Provides log rendering capabilities for the log viewer
5. **Modal Patterns**: TaskDetailsModal provides the template for new modal components

### Existing Hooks and Services
1. **useDashboard Hook**: Contains task management logic (editTask, deleteTask, liquidateTask)
2. **Animation System**: Provides consistent animations for modals and transitions
3. **Button Styles**: getButtonClasses utility for consistent button styling

### Patterns to Follow
1. **Modal Structure**: Header with title and close button, body with content, footer with actions
2. **Confirmation Flow**: Using ConfirmDialog for dangerous operations
3. **Loading States**: Overlay pattern with spinner during async operations
4. **Error Handling**: Try-catch blocks with user-friendly error messages

## Architecture

```mermaid
graph TD
    A[TaskCard Component] --> B[DropdownMenu]
    B --> C{Menu Actions}
    C --> D[Edit Action]
    C --> E[Liquidate Action]
    C --> F[Delete Action]
    C --> G[Copy Action]
    C --> H[View Logs Action]
    
    D --> I[TaskConfigModal]
    E --> J[ConfirmDialog]
    F --> K[ConfirmDialog]
    G --> L[TaskConfigModal<br/>Pre-filled]
    H --> M[TaskLogModal<br/>New Component]
    
    J --> N[useDashboard.liquidateTask]
    K --> O[useDashboard.deleteTask]
    I --> P[useDashboard.saveTaskConfig]
    L --> Q[useDashboard.addTask]
    
    M --> R[Task Log Service]
    R --> S[Mock Log Data]
```

## Components and Interfaces

### Component 1: Enhanced TaskCard Menu Handlers
- **Purpose:** Complete the menu action implementations in TaskCard component
- **Interfaces:** Uses existing TaskCardProps interface
- **Dependencies:** ConfirmDialog, useDashboard hook
- **Reuses:** Existing DropdownMenu integration, loading state pattern

### Component 2: TaskLogModal (New)
- **Purpose:** Display task execution logs with filtering capabilities
- **Interfaces:** 
  ```typescript
  interface TaskLogModalProps {
    task: Task;
    onClose: () => void;
  }
  ```
- **Dependencies:** LogSystem component, AnimatedComponent
- **Reuses:** Modal structure from TaskDetailsModal, LogSystem for rendering

### Component 3: Enhanced useDashboard Hook
- **Purpose:** Add copyTask and getTaskLogs functions
- **Interfaces:** Extends existing UseDashboardReturn interface
- **Dependencies:** Existing task state management
- **Reuses:** Current task update patterns, mock data generation approach

### Component 4: Confirmation Dialogs Integration
- **Purpose:** Replace window.confirm with ConfirmDialog component
- **Interfaces:** Uses existing ConfirmDialog props
- **Dependencies:** ConfirmDialog component, proper async handling
- **Reuses:** Existing confirmation patterns from BrokerManagerModal

## Data Models

### Task Log Extension
```typescript
interface TaskLog extends LogEntry {
    taskId: string;
    taskName: string;
    timestamp: Date;
    level: LogLevel;
    category: LogCategory;
    message: string;
    details?: Record<string, any>;
}
```

### Copy Task Configuration
```typescript
interface CopyTaskConfig {
    sourceTask: Task;
    newName: string;
    preserveSettings: boolean;
}
```

## Error Handling

### Edit Task Errors
- **Running Task**: "请先暂停任务后再进行编辑"
- **Task Not Found**: "任务不存在或已被删除"

### Liquidate Task Errors
- **Not Running**: "只有运行中的任务可以清仓"
- **No Position**: "该任务没有持仓"
- **API Failure**: "清仓失败，请重试"

### Delete Task Errors
- **Running Task**: "无法删除运行中的任务，请先停止任务"
- **Has Position**: Warning in confirmation dialog

### Copy Task Errors
- **Invalid Source**: "源任务配置无效"
- **Duplicate Name**: "任务名称已存在"

### Log View Errors
- **No Logs**: Empty state with appropriate message
- **Load Failure**: "加载日志失败，请重试"

## Testing Strategy

### Unit Tests
1. **Menu Action Tests**: Verify each menu action triggers correct handler
2. **Confirmation Flow Tests**: Ensure proper confirmation before dangerous operations
3. **State Update Tests**: Verify task state updates correctly after operations
4. **Error Handling Tests**: Test all error scenarios

### Integration Tests
1. **Modal Flow Tests**: Test complete flow from menu click to modal interaction
2. **Copy Task Flow**: Verify task duplication with correct data
3. **Log Filtering**: Test log level and category filtering

### E2E Tests
1. **Complete Task Lifecycle**: Create, edit, run, liquidate, delete
2. **Multi-task Operations**: Test operations with multiple tasks
3. **Error Recovery**: Verify system recovers gracefully from errors

### Existing Test Utilities
- React Testing Library patterns already in use
- Mock data generators in useDashboard
- Animation test helpers in AnimationSystem