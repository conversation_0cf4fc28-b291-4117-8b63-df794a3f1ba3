# Implementation Plan

## Task Overview
The task card more menu feature will enhance the existing DropdownMenu integration in TaskCard components by implementing the complete action handlers (edit, liquidate, delete, copy, view logs). The implementation will leverage existing components and patterns while maintaining consistency with the project's "弹出框风格" UI design and following the established architectural patterns.

## Steering Document Compliance
- **structure.md**: Tasks follow the component organization in `src/components/Dashboard/`, utilize existing modals, and maintain naming conventions
- **tech.md**: Implementation uses React Hooks (useDashboard), TypeScript strict mode, proper error handling patterns, and leverages existing UI components

## Tasks

- [ ] 1. Implement Edit Task functionality
  - Complete the onEdit handler in TaskCard component
  - Add running task validation check before opening modal
  - Wire up TaskConfigModal with existing task data
  - Implement saveTaskConfig function in useDashboard hook
  - Add proper error handling for save operations
  - _Requirements: 1.1, 1.2, 1.3, 1.4, 1.5_
  - _Leverage: src/components/Dashboard/TaskConfigModal.tsx, src/hooks/useDashboard.ts_

- [ ] 2. Enhance Liquidate Task functionality with proper confirmations
  - Replace window.confirm with ConfirmDialog component
  - Add validation for running status and position checks
  - Implement liquidateTask function with proper error handling
  - Update task status to "已清仓" after successful liquidation
  - Add loading overlay during liquidation process
  - Display success/error notifications
  - _Requirements: 2.1, 2.2, 2.3, 2.4, 2.5_
  - _Leverage: src/components/ConfirmDialog.tsx, src/components/LoadingOverlay.tsx_

- [ ] 3. Implement Delete Task functionality with confirmations
  - Integrate ConfirmDialog for delete confirmation
  - Add running task validation before allowing deletion
  - Add position warning in confirmation dialog if applicable
  - Implement deleteTask function with state cleanup
  - Update global statistics after deletion
  - _Requirements: 3.1, 3.2, 3.3, 3.4, 3.5_
  - _Leverage: src/components/ConfirmDialog.tsx, src/hooks/useDashboard.ts_

- [ ] 4. Add Copy Task functionality
  - Add "复制" menu item to DropdownMenu configuration
  - Implement copyTask function in useDashboard hook
  - Open TaskConfigModal with pre-filled data from source task
  - Generate unique task name (append "_副本")
  - Add new task to task list after confirmation
  - _Requirements: 4.1, 4.2, 4.3, 4.4_
  - _Leverage: src/components/Dashboard/TaskConfigModal.tsx, existing addTask logic_

- [ ] 5. Create TaskLogModal component
  - Create new modal component following TaskDetailsModal structure
  - Implement log display using LogSystem component
  - Add log level filtering (信息/警告/错误)
  - Add pagination or scroll loading for large log sets
  - Style according to "弹出框风格" design system
  - _Requirements: 5.1, 5.2, 5.3, 5.4_
  - _Leverage: src/components/Dashboard/TaskDetailsModal.tsx, src/components/LogSystem.tsx_

- [ ] 5.1 Add View Logs functionality
  - Add "查看日志" menu item to DropdownMenu configuration
  - Implement getTaskLogs mock function in useDashboard
  - Connect menu action to open TaskLogModal
  - Display appropriate empty state when no logs exist
  - _Requirements: 5.1, 5.2_
  - _Leverage: existing modal patterns, mock data generation approach_

- [ ] 6. Update menu action handlers and state management
  - Update TaskCard to handle all new menu actions
  - Add proper loading states during async operations
  - Implement error boundaries for graceful error handling
  - Ensure menu items are properly disabled based on task state
  - _Requirements: All functional requirements_
  - _Leverage: existing loading state patterns, error handling utilities_

- [ ] 7. Write unit tests for new functionality
  - Test edit flow with running task validation
  - Test liquidation confirmation and state updates
  - Test delete operation with various task states
  - Test copy functionality with name generation
  - Test log modal display and filtering
  - Mock Tauri commands for testing
  - _Requirements: Testing strategy from design document_
  - _Leverage: existing test patterns, React Testing Library setup_

- [ ] 8. Integration testing and UI polish
  - Test complete menu action flows end-to-end
  - Verify modal animations and transitions
  - Ensure consistent styling with "弹出框风格"
  - Test error scenarios and recovery
  - Verify performance requirements (<100ms menu response)
  - _Requirements: Non-functional requirements_
  - _Leverage: existing AnimationSystem, UI design guidelines_