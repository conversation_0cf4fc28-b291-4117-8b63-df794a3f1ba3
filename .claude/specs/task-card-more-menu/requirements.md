# 任务卡片更多菜单功能需求

## 功能概述

为首页策略任务卡片的"更多"按钮（⋯）添加完整的功能实现，提供便捷的任务管理操作入口。

## 背景与问题

### 现状分析
- **现有实现**：TaskCard组件中已经集成了DropdownMenu组件，并定义了编辑、清仓、删除等菜单项
- **可复用组件**：DropdownMenu组件提供了完整的下拉菜单UI实现，支持图标、禁用状态、危险操作样式等特性
- **问题定位**：菜单项的action回调已经定义，但对应的处理函数（onEdit、onLiquidate、onDelete）可能未完整实现

### 业务价值
- 提升用户操作效率，将低频但重要的操作收纳到更多菜单中
- 保持界面简洁，避免操作按钮过多造成的视觉混乱
- 符合产品定位中"多任务并行"和"灵活操作模式"的核心价值

## 需求详述

### 需求1：编辑任务功能
**用户故事**：作为一个交易者，我想要编辑已创建的任务配置，以便调整策略参数或风控设置

#### 接受标准
1. WHEN 用户点击"编辑"菜单项 THEN 系统 SHALL 打开任务配置弹窗
2. IF 任务正在运行中 THEN 系统 SHALL 提示用户需要先暂停任务
3. WHEN 弹窗打开时 THEN 系统 SHALL 加载当前任务的所有配置信息
4. IF 用户修改配置并确认 THEN 系统 SHALL 保存更新并刷新任务卡片显示
5. IF 用户取消编辑 THEN 系统 SHALL 保持原有配置不变

### 需求2：清仓功能增强
**用户故事**：作为一个交易者，我想要快速清仓任务的所有持仓，以便在紧急情况下及时止损

#### 接受标准
1. WHEN 用户点击"清仓"菜单项 THEN 系统 SHALL 显示二次确认对话框
2. IF 任务没有持仓 THEN 系统 SHALL 禁用清仓选项
3. IF 任务状态不是"运行中" THEN 系统 SHALL 禁用清仓选项并显示提示
4. WHEN 用户确认清仓 THEN 系统 SHALL 执行以下操作：
   - 调用交易接口平仓所有持仓
   - 更新任务状态为"已清仓" 
   - 记录清仓时间和价格
   - 显示清仓结果通知
5. IF 清仓失败 THEN 系统 SHALL 显示错误信息并保持任务继续运行

### 需求3：删除任务功能
**用户故事**：作为一个交易者，我想要删除不再需要的任务，以便保持任务列表的整洁

#### 接受标准
1. WHEN 用户点击"删除"菜单项 THEN 系统 SHALL 显示确认对话框
2. IF 任务正在运行中 THEN 系统 SHALL 禁用删除选项并提示需要先停止任务
3. IF 任务有持仓 THEN 系统 SHALL 在确认对话框中警告用户
4. WHEN 用户确认删除 THEN 系统 SHALL：
   - 从任务列表中移除该任务
   - 清理相关的本地数据
   - 更新全局统计信息
5. IF 删除失败 THEN 系统 SHALL 显示错误信息并保持任务存在

### 需求4：复制任务功能（新增）
**用户故事**：作为一个交易者，我想要基于现有任务快速创建相似的新任务，以便节省配置时间

#### 接受标准
1. WHEN 用户点击"复制"菜单项 THEN 系统 SHALL 打开新建任务弹窗
2. WHEN 弹窗打开时 THEN 系统 SHALL 预填充源任务的所有配置（除任务名称）
3. IF 用户确认创建 THEN 系统 SHALL 创建新任务并添加到任务列表
4. WHEN 复制完成 THEN 系统 SHALL 定位到新创建的任务卡片

### 需求5：查看日志功能（新增）
**用户故事**：作为一个交易者，我想要查看任务的运行日志，以便了解策略执行情况和排查问题

#### 接受标准
1. WHEN 用户点击"查看日志"菜单项 THEN 系统 SHALL 打开日志查看弹窗
2. WHEN 弹窗打开时 THEN 系统 SHALL 显示最近的日志记录
3. IF 日志内容过多 THEN 系统 SHALL 提供分页或滚动加载
4. WHEN 查看日志时 THEN 用户 SHALL 能够按级别筛选（信息/警告/错误）

## 非功能需求

### 性能要求
- 菜单响应时间 < 100ms
- 弹窗打开时间 < 200ms
- 清仓操作完成时间 < 3秒

### 用户体验要求
- 所有危险操作必须有二次确认
- 操作结果必须有明确的反馈（成功/失败提示）
- 禁用状态必须有清晰的视觉区分和原因说明
- 遵循项目"弹出框风格"UI设计规范

### 安全性要求
- 清仓操作必须验证用户权限
- 删除操作必须检查任务状态
- 所有操作必须记录审计日志

## 技术约束

### 现有基础设施
- **前端框架**：React 18 + TypeScript
- **UI组件**：已有DropdownMenu组件
- **通信层**：Tauri命令系统
- **状态管理**：React Hooks (useDashboard)

### 集成要求
- 复用现有的TaskConfigModal组件进行编辑
- 通过Tauri命令调用后端API
- 保持与现有代码风格一致
- 遵循项目的错误处理模式

## 优先级排序

1. **P0（必须）**：编辑、删除功能（已有UI，需完善功能）
2. **P1（应该）**：清仓功能增强（完善错误处理和状态更新）
3. **P2（可选）**：复制任务、查看日志功能（提升用户体验）

## 风险与依赖

### 技术风险
- 清仓操作可能因网络或API问题失败，需要完善的错误处理和重试机制
- 并发操作可能导致状态不一致，需要适当的锁机制

### 依赖项
- 后端API需要支持相应的操作接口
- 交易平台API的清仓功能可用性
- 任务配置的持久化存储机制