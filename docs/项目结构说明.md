# 项目结构说明

## 📁 完整项目结构

```
stock/                            # 项目根目录
├── 🌐 前端 (React + TypeScript)
│   └── src/
│       ├── 📄 App.tsx                    # 应用入口组件
│       ├── 🎨 App.css                    # 应用样式
│       ├── 🚀 main.tsx                   # React 应用启动文件
│       ├── 🎨 styles.css                 # 全局样式
│       ├── 📡 communication/             # 通信模块
│       │   ├── index.ts                  # 统一导出
│       │   ├── tradingClient.ts          # 交易客户端基类
│       │   ├── futuClient.ts             # 富途客户端
│       │   └── huashengClient.ts         # 华盛客户端
│       ├── 🧩 components/                # 可复用组件
│       │   ├── index.ts                  # 组件统一导出
│       │   ├── AdapterStatus.tsx         # 适配器状态显示
│       │   ├── ControlPanel.tsx          # 控制面板
│       │   ├── DataQueryPanel.tsx        # 数据查询面板
│       │   ├── DataDisplay.tsx           # 数据显示组件
│       │   ├── RealtimeDataStream.tsx    # 实时数据流
│       │   └── LoadingOverlay.tsx        # 加载遮罩
│       ├── 🪝 hooks/                     # 自定义 Hooks
│       │   ├── index.ts                  # Hooks 统一导出
│       │   └── useTradingDemo.ts         # 交易演示逻辑
│       ├── 📄 pages/                     # 页面组件
│       │   └── TradingDemo.tsx           # 交易演示页面
│       ├── 🏷️ types/                     # 类型定义
│       │   └── index.ts                  # 类型统一导出
│       └── 🛠️ utils/                     # 工具函数
│           └── index.ts                  # 工具函数导出
├── 🦀 后端 (Tauri + Rust)
│   └── src-tauri/src/
│       ├── 📄 main.rs                    # Tauri 应用入口
│       ├── 🔧 commands.rs                # Tauri 命令定义
│       ├── 🚀 sidecar_manager.rs         # Sidecar 进程管理
│       ├── 📡 communication/             # 通信模块
│       │   ├── mod.rs                    # 模块导出
│       │   ├── message_types.rs          # 消息类型定义
│       │   ├── sidecar_client.rs         # Sidecar 客户端
│       │   └── trading_client.rs         # 交易客户端
│       └── 🏷️ types/                     # 类型定义
│           └── mod.rs                    # 类型模块
├── 🐍 Python 适配器
│   └── src-python/adapters/
│       ├── 🔧 config_manager.py          # 配置管理
│       ├── 📡 tauri_communication.py     # 通信基础类
│       ├── 📈 futu_adapter_enhanced.py   # 富途适配器
│       └── 💰 huasheng_adapter_enhanced.py # 华盛适配器
└── 📚 文档
    └── docs/
        ├── 📋 量化交易终端完整设计与规格说明书.md
        ├── 🏗️ 项目结构说明.md
        ├── 📡 Tauri_Sidecar_通信方案完整指南.md
        ├── 🔗 富途牛牛与华盛通接口职责说明.md
        ├── 📝 开发日志.md
        ├── 🔧 故障排除指南.md
        ├── 🚀 构建部署指南.md
        └── 📖 文档索引.md
```

## 🏗️ 架构设计原则

### 1. **分层架构**

-   **页面层 (Pages)**: 路由级别的页面组件
-   **组件层 (Components)**: 可复用的 UI 组件
-   **逻辑层 (Hooks)**: 业务逻辑和状态管理
-   **通信层 (Communication)**: 与后端的通信封装
-   **工具层 (Utils)**: 通用工具函数

### 2. **模块化设计**

-   每个模块都有独立的 `index.ts` 文件统一导出
-   组件职责单一，便于测试和维护
-   通过 Hooks 分离业务逻辑和 UI 逻辑

### 3. **类型安全**

-   完整的 TypeScript 类型定义
-   统一的类型导出和管理
-   接口和类型的复用

## 📦 组件设计

### **AdapterStatus** - 适配器状态

```tsx
<AdapterStatus futuConnected={boolean} huashengConnected={boolean} />
```

### **ControlPanel** - 控制面板

```tsx
<ControlPanel
    futuConnected={boolean}
    huashengConnected={boolean}
    loading={boolean}
    onStartFutu={() => void}
    onStartHuasheng={() => void}
    onConnectFutu={() => void}
    onConnectHuasheng={() => void}
/>
```

### **DataQueryPanel** - 数据查询

```tsx
<DataQueryPanel
    futuConnected={boolean}
    huashengConnected={boolean}
    loading={boolean}
    onGetFutuQuote={() => void}
    onGetHuashengFunds={() => void}
    onSubscribeFutuRealtime={() => void}
    onPlaceFutuOrder={() => void}
/>
```

### **DataDisplay** - 数据显示

```tsx
<DataDisplay quote={any} funds={any} />
```

### **RealtimeDataStream** - 实时数据流

```tsx
<RealtimeDataStream
    realtimeData={RealtimeDataEvent[]}
/>
```

### **LoadingOverlay** - 加载遮罩

```tsx
<LoadingOverlay loading={boolean} />
```

## 🪝 Hooks 设计

### **useTradingDemo**

```tsx
const {
    // 状态
    futuConnected,
    huashengConnected,
    loading,
    quote,
    funds,
    realtimeData,

    // 操作函数
    startFutuAdapter,
    startHuashengAdapter,
    connectFutu,
    connectHuasheng,
    getFutuQuote,
    getHuashengFunds,
    subscribeFutuRealtime,
    placeFutuOrder
} = useTradingDemo();
```

## 🔄 数据流

```
用户操作 → Hook 函数 → 通信客户端 → Tauri 后端 → Python 适配器
    ↓
UI 更新 ← Hook 状态 ← 事件监听 ← Tauri 事件 ← Python 推送
```

## 🎯 扩展指南

### **添加新页面**

1. 在 `src/pages/` 创建新的页面组件
2. 在 `src/App.tsx` 中添加路由（如果需要）

### **添加新组件**

1. 在 `src/components/` 创建新组件
2. 在 `src/components/index.ts` 中导出
3. 编写 Props 接口，确保类型安全

### **添加新 Hook**

1. 在 `src/hooks/` 创建新 Hook
2. 在 `src/hooks/index.ts` 中导出
3. 遵循 React Hooks 规则

### **添加新工具函数**

1. 在 `src/utils/` 中添加函数
2. 在 `src/utils/index.ts` 中导出
3. 编写 JSDoc 注释

### **添加新类型**

1. 在 `src/types/` 中定义类型
2. 在 `src/types/index.ts` 中导出
3. 确保类型的复用性

## 🧪 测试建议

### **组件测试**

-   使用 React Testing Library
-   测试组件的渲染和交互
-   Mock 外部依赖

### **Hook 测试**

-   使用 @testing-library/react-hooks
-   测试状态变化和副作用
-   Mock API 调用

### **工具函数测试**

-   使用 Jest
-   测试边界情况
-   确保函数的纯净性

## 📝 代码规范

### **命名规范**

-   组件：PascalCase (如 `AdapterStatus`)
-   Hook：camelCase，以 `use` 开头 (如 `useTradingDemo`)
-   函数：camelCase (如 `formatTimestamp`)
-   常量：UPPER_SNAKE_CASE (如 `API_BASE_URL`)

### **文件组织**

-   每个组件一个文件
-   相关的类型定义放在同一文件
-   使用 `index.ts` 统一导出

### **导入顺序**

1. React 相关
2. 第三方库
3. 内部模块（按层级顺序）
4. 相对路径导入

这种结构确保了代码的可维护性、可扩展性和团队协作的效率。
