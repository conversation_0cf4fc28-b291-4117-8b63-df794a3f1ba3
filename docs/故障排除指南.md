# 🛠️ 故障排除指南

本文档提供XX交易终端常见问题的诊断和解决方案。

## 📋 目录

- [快速诊断](#快速诊断)  
- [常见问题](#常见问题)
- [调试工具](#调试工具)
- [获取支持](#获取支持)

## 🔍 快速诊断

遇到任何问题，首先尝试：

```bash
# 清理并重新构建
rm -rf src-tauri/target/release/bundle/
./scripts/build-production.sh
```

## 🚨 常见问题

### 1. 环境依赖问题

**症状**: 构建脚本运行失败，提示缺少工具

**解决方案**:
```bash
# 检查必需工具
node --version  # 需要 18+
rustc --version # 需要 1.70+
uv --version    # Python 包管理器

# 如果缺少 UV
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. 权限问题

**症状**: 构建脚本无法执行或复制文件失败

**解决方案**:
```bash
# 确保构建脚本有执行权限
chmod +x scripts/build-production.sh

# 清理可能的权限问题
sudo chown -R $USER:$USER src-tauri/target/
```

### 3. 缓存问题

**症状**: 构建结果不符合预期

**解决方案**:
```bash
# 完全清理构建缓存
rm -rf src-tauri/target/
rm -rf dist/
rm -rf node_modules/.cache/

# 重新构建
yarn install
./scripts/build-production.sh
```

### 4. 网络问题

**症状**: 下载依赖失败或速度慢

**解决方案**:
```bash
# 配置 Cargo 镜像源（国内用户）
mkdir -p ~/.cargo
cat > ~/.cargo/config.toml << 'EOF'
[source.crates-io]
replace-with = 'ustc'

[registries.ustc]
index = "https://mirrors.ustc.edu.cn/crates.io-index"

[net]
git-fetch-with-cli = true
EOF

# 配置 Yarn 镜像源
yarn config set registry https://registry.npmmirror.com
```

### 5. Python 服务无法启动

**症状**: 应用启动但服务显示未启动状态

**解决方案**:
```bash
# 检查 uv 是否正确安装
which uv
uv --version

# 手动测试 Python 服务
cd src-python
uv run python test_services/counter_service.py
```

## 🛠️ 调试工具

### 自动测试脚本

```bash
# 运行应用功能测试
./testing/test_packaged_app.py "src-tauri/target/release/bundle/macos/XX交易终端.app"
```

### 日志查看

```bash
# 查看系统日志
log show --last 30s | grep -E "XX交易终端|counter-service"

# 运行应用并查看日志
./testing/automated-tests/monitor-app-logs.sh
```

### 手动验证

```bash
# 检查应用结构
APP_PATH="src-tauri/target/release/bundle/macos/XX交易终端.app"

# 验证应用包完整性
ls -la "$APP_PATH/Contents/MacOS/"
ls -la "$APP_PATH/Contents/Resources/"

# 测试脚本可执行性
"$APP_PATH/Contents/Resources/counter-service" --help 2>/dev/null && echo "✅ 脚本可执行" || echo "❌ 脚本不可执行"
```

## 📞 获取支持

如果问题仍未解决：

1. **查看详细文档**:
   - [构建部署指南](构建部署指南.md) - 完整技术细节
   - [开发日志](开发日志.md) - 历史问题记录

2. **收集诊断信息**:
   ```bash
   # 系统信息
   uname -a
   node --version
   rustc --version
   uv --version
   
   # 项目状态
   git status
   ls -la src-tauri/target/release/bundle/
   ```

3. **联系技术支持**: 
   - 提供完整的错误日志
   - 描述具体的操作步骤
   - 包含系统环境信息

---

📅 **最后更新**: 2025-07-22  
💡 **建议**: 优先使用 `./scripts/build-production.sh` 进行构建