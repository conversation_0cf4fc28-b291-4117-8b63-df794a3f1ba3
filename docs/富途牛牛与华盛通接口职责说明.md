# 富途牛牛与华盛通接口职责说明

## 📋 接口职责分工

本文档详细说明富途牛牛和华盛通在交易平台中的接口职责分工和功能范围。

### 🎯 核心分工原则

-   **富途牛牛**: 专注于**行情数据服务**，提供实时市场数据
-   **华盛通**: 专注于**交易执行服务**，提供完整的交易功能

---

## 📈 富途牛牛 - 行情数据服务

### 🔧 职责范围

富途牛牛作为**行情数据提供商**，负责所有市场数据的获取和推送。

### 📊 提供的数据类型

#### 1. 买卖盘数据 (Order Book)

-   **10 档买卖盘**: 实时买卖价格和数量
-   **委托笔数**: 每个价位的委托订单数量
-   **数据更新**: 毫秒级实时更新

```json
{
    "bid_list": [
        { "price": 349.9, "volume": 1000, "orders": 5 },
        { "price": 349.8, "volume": 2000, "orders": 8 }
    ],
    "ask_list": [
        { "price": 350.1, "volume": 1500, "orders": 3 },
        { "price": 350.2, "volume": 1200, "orders": 6 }
    ]
}
```

#### 2. 逐笔交易数据 (Ticker)

-   **成交明细**: 每笔交易的价格、数量、时间
-   **买卖方向**: 主动买入/卖出标识
-   **交易类型**: 自动撮合、大宗交易等

```json
{
    "tickers": [
        {
            "time": "14:30:15.123",
            "price": 350.0,
            "volume": 1000,
            "direction": "买盘",
            "trade_type": "自动撮合"
        }
    ]
}
```

#### 3. 买卖盘经纪数据 (Broker Queue)

-   **经纪队列**: 买卖盘背后的经纪商信息
-   **经纪编号**: 券商的唯一标识
-   **经纪名称**: 券商的中文名称

```json
{
    "bid_brokers": [{ "broker_id": "B001", "broker_name": "中信证券" }],
    "ask_brokers": [{ "broker_id": "S001", "broker_name": "海通证券" }]
}
```

#### 4. 基本行情数据 (Quote)

-   **股价信息**: 最新价、涨跌幅、成交量
-   **市场状态**: 开盘、收盘、暂停交易等
-   **统计数据**: 成交额、换手率等

### 🔄 数据获取模式

#### 1. 订阅推送模式 (主要方式)

-   **实时推送**: 富途服务器主动推送数据变化
-   **无调用限制**: 不受 API 调用次数限制
-   **低延迟**: 毫秒级数据更新
-   **持续连接**: 保持长连接，数据流式传输

```python
# 订阅股票行情
await futu_adapter.subscribe_stock("HK.00700", ["quote", "ticker", "orderbook", "broker"])

# 数据会通过回调函数实时推送
def on_data_update(data_type, stock_code, data):
    print(f"收到 {stock_code} 的 {data_type} 数据更新")
```

#### 2. 主动查询模式 (辅助方式)

-   **按需查询**: 主动请求特定数据
-   **有调用限制**: 受富途 API 频率限制
-   **适用场景**: 历史数据、一次性查询

```python
# 主动查询行情数据
quote_data = await futu_adapter.get_quote_data("HK.00700")
ticker_data = await futu_adapter.get_ticker_data("HK.00700", count=20)
```

### ⚠️ 功能限制

-   ❌ **无交易功能**: 不提供任何交易相关接口
-   ❌ **无资金查询**: 不涉及账户资金信息
-   ❌ **无持仓查询**: 不涉及持仓信息
-   ✅ **纯行情服务**: 专注于市场数据提供

---

## 💼 华盛通 - 交易执行服务

### 🔧 职责范围

华盛通作为**交易执行平台**，负责所有交易相关的操作和查询。

### 💰 提供的功能模块

#### 1. 资金管理

-   **资金查询** (`QueryFunds`): 查询账户可用资金
-   **资金明细**: 资金变动记录和详情
-   **币种支持**: 港币、美元等多币种

```json
{
    "available_funds": 50000.0,
    "currency": "HKD",
    "frozen_funds": 5000.0,
    "total_assets": 55000.0
}
```

#### 2. 持仓管理

-   **持仓查询** (`QueryPosition`): 查询当前持仓
-   **持仓明细**: 股票代码、数量、成本价
-   **盈亏统计**: 浮动盈亏、持仓市值

```json
{
    "positions": [
        {
            "stock_code": "00700",
            "stock_name": "腾讯控股",
            "quantity": 1000,
            "avg_cost": 340.0,
            "market_value": 350000.0,
            "unrealized_pnl": 10000.0
        }
    ]
}
```

#### 3. 交易执行

-   **下单** (`PlaceOrder`): 买入/卖出股票
    -   买卖方向: `Side.Buy` / `Side.Sell`
    -   订单类型: `OrderType.EnhanceLimited` (增强限价单)
    -   价格数量: 自定义委托价格和数量

```python
# 下单示例
await huasheng_adapter.place_order(
    stock_code="00700",
    side=Side.Buy,           # 买入
    price=350.0,             # 委托价格
    quantity=1000,           # 委托数量
    order_type=OrderType.EnhanceLimited
)
```

-   **撤单** (`UnOrder`): 撤销未成交委托
    -   根据委托编号撤销订单
    -   支持部分撤单和全部撤单

#### 4. 委托管理

-   **委托查询** (`QueryEntrust`): 查询所有委托记录
-   **分页查询** (`QueryEntrustByPageSize`): 分页查询委托
-   **委托状态**: 已报、部成、已成、已撤等

```json
{
    "entrusts": [
        {
            "entrust_no": "E20250720001",
            "stock_code": "00700",
            "side": "买入",
            "entrust_price": 350.0,
            "entrust_qty": 1000,
            "deal_qty": 500,
            "status": "部分成交"
        }
    ]
}
```

#### 5. 高级功能

-   **ProL2 弹窗** (`ProL2Popup`): 专业 Level2 行情弹窗
    -   集成行情显示和快速下单
    -   支持预设价格和数量
    -   一键下单功能

### 🔄 数据交互模式

#### 1. 请求-响应模式

-   **同步调用**: 发送请求，等待响应
-   **异步回调**: 支持异步回调处理
-   **错误处理**: 完整的错误码和错误信息

#### 2. 推送通知模式

-   **委托状态推送**: 委托状态变化实时通知
-   **成交回报推送**: 成交信息实时推送
-   **资金变动推送**: 资金变化实时通知

### 🔐 安全机制

-   **Token 认证**: 所有接口需要 Token 验证
-   **账号密码**: 与华盛 Pro 客户端共享认证
-   **本地连接**: 仅支持本地 127.0.0.1 连接
-   **端口固定**: 使用固定端口 8080 通信

---

## 🔄 协同工作模式

### 📊 数据流向设计

```
┌─────────────────┐    行情数据     ┌──────────────────┐
│   富途牛牛服务器  │ ──────────────→ │   Python Sidecar │
└─────────────────┘    实时推送     └──────────────────┘
                                            │
                                            │ 整合数据
                                            ↓
┌─────────────────┐    交易指令     ┌──────────────────┐
│   华盛通服务器   │ ←──────────────  │   Tauri 前端     │
└─────────────────┘    执行交易     └──────────────────┘
```

### 🎯 使用场景示例

#### 场景 1: 查看实时行情

1. 前端请求订阅股票行情
2. Python Sidecar 调用富途接口订阅
3. 富途服务器实时推送行情数据
4. 数据通过 Sidecar 转发到前端显示

#### 场景 2: 执行交易操作

1. 用户在前端查看富途行情数据
2. 决定交易后点击买入/卖出按钮
3. 前端发送交易指令到 Python Sidecar
4. Sidecar 调用华盛接口执行交易
5. 交易结果返回前端显示

#### 场景 3: 查询账户信息

1. 前端请求查询资金/持仓
2. Python Sidecar 调用华盛查询接口
3. 华盛返回账户数据
4. 数据展示在前端界面

### ⚡ 性能优化策略

#### 富途行情优化

-   **批量订阅**: 一次订阅多只股票
-   **数据缓存**: 本地缓存最新行情数据
-   **增量更新**: 只推送变化的数据字段

#### 华盛交易优化

-   **连接复用**: 保持长连接，避免频繁连接
-   **异步处理**: 使用异步回调处理交易结果
-   **错误重试**: 自动重试失败的交易请求

---

## 📋 接口对比总结

| 功能类别     | 富途牛牛     | 华盛通      |
| ------------ | ------------ | ----------- |
| **行情数据** | ✅ 实时推送  | ❌ 不提供   |
| **买卖盘**   | ✅ 10 档数据 | ❌ 不提供   |
| **逐笔交易** | ✅ 毫秒级    | ❌ 不提供   |
| **经纪队列** | ✅ 完整信息  | ❌ 不提供   |
| **资金查询** | ❌ 不提供    | ✅ 完整功能 |
| **持仓查询** | ❌ 不提供    | ✅ 完整功能 |
| **下单交易** | ❌ 不提供    | ✅ 完整功能 |
| **撤单操作** | ❌ 不提供    | ✅ 完整功能 |
| **委托查询** | ❌ 不提供    | ✅ 完整功能 |

### 🎯 设计优势

1. **职责清晰**: 行情和交易功能完全分离
2. **性能优化**: 各自专注于擅长的领域
3. **风险隔离**: 行情服务故障不影响交易功能
4. **扩展性强**: 可以独立升级和维护各个模块
5. **合规安全**: 符合金融行业的分离原则

这种设计确保了系统的稳定性、安全性和可维护性，为用户提供了专业的交易体验。
