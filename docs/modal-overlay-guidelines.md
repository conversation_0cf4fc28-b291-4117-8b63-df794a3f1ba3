# 弹框遮罩层交互规范

## 问题描述

在弹框组件中，遮罩层的点击关闭逻辑存在用户体验问题：
- **错误行为**：用户在弹框内拖拽选择文字时，如果鼠标在遮罩层松开，会意外关闭弹框
- **正确行为**：只有在遮罩层上完整点击（mousedown + mouseup 都在遮罩层）才应该关闭弹框

## 解决方案

### 1. 统一 Hook：`useModalOverlay`

项目提供了统一的遮罩层点击处理 Hook，位于 `src/hooks/useModalOverlay.ts`：

```typescript
import { useSimpleModalOverlay } from '@/hooks/useModalOverlay';

// 在组件中使用
const { handleOverlayClick } = useSimpleModalOverlay(onClose);

// 应用到遮罩层
<div className="modal-overlay" onClick={handleOverlayClick}>
  <div className="modal-content" onClick={e => e.stopPropagation()}>
    {/* 弹框内容 */}
  </div>
</div>
```

### 2. Hook 变体

#### `useSimpleModalOverlay` (推荐)
- 使用 `e.target === e.currentTarget` 检查
- 适用于大多数场景
- 简单可靠

#### `useModalOverlay` (高级)
- 同时检查 mousedown 和 click 事件
- 更严格的点击检测
- 适用于复杂交互场景，特别是防止拖拽选择文字时意外关闭
- 使用方式：
  ```typescript
  const { handleOverlayClick, handleOverlayMouseDown } = useModalOverlay(onClose);
  <div
    className="modal-overlay"
    onMouseDown={handleOverlayMouseDown}
    onClick={handleOverlayClick}
  >
  ```

#### `useConditionalModalOverlay` (条件)
- 支持条件检查阻止关闭
- 适用于有子弹框的场景

### 3. 实现原理

```typescript
const handleOverlayClick = useCallback((e: React.MouseEvent) => {
  // 只有点击遮罩层本身才关闭弹框
  if (e.target === e.currentTarget) {
    onClose();
  }
}, [onClose]);
```

**关键点**：
- `e.target`：实际被点击的元素
- `e.currentTarget`：绑定事件处理器的元素（遮罩层）
- 只有两者相等时，才是直接点击遮罩层

## 已修复的组件

### ✅ 正确实现的组件：
1. **BrokerManagerModal.tsx** - 使用 `e.target === e.currentTarget` 检查
2. **TaskConfigModal.tsx** - 使用条件检查和遮罩层检测
3. **ConfirmDialog.tsx** - 使用 `e.target === e.currentTarget` 检查
4. **TaskLogModal.tsx** - 使用 `e.target === e.currentTarget` 检查

### ✅ 已修复的组件：
1. **MarketConfigModal.tsx** - 已更新使用 `useSimpleModalOverlay`
2. **TradingConfigModal.tsx** - 已更新使用 `useSimpleModalOverlay`
3. **KeyboardInteractions.tsx** - 已更新使用 `useSimpleModalOverlay`
4. **TaskDetailsModal.tsx** - 已更新使用 `useSimpleModalOverlay`
5. **TaskConfigModal.tsx** - 已更新使用 `useModalOverlay`（更严格的检查，防止拖拽选择时意外关闭）

## 开发规范

### 新建弹框组件时：

1. **导入 Hook**：
   ```typescript
   import { useSimpleModalOverlay } from '@/hooks/useModalOverlay';
   ```

2. **使用 Hook**：
   ```typescript
   const { handleOverlayClick } = useSimpleModalOverlay(onClose);
   ```

3. **应用到遮罩层**：
   ```typescript
   <div className="modal-overlay" onClick={handleOverlayClick}>
   ```

4. **阻止事件冒泡**：
   ```typescript
   <div className="modal-content" onClick={e => e.stopPropagation()}>
   ```

### 错误示例（避免）：

```typescript
// ❌ 错误：直接使用 onClose
<div className="modal-overlay" onClick={onClose}>

// ❌ 错误：没有检查点击目标
<div className="modal-overlay" onClick={() => onClose()}>
```

### 正确示例：

```typescript
// ✅ 正确：使用统一 Hook
const { handleOverlayClick } = useSimpleModalOverlay(onClose);
<div className="modal-overlay" onClick={handleOverlayClick}>
```

## 测试方法

1. **基本点击测试**：
   - 点击遮罩层 → 弹框应该关闭
   - 点击弹框内容 → 弹框不应该关闭

2. **拖拽测试**：
   - 在弹框内开始拖拽选择文字
   - 拖拽到遮罩层并松开鼠标
   - 弹框不应该关闭

3. **边界测试**：
   - 在弹框边缘点击
   - 确保行为符合预期

## 注意事项

1. **事件冒泡**：弹框内容必须使用 `onClick={e => e.stopPropagation()}` 阻止事件冒泡
2. **子弹框**：如果有子弹框，使用 `useConditionalModalOverlay` 并提供条件检查函数
3. **键盘支持**：记得添加 ESC 键关闭支持
4. **无障碍性**：添加适当的 ARIA 属性

## 相关文件

- `src/hooks/useModalOverlay.ts` - 统一 Hook 实现
- `src/components/Dashboard/` - 各种弹框组件
- `docs/modal-overlay-guidelines.md` - 本规范文档
