# 《量化交易终端完整设计与规格说明书》

#### **文档介绍**

本文件是为"自定义策略量化交易终端"项目编写的综合性设计与规格说明书。旨在作为项目从开发、测试到后期维护的核心指导蓝图，确保所有工作都围绕统一的目标和规格进行。

---

#### **第一章：需求背景与目标**

##### **1.1 项目愿景与目标 (Project Vision & Goals)**

-   **项目背景 (Why)**: 我拥有一套或多套基于“特定券商分点在买卖盘口的挂单行为”及“逐笔成交明细”的私有交易策略。当前依赖手动盯盘执行港股交易，此方式存在执行延迟、情绪干扰、无法同时监控多只股票等痛点。
-   **项目目标 (What to achieve)**:
    1.  **策略自动化**: 开发一个桌面端交易终端，100%复现并自动化执行我的核心交易策略。
    2.  **多任务并行**: 实现多任务管理，允许为多只不同的港股配置并独立运行策略任务。
    3.  **智能化风控**: 建立一套可编程、多条件的应急风控引擎，能够基于价格、时间、乃至特定市场行为进行清仓，并支持可配置的清仓执行算法。
    4.  **解放生产力**: 通过稳定可靠的自动化系统，将我从重复性的盯盘工作中解放出来，聚焦于策略研发和优化，同时提升交易纪律性、执行速度和整体效能。

##### **1.2 用户画像 (User Persona)**

-   **用户**: 我自己。
-   **交易经验**: 经验丰富的**港股**交易者，精通技术分析，并对基于“券商分点经纪商队列”的筹码分析有深入研究和独到见解。
-   **技术能力**: 具备专业的软件开发能力，技术栈涵盖 **Rust、Python、Tauri 框架及 Web 前端**，能够独立完成本终端的设计、开发、测试与后期维护。
-   **风险偏好**: 风险偏好中等偏上，愿意为策略信号承担必要的市场风险，但对风险控制有极高的要求，需要明确、可靠且自动化的“最终安全网” (Safety Net)。
-   **使用场景**: 主要在**港股交易时段（连续交易时段 09:30-12:00, 13:00-16:00）**于桌面电脑上运行本软件，进行实盘交易的自动化执行与监控。

---

#### **第二章：UI/UX 核心设计 (详细版)**

本章详细定义了应用的用户界面布局、核心组件和交互流程，旨在提供清晰、高效、低噪音的用户体验。

##### **2.1 设计理念**

-   **状态优先**: 系统的核心状态（连接、策略、持仓）必须拥有最高的可视化优先级。
-   **数据为王**: 界面布局围绕策略最关心的核心数据展开，做到一目了然。
-   **减少噪音**: 只显示与决策相关的关键信息，屏蔽无关干扰。
-   **控制在手**: 自动化是核心，但手动干预和紧急停止的权限必须直接、可靠。

##### **2.2 主界面：多任务仪表盘 (Dashboard)**

应用启动后的主窗口，采用**卡片式布局 (Card-based Layout)**，是所有任务的指挥中心。

-   **全局控制区 (位于顶部)**:

    -   `[+ 添加新任务]` 按钮: 启动“任务配置”流程的入口。
    -   `[▶ 全部启动]` / `[❚❚ 全部暂停]` 按钮: 一键控制所有任务。
    -   **全局状态概览**: 显示 `总任务数`、`总持仓市值`、`总浮动盈亏`。
    -   **连接状态**: `行情: ●` `交易: ●` (绿/黄/红指示灯)。

-   **任务卡片区 (主体区域)**:
    -   以网格形式展示所有独立的策略任务卡片。

##### **2.3 核心组件：任务卡片 (Task Card)**

每一张卡片都是一个独立的任务单元，展示最关键的摘要信息。

-   **顶部 - 任务标识**:
    -   股票名称与代码: `腾讯控股 0700.HK`
    -   应用的策略名称: `策略A: 大单监控`
-   **中部 - 核心状态**:
    -   **运行状态**: `● 运行中` (醒目的状态指示灯与文本)。
    -   **持仓信息**: `持仓: 200 股` 或 `状态: 空仓`。
    -   **实时盈亏**: `浮动盈亏: + HK$ 3,450.10` (实时跳动并根据正负变色)。
-   **底部 - 关键操作**:
    -   `[▶ 启动 / ❚❚ 暂停]` 切换按钮: 控制此单个任务。
    -   `[详细信息]` 按钮: 打开此任务的“独立详细信息窗口”。
    -   `[编辑]` 按钮: 打开“任务配置窗口”并载入当前参数。
    -   `[删除]` 按钮: 从仪表盘移除此任务（需二次确认）。

##### **2.4 交互窗口 1：任务配置窗口 (详细)**

通过点击`[+ 添加新任务]`或`[编辑]`按钮弹出，采用**标签页 (Tabs)** 设计，确保配置流程清晰。

-   **标签页 1: 基本信息与策略**

    -   `目标股票`: 输入框，用于填写股票代码，支持实时校验。
    -   `任务名称`: 自定义名称，方便用户识别，如“腾讯大单策略”。
    -   `选择策略`: **下拉菜单**，列出所有预先在系统中定义好的策略模板 (例如: `策略A: 大单监控`, `策略B: 突破追涨`)。

-   **标签页 2: 策略条件配置**

    -   **此区域内容根据在“标签页 1”中选择的策略动态生成**，用于配置该策略的触发参数。
        > **例 1：若选择 `策略A: 大单监控`**
        >
        > -   `监控买盘阈值(股): [ ]`
        > -   `持续时间(秒): [ ]`
        > -   `目标经纪商列表: [ ]` (可多选的经纪商输入框)
        > -   `单笔买入股数: [ ]`
        >
        > **例 2：若选择 `策略B: 突破追涨`**
        >
        > -   `突破周期(日): [ ]`
        > -   `成交量放大倍数: [ ]`
        > -   `回踩幅度(%): [ ]`

-   **标签页 3: 高级应急风控配置 (详细)**

    -   **此区域用于构建复杂、多条件的应急清仓触发器和具体的执行方式。**
    -   **3.1 触发条件组合 (Trigger Conditions)**

        -   **触发逻辑**: 提供单选按钮 `(●) 满足任一条件 (OR)  ( ) 满足全部条件 (AND)`，决定下方所有条件的组合方式。
        -   **动态条件列表**: 一个可以动态增删条件的列表区域。
            -   `[+ 添加应急条件]` 按钮，点击后弹出“条件配置”弹窗。
            -   列表中每一行都是一个已添加的条件，并有`[编辑]`和`[删除]`按钮。
        -   **“添加/编辑条件”弹窗**:
            -   **条件类型**: 下拉菜单，列出所有支持的条件模板，如：
                -   `价格类` -> `价格触及`
                -   `盈亏类` -> `持仓浮亏比例超过`
                -   **`行为类`** -> `N周期内无特定行为`
                -   `时间类` -> `持仓超过N分钟`
            -   **动态参数**: 根据选择的类型，显示不同的输入框。
                > **例：选择 `N周期内无特定行为`**
                >
                > -   `时间周期: [ 10 ]` `周期单位: [分钟 ▼]`
                > -   `监测对象: [ 主力A组 ▼ ]` (指向预设的经纪商分组)
                > -   `监测行为: [ 卖出 ▼ ]`
                > -   `行为条件: [ 累计成交量低于 ]` `[ 10000 ] 股`

    -   **3.2 清仓执行算法 (Liquidation Algorithm)**
        -   **清仓执行策略**: 提供下拉菜单，让用户选择清仓方式。
        -   **选项 1: 市价单 (Market Order)**
            -   _说明_: 速度最快，保证成交，但可能产生较大滑点。
        -   **选项 2: 优化限价单 (Optimized Limit Order)**
            -   _说明_: 平衡成交速度与价格，是推荐的默认选项。
            -   _配置项_:
                -   `基础价格: [ 卖一价 ▼ ]` (可选项: 买一价, 最新价等)
                -   `价格偏移: [ 向下 ▼ ]` `[ 3 ]` `[ ticks ▼ ]`
                -   `超时保护: [ 20 ] 秒后未完全成交，则 [ 撤单并转为市价单 ▼ ]`
        -   **选项 3: TWAP/VWAP 算法单**
            -   _说明_: 在指定时间内分批卖出，减少市场冲击，适合大资金。
            -   _配置项_: `执行时间(分钟): [ 5 ]`

##### **2.5 交互窗口 2：任务详细信息窗口 (Deep Dive Window)**

通过点击任务卡片上的`[详细信息]`按钮弹出，针对**单个任务**进行深度数据分析。

-   **窗口标题**: `详细信息 - 腾讯控股 0700.HK`
-   **核心监控仪表盘**: 显示该任务详细的持仓状态、策略信号说明。
-   **策略数据深度分析区**:
    -   **L2 深度报价 (Order Book)**: 实时买卖盘口。
    -   **逐笔成交明细 (Tick Feed)**: 滚动的 Tick 数据流。
    -   **自定义策略分析**: 高度定制的区域，例如“券商分点监控”表格。
-   **日志与反馈窗口**: 分页显示该任务的`策略日志`、`交易日志`和`系统日志`。

---

#### **第三章：技术架构选型**

##### **3.1 整体架构**

本系统采用**三层分离架构**，确保各层职责清晰、易于维护和扩展：

```
🌐 前端层 (React + TypeScript)
    ↕️ Tauri IPC
🦀 后端层 (Tauri + Rust)
    ↕️ Sidecar 进程通信
🐍 适配器层 (Python Sidecar)
    ↕️ API 调用
📈 交易平台 (富途/华盛)
```

##### **3.2 技术栈选型**

-   **核心框架**: **Tauri 1.x** - 提供跨平台桌面应用能力
-   **后端语言**: **Rust** - 高性能、内存安全的系统层语言
-   **前端技术**: **React + TypeScript + Vite** - 现代化Web开发栈
-   **数据服务**: **Python + 富途牛牛 OpenAPI SDK** - 快速适配交易接口
-   **通信方式**: **Tauri Sidecar 模式 + 标准 I/O** - 简化进程间通信

##### **3.3 核心架构特点**

-   **进程隔离**: Python数据服务作为独立Sidecar进程运行，提高系统稳定性
-   **实时通信**: 基于标准输入输出的高效双向通信协议
-   **模块化设计**: 各层独立开发、测试和部署
-   **类型安全**: 全栈TypeScript + Rust强类型系统保障

---

#### **第四章：关键数据与接口规格**

##### **4.1 接口职责分工**

基于**双平台架构**的接口职责清晰分工：

-   **富途牛牛**: 专注于**行情数据服务**，提供实时市场数据
    -   10档买卖盘数据
    -   逐笔成交明细
    -   经纪商队列信息
    -   实时价格推送
-   **华盛通**: 专注于**交易执行服务**，提供完整的交易功能
    -   账户资金查询
    -   持仓信息管理
    -   订单下达和管理
    -   交易记录查询

##### **4.2 数据接口规格**

-   **行情数据接口**: **富途牛牛 OpenAPI (FutuOpenD v6.3+)**
-   **交易执行接口**: **华盛通 T0 交易软件开放接口 (Valuable Capital OpenAPI v2.0+)**

##### **4.3 核心数据格式**

-   **行情数据 (由富途提供)**: `经纪商队列 (BrokerQueue)`、`实时报价 (Ticker)`、`逐笔成交 (RTData)`。
-   **账户数据 (由华盛通提供)**: `持仓信息`、`账户余额`、`订单状态`。

##### **4.4 通信协议设计**

采用**JSON消息格式**进行标准化通信：

```json
// Python → Tauri (实时数据推送)
{
    "type": "realtime_data",
    "service": "futu",
    "data": {
        "symbol": "HK.00700",
        "price": 320.5,
        "timestamp": 1642567890.123
    }
}

// Tauri → Python (命令请求)
{
    "id": "cmd_12345",
    "action": "get_quote",
    "params": {
        "symbol": "HK.00700"
    },
    "timestamp": 1642567890.456
}

// Python → Tauri (错误响应)
{
    "type": "error",
    "source": "futu",
    "command_id": "cmd_12345",
    "data": null,
    "success": false,
    "error": "股票代码不存在或连接超时",
    "timestamp": 1642567890.789
}
```

---

#### **第五章：核心功能流程设计**

##### **5.1 数据监听与解析**

1.  **启动**: Tauri 的 Rust 后端通过 Sidecar 模式启动并管理 Python 数据服务子进程。
2.  **订阅**: Rust 主进程通过`stdin`向 Python 服务发送指令，Python 服务调用 SDK 订阅指定股票的数据。
3.  **传输**: Python 服务将接收到的实时数据处理成 JSON 字符串，逐行打印到`stdout`。Rust 后端异步监听`stdout`流，完成数据接收。

##### **5.2 策略信号生成**

1.  Rust 后端接收到数据后，送入对应任务的策略引擎实例进行实时计算。
2.  当策略条件满足时，生成 `BUY` 或 `SELL` 信号。

##### **5.3 订单执行**

1.  信号经风控模块前置检查通过后，订单生成模块创建符合**华盛通交易 API**要求的订单对象，并通过 API 发送。

##### **5.4 持仓监控与风控**

1.  订单成交后，更新持仓状态。
2.  **应急风控引擎**启动，将实时行情与用户配置的应急条件进行比对。

##### **5.5 应急清仓执行**

1.  应急条件触发后，系统根据配置的“清仓执行算法”，生成清仓订单并通过**华盛通交易 API**执行。

---

#### **第六章：系统健壮性与非功能性需求**

本章定义了保证系统稳定、可靠、安全运行的系统级行为和要求。

##### **6.1 健壮性与异常处理**

-   **6.1.1 网络与 API 连接**

    -   **数据接口 (富途)**: 系统需内置自动重连机制。若连接中断，按指数退避策略尝试重连，5 次失败后暂停所有任务并告警。
    -   **交易接口 (华盛通)**: 提交订单时若 API 返回错误或超时，需记录日志并 UI 告警，**不进行自动重试**以防重复下单。

-   **6.1.2 边车进程(Sidecar)管理**

    -   Rust 主进程负责监控 Python Sidecar 的健康状态。若其意外退出，应尝试自动重启（最多 3 次）。重启期间，任务自动暂停并显示“数据服务异常”。

-   **6.1.3 状态持久化**
    -   系统在每次持仓变动后，将所有任务的**持仓状态**（股票代码、持仓量、平均成本）实时保存到本地`state.json`。应用重启时可加载此状态以恢复显示，但**不会自动恢复策略运行**，需用户手动确认。

##### **6.2 策略验证支持**

-   **6.2.1 系统模式**: 系统架构需支持`实盘模式 (Live Mode)` 和 `回测模式 (Backtest Mode)`，通过配置文件切换。
-   **6.2.2 回测模式要求**: 在`回测模式`下，数据源和交易执行器应被替换为本地的“历史数据读取器”和“模拟交易所”，以在不改动核心策略代码的情况下进行历史回测。
-   **6.2.3 模拟盘交易**: 在进行实盘交易前，系统应支持连接**华盛通的模拟盘账户**（若其 API 提供此功能）。

##### **6.3 配置与安全**

-   **6.3.1 配置管理**: 所有非敏感参数存储于用户目录下的`config.toml`文件。
-   **6.3.2 敏感信息管理**: API `Key`和`Secret`等凭证，**严禁明文存储**。必须通过环境变量或操作系统级的凭证管理器进行加载。
