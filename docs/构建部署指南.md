# 🚀 构建部署指南

本文档详细介绍XX交易终端的构建、打包和部署流程。

## 📋 目录

- [一键构建（推荐）](#一键构建推荐)
- [环境准备](#环境准备)  
- [手动构建流程](#手动构建流程)
- [验证和测试](#验证和测试)
- [最佳实践](#最佳实践)

## 🎯 一键构建（推荐）

**最简单的方式：**

```bash
./scripts/build-production.sh
```

该脚本会自动完成所有构建步骤：
- 构建 Python 服务包装器
- 安装前端依赖
- 构建前端资源  
- 构建 Tauri 应用
- 验证构建产物
- 执行功能测试

构建完成后应用可以直接使用。

## 环境准备

### 必需工具

#### 1. Rust 工具链

```bash
# 安装 Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# 重新加载环境
source ~/.bashrc  # 或 source ~/.zshrc

# 验证安装
rustc --version
cargo --version
```

**官方文档**: [Rust 安装指南](https://www.rust-lang.org/zh-CN/tools/install)

#### 2. Node.js 和 Yarn

```bash
# 安装 Node.js (推荐使用 nvm)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
nvm install --lts
nvm use --lts

# 安装 Yarn
npm install -g yarn

# 验证安装
node --version
yarn --version
```

**官方文档**: [Node.js 安装指南](https://nodejs.org/zh-cn/download/) | [Yarn 安装指南](https://yarnpkg.com/getting-started/install)

#### 3. Tauri CLI

```bash
# 通过 Cargo 安装 Tauri CLI
cargo install tauri-cli --version "^1"

# 验证安装
cargo tauri --version
```

#### 4. UV (Python 包管理器)

```bash
# macOS/Linux
curl -LsSf https://astral.sh/uv/install.sh | sh

# Windows
powershell -c "irm https://astral.sh/uv/install.ps1 | iex"

# 验证安装
uv --version
```

**官方文档**: [uv 安装指南](https://docs.astral.sh/uv/getting-started/installation/)

### 环境配置

#### Cargo 镜像配置（中国用户推荐）

```bash
# 创建 Cargo 配置文件
mkdir -p ~/.cargo
cat > ~/.cargo/config.toml << EOF
[source.crates-io]
registry = "https://github.com/rust-lang/crates.io-index"
replace-with = 'ustc'

[source.ustc]
registry = "https://mirrors.ustc.edu.cn/crates.io-index"

[net]
git-fetch-with-cli = true
EOF
```

## 构建流程

### 快速构建

适用于快速测试和开发验证：

```bash
# 1. 安装依赖
yarn install

# 2. 构建前端
yarn build

# 3. 构建 Tauri 应用
yarn tauri build
```

### 生产环境完整构建

使用自动化构建脚本，包含完整的验证和修复逻辑：

```bash
# 运行生产环境构建脚本
./scripts/build-production.sh
```

#### 构建脚本完整流程

`build-production.sh` 脚本执行以下步骤：

1. **🔨 构建 Python 服务包装器**
   - 清理旧的包装器文件
   - 生成带环境检测的智能包装脚本
   - 支持 macOS 应用包环境和开发环境

2. **📦 安装和构建前端**
   - 检查并安装 Node.js 依赖
   - 构建 React 前端资源
   - 验证构建产物

3. **🏗️ 构建 Tauri 应用**
   - 清理之前的构建产物
   - 构建 Rust 后端
   - 打包应用程序

4. **🔧 自动修复脚本位置**（关键步骤）
   - 将脚本从 `Contents/MacOS/` 复制到 `Contents/Resources/`
   - 解决 Tauri Sidecar 脚本位置不一致问题

5. **✅ 验证和报告**
   - 显示构建产物位置和大小
   - 验证应用包内的脚本正确性
   - 检查 Python 资源文件完整性

## 核心问题解决方案

### Tauri Sidecar 脚本位置不一致问题

#### 问题描述

Tauri 构建过程中存在一个关键问题：

- **构建阶段**：脚本被放置在 `Contents/MacOS/` 目录
- **运行阶段**：Tauri Sidecar 期望在 `Contents/Resources/` 目录找到脚本
- **结果**：导致 "Sidecar binary not found" 错误

#### 三层解决方案

##### 1. 构建脚本自动修复（主要方案）

```bash
# 在 build-production.sh 中的步骤 6.5
for script in counter-service futu-adapter huasheng-adapter; do
    if [ -f "$APP_PATH/Contents/MacOS/$script" ]; then
        cp "$APP_PATH/Contents/MacOS/$script" "$APP_PATH/Contents/Resources/$script"
        echo "✅ 已复制 $script 到 Resources 目录"
    fi
done
```

##### 2. Python 包装脚本智能路径检测（备用方案）

包装脚本自动检测运行环境：

```bash
# 获取脚本所在目录
SCRIPT_DIR="$(cd "$(dirname "$0")" && pwd)"

# 智能环境检测
if [[ "$SCRIPT_DIR" == */Contents/MacOS ]]; then
    # 脚本在 Contents/MacOS/ 目录下
    CONTENTS_DIR="$(dirname "$SCRIPT_DIR")"
    PROJECT_ROOT="$CONTENTS_DIR/Resources/_up_"
    echo "🍎 macOS 应用包环境 (MacOS)" >&2
elif [[ "$SCRIPT_DIR" == */Contents/Resources ]]; then
    # 脚本在 Contents/Resources/ 目录下
    CONTENTS_DIR="$(dirname "$SCRIPT_DIR")"
    PROJECT_ROOT="$SCRIPT_DIR/_up_"
    echo "🍎 macOS 应用包环境 (Resources)" >&2
else
    # 开发环境
    PROJECT_ROOT="$(dirname "$(dirname "$SCRIPT_DIR")")"
    echo "🔧 开发环境" >&2
fi
```

##### 3. UV 命令路径自动查找（环境兼容）

解决生产环境 PATH 问题：

```bash
# 检查 uv 命令是否可用，使用绝对路径作为备选
UV_CMD="uv"
if ! command -v uv >/dev/null 2>&1; then
    # 尝试常见的 uv 安装路径
    if [ -x "/Users/<USER>/.local/bin/uv" ]; then
        UV_CMD="/Users/<USER>/.local/bin/uv"
    elif [ -x "$HOME/.local/bin/uv" ]; then
        UV_CMD="$HOME/.local/bin/uv"
    else
        echo "❌ uv 命令未找到，请安装 uv 或检查 PATH"
        exit 1
    fi
fi
```

## 部署和验证

### 构建产物

成功构建后会生成以下文件：

```
src-tauri/target/release/bundle/
├── macos/
│   └── XX交易终端.app/           # macOS 应用包
└── dmg/
    └── XX交易终端_0.1.0_aarch64.dmg  # 安装包
```

### 应用包结构验证

```bash
# 检查应用包结构
ls -la "src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/"

# 验证脚本位置（两个位置都应该有）
ls -la "src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/MacOS/"
ls -la "src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/Resources/"

# 验证 Python 资源
ls -la "src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/Resources/_up_/"
```

### 功能测试

#### 1. 使用测试脚本

```bash
# 运行自动化测试
./testing/test_packaged_app.py "src-tauri/target/release/bundle/macos/XX交易终端.app"
```

#### 2. 手动启动测试

```bash
# 通过命令行启动
open "src-tauri/target/release/bundle/macos/XX交易终端.app"

# 检查进程
ps aux | grep "XX交易终端"
ps aux | grep "counter-service\|futu-adapter\|huasheng-adapter"
```

## 最佳实践

### 构建前准备

1. **清理旧构建产物**
   ```bash
   rm -rf src-tauri/target/release/bundle/
   rm -rf dist/
   ```

2. **更新依赖**
   ```bash
   yarn install
   cargo update
   ```

### 构建过程优化

1. **使用生产构建脚本**
   - 始终使用 `./scripts/build-production.sh`
   - 不要跳过任何步骤，特别是步骤 6.5 的脚本修复

2. **验证构建结果**
   - 检查构建日志中的 "✅ 已复制 xxx 到 Resources 目录" 信息
   - 确认脚本包含正确的环境检测逻辑

### 部署前检查

1. **脚本验证**
   ```bash
   # 检查脚本内容
   grep -n "macOS 应用包环境" "src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/Resources/counter-service"
   ```

2. **权限检查**
   ```bash
   # 确保脚本有执行权限
   ls -la "src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/Resources/"
   ```

## 故障排除

### 常见问题

#### 1. "Sidecar binary not found" 错误

**症状**：应用启动时无法找到 Python 服务脚本

**解决方案**：
```bash
# 手动复制脚本（临时解决）
APP_PATH="src-tauri/target/release/bundle/macos/XX交易终端.app"
for script in counter-service futu-adapter huasheng-adapter; do
    cp "$APP_PATH/Contents/MacOS/$script" "$APP_PATH/Contents/Resources/$script"
done
```

**根本解决**：确保使用 `build-production.sh` 脚本进行构建

#### 2. UV 命令未找到

**症状**：Python 服务启动失败，日志显示 "uv 命令未找到"

**解决方案**：
```bash
# 确保 UV 已安装并在正确路径
which uv
ls -la ~/.local/bin/uv

# 如果需要，重新安装 UV
curl -LsSf https://astral.sh/uv/install.sh | sh
```

#### 3. 构建脚本被超时中断

**症状**：`build-production.sh` 运行时被中断，步骤 6.5 未执行

**解决方案**：
```bash
# 单独运行 Tauri 构建
cd src-tauri && cargo tauri build

# 然后手动执行脚本修复
APP_PATH="target/release/bundle/macos/XX交易终端.app"
for script in counter-service futu-adapter huasheng-adapter; do
    cp "$APP_PATH/Contents/MacOS/$script" "$APP_PATH/Contents/Resources/$script"
done
```

### 诊断工具

#### 日志查看

```bash
# 查看系统日志
log show --last 30s | grep -E "XX交易终端|counter-service"

# 查看应用启动日志
./testing/automated-tests/monitor-app-logs.sh
```

#### 测试工具

```bash
# 全面测试
./testing/test_packaged_app.py

# 快速验证
./testing/automated-tests/quick-build.sh
```

## 相关文档

- [README.md](../README.md) - 项目快速开始指南
- [故障排除指南.md](./故障排除指南.md) - 详细的问题诊断和解决方案
- [开发日志.md](./开发日志.md) - 开发过程中的问题记录

---

📅 **最后更新**: 2025-07-22  
👤 **维护者**: 开发团队  
🔧 **版本**: v1.0