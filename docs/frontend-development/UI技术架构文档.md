# 量化交易终端 UI 技术架构文档

## 📐 整体架构设计

### 架构分层
```
┌─────────────────────────────────────────────────┐
│                  展示层 (Presentation)            │
│  ┌─────────────────┐  ┌─────────────────┐      │
│  │   Dashboard     │  │   Modal系统      │      │
│  │   - 主仪表盘     │  │  - TaskConfig   │      │
│  │   - 导航系统     │  │  - TaskDetails  │      │
│  └─────────────────┘  └─────────────────┘      │
├─────────────────────────────────────────────────┤
│                  组件层 (Components)              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│  │GlobalControls│ │  TaskCard   │ │  TaskList   │ │
│  │- 全局控制    │ │ - 任务卡片   │ │ - 任务列表   │ │
│  │- 状态展示    │ │ - 状态指示   │ │ - 网格布局   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────┤
│                  逻辑层 (Business Logic)          │
│  ┌─────────────────────────────────────────────┐ │
│  │              useDashboard Hook              │ │
│  │  - 状态管理     - 业务操作     - 事件处理    │ │
│  │  - 数据获取     - 实时更新     - Modal控制   │ │
│  └─────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────┤
│                  通信层 (Communication)           │
│  ┌─────────────────┐  ┌─────────────────┐      │
│  │   FutuClient    │  │ HuashengClient  │      │
│  │   - 行情数据     │  │  - 交易执行     │      │
│  │   - 实时推送     │  │  - 账户查询     │      │
│  └─────────────────┘  └─────────────────┘      │
├─────────────────────────────────────────────────┤
│                  数据层 (Data)                   │
│  ┌─────────────────────────────────────────────┐ │
│  │                 类型系统                     │ │
│  │  Task | Strategy | RiskConfig | GlobalStatus│ │
│  └─────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────┘
```

### 数据流向图
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ 用户交互操作 │ ──► │ Hook 函数   │ ──► │ 状态更新     │
└─────────────┘     └─────────────┘     └─────────────┘
                                              │
┌─────────────┐     ┌─────────────┐     ┌─────┴─────┐
│ UI 重新渲染 │ ◄── │ 组件 Props   │ ◄── │ State 变化│
└─────────────┘     └─────────────┘     └───────────┘

┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│ 后端实时推送 │ ──► │ 事件监听器   │ ──► │ Hook 状态   │
└─────────────┘     └─────────────┘     └─────────────┘
```

---

## 🧩 核心组件设计

### Dashboard 主容器
```typescript
interface DashboardProps = {};

// 职责：
// 1. 整合所有子组件
// 2. 提供统一的数据和操作接口  
// 3. 管理Modal显示状态
// 4. 处理加载状态
```

### GlobalControls 全局控制区
```typescript
interface GlobalControlsProps {
    globalStatus: GlobalStatus;    // 全局状态数据
    onAddTask: () => void;         // 添加任务回调
    onStartAll: () => void;        // 全部启动回调
    onStopAll: () => void;         // 全部停止回调
    isLoading: boolean;            // 加载状态
}

// 布局结构：
// [操作按钮组] [统计数据区] [连接状态指示]
```

### TaskCard 任务卡片
```typescript
interface TaskCardProps {
    task: Task;                           // 任务数据
    onToggle: (taskId: string) => void;   // 切换状态
    onShowDetails: (taskId: string) => void; // 显示详情
    onEdit: (taskId: string) => void;     // 编辑任务
    onDelete: (taskId: string) => void;   // 删除任务
}

// 卡片结构：
// ┌─[股票信息 + 状态指示]─┐
// │ 持仓信息              │
// │ 盈亏信息              │
// │ 更新时间              │
// └─[操作按钮组]─────────┘
```

### TaskList 任务列表容器
```typescript
interface TaskListProps {
    tasks: Task[];                        // 任务数组
    onToggleTask: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEditTask: (taskId: string) => void;
    onDeleteTask: (taskId: string) => void;
}

// 网格布局：
// ┌─────┬─────┬─────┐
// │Card1│Card2│Card3│
// ├─────┼─────┼─────┤
// │Card4│Card5│Card6│
// └─────┴─────┴─────┘
```

---

## 🔄 状态管理架构

### useDashboard Hook 状态结构
```typescript
interface DashboardState {
    // 核心数据状态
    tasks: Task[];                    // 任务列表
    globalStatus: GlobalStatus;       // 全局状态
    isLoading: boolean;              // 加载状态
    
    // UI 控制状态
    selectedTask: Task | null;        // 当前选中任务
    showConfigModal: boolean;         // 配置窗口显示
    showDetailsModal: boolean;        // 详情窗口显示
}
```

### 状态更新流程
```typescript
// 1. 用户操作触发
const toggleTask = async (taskId: string) => {
    // 2. 计算新状态
    const updatedTasks = tasks.map(task => {
        if (task.id === taskId) {
            const newStatus = task.status === 'running' ? 'paused' : 'running';
            return { ...task, status: newStatus, updatedAt: new Date() };
        }
        return task;
    });
    
    // 3. 更新状态
    setTasks(updatedTasks);
    
    // 4. 同步全局状态
    updateGlobalStatus(updatedTasks);
};
```

### 实时数据更新机制
```typescript
// 定时更新 PnL 数据
useEffect(() => {
    const interval = setInterval(() => {
        setTasks(prevTasks => {
            return prevTasks.map(task => {
                if (task.status === 'running' && task.position > 0) {
                    // 模拟价格波动
                    const priceChange = (Math.random() - 0.5) * 0.01 * (task.avgCost || 100);
                    const newPnL = task.pnl + (priceChange * task.position);
                    return { ...task, pnl: newPnL };
                }
                return task;
            });
        });
    }, 2000); // 每2秒更新一次
    
    return () => clearInterval(interval);
}, []);
```

---

## 🎨 样式系统设计

### CSS 架构原则
1. **BEM 命名规范**: `.task-card__status-indicator--running`
2. **语义化类名**: `.profit`, `.loss`, `.status-running`  
3. **响应式优先**: Mobile First 设计
4. **CSS 变量**: 统一色彩和尺寸管理

### 核心样式变量
```css
:root {
    /* 色彩系统 */
    --color-success: #28a745;
    --color-warning: #ffc107;
    --color-danger: #dc3545;
    --color-info: #17a2b8;
    --color-primary: #007bff;
    --color-secondary: #6c757d;
    
    /* 状态颜色 */
    --status-running: #28a745;
    --status-paused: #ffc107;
    --status-error: #dc3545;
    --status-stopped: #6c757d;
    
    /* 盈亏颜色 */
    --pnl-profit: #28a745;
    --pnl-loss: #dc3545;
    
    /* 间距系统 */
    --spacing-xs: 5px;
    --spacing-sm: 10px;
    --spacing-md: 15px;
    --spacing-lg: 20px;
    --spacing-xl: 30px;
    
    /* 圆角 */
    --border-radius-sm: 5px;
    --border-radius-md: 8px;
    --border-radius-lg: 10px;
}
```

### 网格布局系统
```css
.task-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-lg);
}

/* 响应式断点 */
@media (max-width: 768px) {
    .task-grid {
        grid-template-columns: 1fr;
    }
}
```

### 动画系统
```css
/* 运行状态脉动动画 */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 错误状态闪烁动画 */
@keyframes blink {
    0%, 50% { opacity: 1; }
    51%, 100% { opacity: 0; }
}

/* 卡片悬停效果 */
.task-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}
```

---

## 📊 数据类型设计

### 核心实体类型
```typescript
// 任务实体
interface Task {
    id: string;                    // 唯一标识
    name: string;                  // 任务名称
    stockCode: string;             // 股票代码
    stockName: string;             // 股票名称
    strategyName: string;          // 策略名称
    status: TaskStatus;            // 运行状态
    position: number;              // 持仓数量
    avgCost?: number;              // 平均成本
    pnl: number;                   // 浮动盈亏
    createdAt: Date;              // 创建时间
    updatedAt: Date;              // 更新时间
    strategyConfig: StrategyConfig; // 策略配置
    riskConfig: RiskConfig;        // 风控配置
}

// 状态枚举
type TaskStatus = 'running' | 'paused' | 'error' | 'stopped';

// 全局状态
interface GlobalStatus {
    totalTasks: number;            // 总任务数
    runningTasks: number;          // 运行中任务数  
    totalMarketValue: number;      // 总持仓市值
    totalPnL: number;             // 总浮动盈亏
    connections: {                 // 连接状态
        market: ConnectionStatus;  // 行情连接
        trading: ConnectionStatus; // 交易连接
    };
}
```

### 策略配置类型
```typescript
// 策略配置基类
interface StrategyConfig {
    strategyType: string;
    params: Record<string, any>;
}

// 策略A：大单监控
interface StrategyAConfig extends StrategyConfig {
    strategyType: 'strategy_a_big_order_monitor';
    params: {
        monitorThreshold: number;     // 监控阈值
        durationSeconds: number;      // 持续时间
        targetBrokers: string[];      // 目标经纪商
        orderSize: number;            // 单笔大小
    };
}

// 策略B：突破追涨  
interface StrategyBConfig extends StrategyConfig {
    strategyType: 'strategy_b_breakout_chase';
    params: {
        breakoutPeriod: number;       // 突破周期
        volumeMultiplier: number;     // 成交量倍数
        pullbackPercent: number;      // 回踩幅度
    };
}
```

### 风控配置类型
```typescript
// 风控配置
interface RiskConfig {
    triggerLogic: 'any' | 'all';      // 触发逻辑
    conditions: RiskCondition[];       // 风险条件
    liquidationStrategy: LiquidationStrategy; // 清仓策略
}

// 风险条件基类
interface RiskCondition {
    id: string;
    type: 'price' | 'pnl_ratio' | 'behavior' | 'time';
    params: Record<string, any>;
}

// 清仓策略
interface LiquidationStrategy {
    type: 'market' | 'limit_optimized' | 'twap_vwap';
    params: Record<string, any>;
}
```

---

## 🔌 通信集成设计

### 事件监听机制
```typescript
// Tauri 事件监听
useEffect(() => {
    const setupEventListeners = async () => {
        await listen('sidecar-message', (event: any) => {
            const message = event.payload;
            
            if (message.type === 'push') {
                // 处理实时数据推送
                handleRealtimeUpdate(message);
            }
        });
    };
    
    setupEventListeners();
}, []);

// 实时数据处理
const handleRealtimeUpdate = (message: any) => {
    switch (message.source) {
        case 'futu':
            if (message.data.type === 'quote_data') {
                updateTaskPnL(message.data);
            }
            break;
        case 'huasheng':
            if (message.data.type === 'position_update') {
                updateTaskPosition(message.data);
            }
            break;
    }
};
```

### API 调用抽象
```typescript
// 任务操作 API
class TaskAPI {
    static async createTask(config: Partial<Task>): Promise<Task> {
        return invoke('create_task', { config });
    }
    
    static async updateTask(taskId: string, updates: Partial<Task>): Promise<void> {
        return invoke('update_task', { taskId, updates });
    }
    
    static async deleteTask(taskId: string): Promise<void> {
        return invoke('delete_task', { taskId });
    }
    
    static async startTask(taskId: string): Promise<void> {
        return invoke('start_task', { taskId });
    }
    
    static async stopTask(taskId: string): Promise<void> {
        return invoke('stop_task', { taskId });
    }
}
```

---

## 🧪 测试架构

### 组件测试策略
```typescript
// TaskCard 组件测试示例
describe('TaskCard', () => {
    const mockTask: Task = {
        id: 'test-1',
        name: '测试任务',
        stockCode: 'HK.00700',
        stockName: '腾讯控股',
        status: 'running',
        position: 100,
        pnl: 1000,
        // ...其他字段
    };
    
    it('应该正确显示任务信息', () => {
        render(
            <TaskCard
                task={mockTask}
                onToggle={jest.fn()}
                onShowDetails={jest.fn()}
                onEdit={jest.fn()}
                onDelete={jest.fn()}
            />
        );
        
        expect(screen.getByText('腾讯控股 (HK.00700)')).toBeInTheDocument();
        expect(screen.getByText('● 运行中')).toBeInTheDocument();
    });
    
    it('应该正确处理状态切换', () => {
        const onToggle = jest.fn();
        render(<TaskCard task={mockTask} onToggle={onToggle} {...otherProps} />);
        
        fireEvent.click(screen.getByText('❚❚ 暂停'));
        expect(onToggle).toHaveBeenCalledWith('test-1');
    });
});
```

### Hook 测试策略
```typescript
// useDashboard Hook 测试示例
describe('useDashboard', () => {
    it('应该正确初始化状态', () => {
        const { result } = renderHook(() => useDashboard());
        
        expect(result.current.tasks).toEqual([]);
        expect(result.current.isLoading).toBe(false);
        expect(result.current.globalStatus.totalTasks).toBe(0);
    });
    
    it('应该正确处理任务切换', async () => {
        const { result } = renderHook(() => useDashboard());
        
        act(() => {
            result.current.addTask(mockTaskConfig);
        });
        
        await waitFor(() => {
            expect(result.current.tasks).toHaveLength(1);
        });
        
        act(() => {
            result.current.toggleTask(result.current.tasks[0].id);
        });
        
        expect(result.current.tasks[0].status).toBe('paused');
    });
});
```

---

## 📈 性能优化策略

### 渲染优化
```typescript
// 使用 React.memo 避免不必要的重渲染
export const TaskCard = React.memo<TaskCardProps>(({
    task,
    onToggle,
    onShowDetails,
    onEdit,
    onDelete
}) => {
    // 组件实现
}, (prevProps, nextProps) => {
    // 自定义比较函数
    return prevProps.task.id === nextProps.task.id &&
           prevProps.task.status === nextProps.task.status &&
           prevProps.task.pnl === nextProps.task.pnl;
});

// 使用 useCallback 避免函数重创建
const toggleTask = useCallback((taskId: string) => {
    // 实现逻辑
}, [tasks]);
```

### 数据优化
```typescript
// 使用 useMemo 缓存计算结果
const taskStats = useMemo(() => {
    return {
        running: tasks.filter(t => t.status === 'running').length,
        paused: tasks.filter(t => t.status === 'paused').length,
        stopped: tasks.filter(t => t.status === 'stopped').length,
        error: tasks.filter(t => t.status === 'error').length
    };
}, [tasks]);

// 防抖处理高频更新
const debouncedUpdatePnL = useMemo(
    () => debounce((updates: PnLUpdate[]) => {
        // 批量更新逻辑
    }, 100),
    []
);
```

### 虚拟化支持
```typescript
// 对于大量任务的场景，可以集成虚拟滚动
import { FixedSizeGrid as Grid } from 'react-window';

const VirtualizedTaskGrid = ({ tasks }: { tasks: Task[] }) => (
    <Grid
        columnCount={Math.floor(window.innerWidth / 420)}
        columnWidth={420}
        height={600}
        rowCount={Math.ceil(tasks.length / columnCount)}
        rowHeight={300}
        itemData={tasks}
    >
        {TaskCardRenderer}
    </Grid>
);
```

---

**文档版本**: v1.0.0  
**创建时间**: 2025年7月22日  
**适用范围**: 量化交易终端前端架构  
**维护状态**: 活跃维护