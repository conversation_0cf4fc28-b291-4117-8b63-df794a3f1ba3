# Tauri Sidecar 通信方案完整指南

## 📋 概述

本文档提供基于 **Tauri Sidecar + 标准 I/O** 的实时通信方案的完整指南，包括架构设计、技术实现和使用方法。该方案用于实现富途和华盛交易接口的实时数据推送和双向命令通信。

### 核心需求

-   ✅ **长期运行的 Python 进程** - 保持与交易所的长连接
-   ✅ **实时数据推送** - 交易所 → Python → Tauri → 前端
-   ✅ **双向通信** - 前端可以发送命令到 Python
-   ✅ **单一可执行文件** - 简化部署

---

## 🏗️ 架构设计

### 整体架构图

```
┌─────────────────┐    实时推送     ┌──────────────────┐    长连接    ┌─────────────────┐
│   React 前端    │ ←──────────────→ │  Tauri Backend   │ ←──────────→ │ Python Sidecar  │ ←──────→ │ 富途/华盛服务器 │
└─────────────────┘   Tauri Events   └──────────────────┘  stdin/stdout └─────────────────┘  WebSocket └─────────────────┘
                                              ↑                                ↑
                                         命令调用                        实时数据接收
                                              ↓                                ↓
                                      ┌──────────────────┐              ┌─────────────────┐
                                      │  Tauri Commands  │              │   数据处理逻辑   │
                                      └──────────────────┘              └─────────────────┘
```

### 三层通信架构

```
前端 (TypeScript)
    ↕️
Tauri Backend (Rust)
    ↕️
Python Sidecar (Python)
```

### 封装层次

1. **基础通信层**：处理消息序列化、进程管理、错误处理
2. **交易抽象层**：提供标准化的交易接口
3. **具体实现层**：富途、华盛等具体的交易适配器

### 数据流设计

#### 1. 实时推送流程

```
交易所实时数据 → Python接收 → stdout输出 → Tauri读取 → 前端事件 → UI更新
```

#### 2. 命令执行流程

```
前端操作 → Tauri Command → stdin发送 → Python处理 → stdout返回 → 前端响应
```

## 📡 通信协议设计

### JSON 消息格式

#### Python → Tauri (stdout)

```json
{
  "type": "push|response|error",
  "source": "futu|huasheng|system",
  "command_id": "uuid-string",
  "data": {...},
  "timestamp": 1234567890,
  "success": true,
  "error": "error message if any"
}
```

#### Tauri → Python (stdin)

```json
{
  "id": "uuid-string",
  "action": "query_funds|query_positions|subscribe_data",
  "params": {...},
  "timestamp": 1234567890
}
```

### 消息类型定义

| 类型       | 方向         | 描述         | 示例               |
| ---------- | ------------ | ------------ | ------------------ |
| `push`     | Python→Tauri | 实时数据推送 | 股价变动、交易状态 |
| `response` | Python→Tauri | 命令执行结果 | 查询资金结果       |
| `error`    | Python→Tauri | 错误信息     | 连接失败、API 错误 |
| `command`  | Tauri→Python | 执行命令     | 查询持仓、下单操作 |

---

## 💻 使用指南

### 🐍 Python 端封装

#### 基础通信类 - `TauriCommunicator`

```python
from src_python.adapters.tauri_communication import TauriCommunicator

# 创建通信器
comm = TauriCommunicator("my_adapter")

# 注册命令处理器
comm.register_command("get_data", my_handler)

# 发送实时数据
comm.send_push({"price": 100.50}, "quote_data")

# 发送响应
comm.send_response(command_id, True, {"result": "success"})

# 启动监听器
await comm.start_stdin_listener()
```

#### 交易适配器基类 - `TradingAdapterBase`

```python
from src_python.adapters.tauri_communication import TradingAdapterBase

class MyTradingAdapter(TradingAdapterBase):
    def __init__(self):
        super().__init__("my_adapter")

    def register_custom_commands(self):
        # 注册自定义命令
        self.communicator.register_command("get_quote", self.get_quote)

    async def connect(self, params):
        # 实现连接逻辑
        return {"status": "connected"}

    async def get_quote(self, params):
        # 实现报价查询
        return {"price": 100.50}
```

#### 富途适配器使用示例

```python
# 使用增强版富途适配器
from src_python.adapters.futu_adapter_enhanced import FutuAdapterEnhanced

adapter = FutuAdapterEnhanced()
await adapter.start()
```

### 🦀 Rust 端封装

#### 基础客户端 - `SidecarClient`

```rust
use crate::communication::SidecarClient;

// 创建客户端
let client = SidecarClient::new(
    app_handle,
    "futu",
    "src-python/adapters/futu_adapter_enhanced.py"
);

// 启动适配器
client.start().await?;

// 发送命令
let response = client.send_command_with_response(
    "get_quote".to_string(),
    Some(json!({"stock_code": "HK.00700"}))
).await?;

// 心跳检查
client.ping().await?;
```

#### 交易客户端 - `TradingClient`

```rust
use crate::communication::TradingClient;

// 创建交易客户端
let trading_client = TradingClient::new(
    app_handle,
    "futu",
    "src-python/adapters/futu_adapter_enhanced.py"
);

// 启动
trading_client.start().await?;

// 获取报价
let quote = trading_client.get_quote("HK.00700").await?;

// 获取逐笔数据
let ticker = trading_client.get_ticker("HK.00700", Some(20)).await?;

// 订阅实时数据
let result = trading_client.subscribe_realtime(
    "HK.00700",
    vec![TradingDataType::Quote, TradingDataType::Ticker]
).await?;
```

### 🌐 前端封装

#### 基础交易客户端 - `TradingClientBase`

```typescript
import { TradingClientBase, TradingDataType } from "@/communication";

class MyTradingClient extends TradingClientBase {
    constructor() {
        super("my_adapter");
    }
}

const client = new MyTradingClient();

// 启动适配器
await client.start();

// 获取报价
const quote = await client.getQuote("HK.00700");

// 订阅实时数据
await client.subscribeRealtime("HK.00700", [TradingDataType.Quote]);

// 监听实时数据
await client.onRealtimeData((event) => {
    console.log("实时数据:", event);
});
```

#### 富途客户端使用示例

```typescript
import { futuClient } from "@/communication";

// 启动富途适配器
await futuClient.start();

// 连接富途服务
await futuClient.connect();

// 获取报价
const quote = await futuClient.getQuote("HK.00700");

// 获取资金信息
const funds = await futuClient.getFunds();

// 下单
await futuClient.placeOrder({
    stockCode: "HK.00700",
    orderType: "BUY",
    quantity: 100,
    price: 420.0
});
```

#### 华盛客户端使用示例

```typescript
import { huashengClient } from "@/communication";

// 启动华盛适配器
await huashengClient.start();

// 连接华盛服务
await huashengClient.connect();

// 获取账户信息
const account = await huashengClient.getAccountInfo();

// 执行交易演示
await huashengClient.runTradingDemo();
```

---

## 🔧 关键技术实现

### 1. 进程生命周期管理

#### 优雅停止机制

```rust
impl SidecarManager {
    pub async fn stop(&self) {
        // 1. 首先取消心跳任务
        if let Some(cancel_token) = self.heartbeat_cancel_token.lock().await.take() {
            cancel_token.cancel();
        }

        // 2. 尝试优雅地停止服务
        if self.is_running().await {
            if let Err(e) = self.send_command_with_response("stop".to_string(), None).await {
                eprintln!("Failed to send stop command: {}", e);
            } else {
                // 等待一段时间让服务优雅关闭
                tokio::time::sleep(Duration::from_secs(2)).await;
            }
        }

        // 3. 然后强制停止子进程（如果还在运行）
        if let Some(mut child) = self.child.lock().await.take() {
            let _ = child.kill().await;
        }
    }
}
```

### 2. 心跳检测机制

#### 可取消的心跳任务

```rust
// 创建心跳检查任务的取消令牌
let cancel_token = CancellationToken::new();

// 发送心跳检查
tokio::spawn({
    let manager = self.clone();
    async move {
        loop {
            tokio::select! {
                _ = cancel_token.cancelled() => {
                    eprintln!("Heartbeat task cancelled");
                    break;
                }
                _ = tokio::time::sleep(Duration::from_secs(30)) => {
                    if let Err(e) = manager.ping().await {
                        eprintln!("Sidecar ping failed: {}", e);
                    }
                }
            }
        }
    }
});
```

### 3. 错误恢复机制

```python
class TauriSidecar:
    async def with_retry(self, func, max_retries=3):
        """带重试的函数执行"""
        for attempt in range(max_retries):
            try:
                return await func()
            except Exception as e:
                if attempt == max_retries - 1:
                    self.send_error(f"Max retries exceeded: {e}")
                    raise
                await asyncio.sleep(2 ** attempt)  # 指数退避
```

---

## 🔄 服务生命周期管理

### 启动服务

```typescript
// 启动指定服务
await invoke("start_service", { serviceName: "futu" });

// 启动所有自动启动服务
await invoke("start_auto_services");
```

### 停止服务

```typescript
// 停止指定服务（优雅停止）
await invoke("stop_service", { serviceName: "futu" });

// 停止所有服务
await invoke("stop_all_services");
```

#### 优雅停止机制流程

服务停止时会按以下顺序执行：

1. **取消心跳任务** - 停止后台心跳检查
2. **发送停止命令** - 向 Python 服务发送 `stop` 命令
3. **等待优雅关闭** - 给服务 2 秒时间自行关闭
4. **强制终止进程** - 如果服务未响应，强制杀死进程

#### Python 端停止命令处理

```python
class MyService:
    def __init__(self):
        self.running = True
        self.command_handlers = {
            'ping': self.handle_ping,
            'stop': self.handle_stop,  # 必须实现停止处理
        }
    
    async def handle_stop(self, params):
        """处理停止命令"""
        self.running = False
        return {"status": "stopping"}
    
    async def main_loop(self):
        """主循环必须检查 running 状态"""
        while self.running:
            # 处理业务逻辑
            await asyncio.sleep(1)
```

---

## 🔧 Tauri 命令注册

在 `src-tauri/src/main.rs` 中注册统一的 Sidecar 命令：

```rust
use crate::commands::*;
use crate::sidecar_manager::SidecarManager;

fn main() {
    tauri::Builder::default()
        .manage(SidecarManager::new())
        .invoke_handler(tauri::generate_handler![
            // Sidecar 管理
            start_sidecar,
            stop_sidecar,
            sidecar_status,

            // 统一的 Sidecar 通信接口
            send_sidecar_command,
            ping_sidecar
        ])
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
```

### Sidecar 命令实现示例

```rust
// src-tauri/src/commands.rs
use crate::sidecar_manager::SidecarManager;
use tauri::State;

#[tauri::command]
pub async fn start_sidecar(
    manager: State<'_, SidecarManager>,
    adapter_name: String,
    script_path: String,
) -> Result<serde_json::Value, String> {
    manager.start_sidecar(&adapter_name, &script_path).await
}

#[tauri::command]
pub async fn send_sidecar_command(
    manager: State<'_, SidecarManager>,
    adapter_name: String,
    action: String,
    params: Option<serde_json::Value>,
) -> Result<serde_json::Value, String> {
    manager.send_command(&adapter_name, &action, params).await
}
```

---

## 📊 消息格式标准

### 命令格式

```json
{
    "id": "uuid-string",
    "action": "get_quote",
    "params": {
        "stock_code": "HK.00700"
    },
    "timestamp": 1642678800.123
}
```

### 响应格式

```json
{
    "type": "response",
    "source": "futu",
    "command_id": "uuid-string",
    "success": true,
    "data": {
        "quote": {
            "stock_code": "HK.00700",
            "price": 420.3,
            "change": 5.2
        }
    },
    "timestamp": 1642678801.456
}
```

### 推送格式

```json
{
    "type": "push",
    "source": "futu",
    "data": {
        "type": "quote_data",
        "stock_code": "HK.00700",
        "price": 420.35,
        "volume": 1000
    },
    "timestamp": 1642678802.789
}
```

---

## 🎯 前端状态同步最佳实践

### 问题背景

在自动启动 (`auto_start: true`) 的服务环境中，前端客户端的状态可能与后端服务实际状态不同步，导致用户操作失效。

### 核心问题

```typescript
// ❌ 错误的状态管理
export class ServiceClient {
    private isStarted = false; // 初始值固定为 false
    
    async stop() {
        if (!this.isStarted) {
            return; // 直接返回，不调用后端
        }
        // 停止逻辑...
    }
}
```

**问题场景**：
1. 服务通过 `start_auto_services()` 自动启动
2. `isStarted` 仍为 `false`（未同步）
3. 用户点击停止按钮 → `stop()` 检查状态 → 直接返回
4. 实际服务继续运行

### ✅ 正确的实现方式

#### 1. 构造函数中同步状态

```typescript
export class ServiceClient {
    private isStarted = false;
    
    constructor() {
        this.setupEventListeners();
        this.syncServiceStatus(); // 🔑 关键：同步服务状态
    }
    
    private async syncServiceStatus() {
        try {
            const status = await this.getStatus();
            if (status && status.running) {
                this.isStarted = true;
                console.log('🔍 Service is running, synchronized state');
            }
        } catch (error) {
            this.isStarted = false;
        }
    }
}
```

#### 2. 推送消息时更新状态

```typescript
private async setupEventListeners() {
    await listen('sidecar-message', (event: any) => {
        const message = event.payload;
        
        if (message.type === 'push' && message.source === 'my_service') {
            // 🔑 关键：接收到推送说明服务在运行
            if (!this.isStarted) {
                console.log('🔍 Detected running service, updating state');
                this.isStarted = true;
            }
            this.notifyListeners(message.data);
        }
    });
}
```

#### 3. 操作前验证状态

```typescript
async stop(): Promise<void> {
    console.log('🔍 Stop called, isStarted:', this.isStarted);
    
    // 可选：再次验证真实状态
    try {
        const status = await this.getStatus();
        if (!status || !status.running) {
            console.log('🔍 Service not running, skip stop');
            this.isStarted = false;
            return;
        }
    } catch (error) {
        console.log('🔍 Cannot verify status, proceeding with stop');
    }
    
    // 执行停止逻辑
    await invoke('stop_service', { serviceName: 'my_service' });
    this.isStarted = false;
}
```

### 🎯 状态同步原则

1. **初始化同步**：构造函数中检查实际服务状态
2. **实时更新**：通过推送消息检测服务运行状态
3. **操作验证**：关键操作前可选择再次验证状态
4. **双向同步**：前端状态变化时及时更新

---

## 🚀 使用优势

### 1. **标准化接口**

-   统一的命令格式和响应格式
-   标准化的错误处理
-   一致的事件监听机制

### 2. **可复用性**

-   基础通信逻辑可复用
-   交易接口标准化
-   适配器模式便于扩展

### 3. **类型安全**

-   TypeScript 类型定义
-   Rust 强类型系统
-   Python 类型提示

### 4. **错误处理**

-   统一的错误格式
-   超时处理
-   连接状态管理

### 5. **实时数据**

-   标准化的推送机制
-   事件监听器管理
-   自动重连机制

---

## 📊 性能指标

### 目标性能

-   **启动时间**: < 3 秒
-   **命令响应**: < 500ms
-   **实时数据延迟**: < 100ms
-   **内存占用**: < 200MB
-   **CPU 占用**: < 5% (空闲时)

### 监控指标

-   进程健康状态
-   消息传输成功率
-   数据推送延迟
-   错误恢复次数

---

## 🔍 测试策略

### 单元测试

-   Python sidecar 各模块功能
-   Rust 消息处理逻辑
-   前端数据处理组件

### 集成测试

-   端到端数据流测试
-   错误恢复机制测试
-   高负载压力测试

### 用户验收测试

-   实际交易场景模拟
-   用户界面响应性测试
-   长期稳定性测试

---

## 🚀 实施要点

### 关键优势

相比传统 IPC Socket 方案，新架构具有以下优势：

-   ✅ **简单可靠**的进程间通信
-   ✅ **实时数据推送**能力
-   ✅ **完整的错误处理**机制
-   ✅ **单一可执行文件**部署
-   ✅ **生产级别**的性能和稳定性

### 技术选型理由

- **标准 I/O 通信**：避免复杂的网络配置，提高可靠性
- **JSON 消息格式**：易于调试和扩展
- **异步处理**：支持高并发和实时数据处理
- **优雅停止**：确保数据完整性和资源清理

### 🎯 下一步集成

当需要对接真实的富途牛牛或华盛时：

1. **替换模拟数据**：在适配器中实现真实的 API 调用
2. **配置管理**：使用 `config_manager.py` 管理账号配置
3. **错误处理**：完善具体的错误处理逻辑
4. **性能优化**：根据实际使用情况优化性能

通过这套封装，您可以快速集成任何交易服务，而无需重复开发通信基础设施！

---

## 📚 相关文档

-   [富途牛牛与华盛通接口职责说明](富途牛牛与华盛通接口职责说明.md) - 接口分工详解
-   [量化交易终端完整设计与规格说明书](量化交易终端完整设计与规格说明书.md) - 项目整体设计
-   [开发日志](开发日志.md) - 历史问题记录与解决过程

### 📊 调试技巧

添加详细的状态日志：

```typescript
async stop(): Promise<void> {
    console.log('🔍 ServiceClient.stop() called');
    console.log('🔍 Current isStarted:', this.isStarted);
    
    if (!this.isStarted) {
        console.log('🔍 Service not started, skipping stop');
        return;
    }

    console.log('🔍 Calling backend stop command...');
    await invoke('stop_service');
    console.log('✅ Stop command completed');
}
```

通过这些最佳实践，可以确保前端状态与后端服务状态保持一致，避免用户操作失效的问题。

---

## 📚 参考资料

- [Tauri IPC 通信文档](https://tauri.app/v1/guides/features/command)
- [异步状态管理最佳实践](https://javascript.info/async-await)
- [React 状态同步模式](https://reactjs.org/docs/lifting-state-up.html)
- [Tauri Sidecar 文档](https://tauri.app/v1/guides/building/sidecar)
- [Tokio 异步编程](https://tokio.rs/)
- [Python asyncio 文档](https://docs.python.org/3/library/asyncio.html)

---

**技术栈版本要求**：
-   Tauri: v1.5+
-   Rust: 1.70+
-   Python: 3.9+
-   Node.js: 18+
-   React: 18+