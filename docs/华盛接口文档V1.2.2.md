# ProApi 接口文档 V1.2.2

## 1. 整体介绍

1. 需要使用 Api 必须先登陆 Pro
2. Api 的交易数据与 Pro 共享
3. Api 连接地址为本地 ip：127.0.0.1，端口为：8080
4. 登陆 Api 的账号,密码与 Pro 的交易账号,密码一致
5. Api 所有接口需要 Token 字段做安全验证

## 2. 协议

1. 基于 Netty 的 TCP 长链接
2. 报文格式：通讯头（header） + 变长报文体（msgBody），数据报文体采用二进制格式
3. 通讯头：为变长报文体的长度，固定 4 字节(即 int32 转字节)
4. 变长报文体：为 json 字符串对应的字节

### 2.1 请求报文体（即变长报文体未转字节前）

**格式：** json

**属性：**

| 字段 | 必选 | 类型及范围 | 说明 |
|------|------|------------|------|
| RequestId | true | int | 请求标识 |
| RequestType | true | Int | 请求消息类型<br/>100. 登陆<br/>2: 资金<br/>3: 持仓<br/>5: 委托记录<br/>7: 下单<br/>8: 撤单 |
| Token | False | String | 登陆不需要 Token，其余接口均需要 Token 字段（Token 值为登陆 Api 返回 Token 值） |
| 其它参数 | False | | 不同的消息类型参数不同 |

### 2.2 请求参数

| 请求类型 | 是否必须 | 参数 | 参数类型 |
|----------|----------|------|----------|
| 登陆(100) | true | Account(账号) | string(字符串) |
| | | Password(密码) | string(字符串) |
| 资金(2) | false | 无 | |
| 持仓(3) | false | 无 | |
| 订单记录(5) | false | PageSize（每页行数） | int32（整型） |
| | | PageNo（页码） | int32（整型） |
| 下单(7) | true | StockCode（股票代码） | string(字符串) |
| | | Price(价格) | float(浮点型) |
| | | Qty(数量) | int32（整型） |
| | | Side(方向) | int32（整型） |
| | | OrderType(订单类型) | int32（整型） |
| 撤单(8) | true | EntrustNo(订单号) | string(字符串) |
| 客户端L2弹窗(9) | true | StockCode（股票代码） | string(字符串) |
| | false | ShowPrieAndQty(是否显示下单框价格、数量) | bool |
| | false | Price(下单框价格) | decimal |
| | false | Qty(下单框数量) | int |
| | false | OpenAutoSell（是否开启自动卖出） | bool |
| | false | AutoSellPrice(自动卖出框价格) | decimal |

### 2.3 响应报文体

**格式：** Json

**属性：**

| 字段 | 类型及范围 | 说明 |
|------|------------|------|
| RequestId | int | 请求唯一标识 |
| RequestType | int | 请求消息类型<br/>0: 推送回报<br/>2: 资金查询回报<br/>3: 持仓查询回报<br/>5: 订单查询回报<br/>7: 下单回报 |
| ResponseCode | int | 响应码 0 为正确，其它为错误码 |
| ResponseType | int | 响应格式类型<br/>1. RequestType=0<br/>ResponseType=101: 订单推送类型<br/>2. RequestType=2<br/>ResponseType=101：资金查询类型<br/>3. RequestType=3<br/>ResponseType=103：持仓查询类型<br/>4. RequestType=5<br/>ResponseType=101：订单查询类型 |
| ResponseMsg | String | 响应说明(错误说明) |
| Payload | Object | 响应实体，Json 对象，上面响应类型对应的对象类型 |

## 3. 接口数据查询、响应Demo、回报说明

### 3.1 登陆

**请求数据：**
```json
{
  "RequestId": 2,
  "RequestType": 100,
  "Account": "***********",
  "Password": "123456"
}
```

**响应数据：**
```json
{
  "RequestId": "2",
  "RequestType": 100,
  "ResponseCode": 0,
  "ResponseType": 100,
  "Account": "***********",
  "LoginResoult": true,
  "Token": "1bc67cdd-2dad-4be3-8806-9316d0d0b1ac",
  "ResponseMsg": "登录成功"
}
```

### 3.2 查询资金

**请求数据：**
```json
{
  "RequestId": 5,
  "RequestType": 2,
  "Token": "1bc67cdd-2dad-4be3-8806-9316d0d0b1ac"
}
```

**响应数据：**
```json
{
  "ResponseCode": 0,
  "RequestId": "Funds",
  "ResponseMsg": "查询交易员资金成功",
  "Payload": {
    "dayNetProfit": 0.0,
    "buyingPower": 938626.47,
    "dealerAccountId": 424,
    "frozenBalance": 0.0,
    "dayClosePositionProfit": -213221.68,
    "enableBalance": 355404.79,
    "dayMarketValue": 370000.0,
    "prevMarketValue": 370000.0
  },
  "RequestType": 2,
  "ResponseType": 101
}
```

**回报 Payload 字段说明：**

| 字段 | 类型及范围 | 说明 |
|------|------------|------|
| dayNetProfit | double | 当天盈亏 |
| buyingPower | double | 购买力 |
| dealerAccountId | int | 账号 id |
| frozenBalance | double | 冻结资金 |
| dayClosePositionProfit | double | 当日已平仓盈亏 |
| enableBalance | double | 可用金额 |
| dayMarketValue | double | 当日市值（不准确，建议自己计算） |
| prevMarketValue | double | 前一日收盘市值 |

### 3.3 查询持仓

**请求数据：**
```json
{
  "RequestId": 6,
  "RequestType": 3,
  "Token": "1bc67cdd-2dad-4be3-8806-9316d0d0b1ac"
}
```

**响应数据：**
```json
{
  "ResponseCode": 0,
  "RequestId": "Position",
  "ResponseMsg": "查询持仓成功",
  "Payload": [
    {
      "costValue": 381373.53,
      "dealerAccountId": 424,
      "realProfit": 0,
      "costPrice": 381.3735,
      "bossAccountId": 5,
      "stockCode": "00700.HK",
      "dealerAccountName": "姓名C",
      "bossAccountName": "姓名",
      "longQty": 1000,
      "stockName": "腾讯控股",
      "deliverPrice": 380.8,
      "bossLoginAccount": "***********",
      "frozenQty": 0,
      "dayClosePositionProfit": 0.0,
      "id": 3723,
      "channelCode": "fix_hk",
      "dealerLoginAccount": "***********"
    }
  ],
  "RequestType": 3,
  "ResponseType": 103
}
```

**回报 Payload 字段说明：**

| 字段 | 类型及范围 | 说明 |
|------|------------|------|
| costValue | double | 成本金额 |
| dealerAccountId | int | 交易账号 id |
| realProfit | double | 实际盈亏 |
| costPrice | double | 成本价 |
| bossAccountId | int | boss 账号 id |
| stockCode | string | 股票代码 |
| dealerAccountName | string | 交易员名字 |
| bossAccountName | string | boss 名字 |
| longQty | int | 持仓金额 |
| stockName | string | 股票名称 |
| deliverPrice | double | 平均成交价 |
| bossLoginAccount | string | boss 账号 |
| frozenQty | int | 冻结数量 |
| dayClosePositionProfit | double | 当日已平仓盈亏 |
| dealerLoginAccount | string | 交易账号 |

### 3.4 查询订单

**请求数据（不带页码，默认第一页）：**
```json
{
  "RequestId": 7,
  "RequestType": 5,
  "Token": "1bc67cdd-2dad-4be3-8806-9316d0d0b1ac"
}
```

**请求数据（带页码）：**
```json
{
  "RequestId": 7,
  "RequestType": 5,
  "PageSize": 10,
  "PageNo": 1,
  "Token": "1bc67cdd-2dad-4be3-8806-9316d0d0b1ac"
}
```

**响应数据：**
```json
{
  "ResponseCode": 0,
  "RequestId": "CancelList",
  "ResponseMsg": "查询订单成功",
  "Payload": {
    "hasPrev": false,
    "needTotalCount": true,
    "nextPage": 1,
    "pageSize": 10,
    "hasNext": false,
    "prevPage": 1,
    "totalCount": 2,
    "orderBySetted": false,
    "needData": true,
    "startIndex": 0,
    "endIndex": 10,
    "pageNo": 1,
    "dataList": [
      {
        "orderType": 2,
        "side": 1,
        "entrustQty": 100,
        "dealerAccountId": 424,
        "deliverQty": 0,
        "bossAccountId": 5,
        "entrustStatus": 10,
        "entrustTime": "2024-08-12T15:22:27.577",
        "stockCode": "01211.HK",
        "dealerAccountName": "姓名C",
        "bossAccountName": "姓名",
        "entrustNo": "9349249",
        "modifyTime": "2024-08-12T15:22:28",
        "stockName": "比亚迪股份",
        "deliverPrice": 0.0,
        "bossLoginAccount": "***********",
        "entrustPrice": 212.4,
        "id": 12324,
        "deliverFee": 0.0,
        "channelCode": "fix_hk",
        "dealerLoginAccount": "***********"
      }
    ],
    "totalPages": 1,
    "first": 0
  },
  "RequestType": 5,
  "ResponseType": 101
}
```

**回报 Payload 字段说明：**

| 字段 | 类型及范围 | 说明 |
|------|------------|------|
| orderType | int | 订单类型 |
| side | int | 方向（1为买入,2为卖出） |
| entrustQty | int | 委托数量 |
| dealerAccountId | string | 交易账号 id |
| deliverQty | int | 成交数量 |
| bossAccountId | string | boss 账号 id |
| entrustStatus | int | 订单状态 |
| entrustTime | string | 委托时间 |
| stockCode | string | 股票代码 |
| dealerAccountName | string | 交易员名称 |
| bossAccountName | string | boss 名称 |
| entrustNo | string | 订单号 |
| modifyTime | string | 订单最新修改时间 |
| stockName | string | 股票名称 |
| deliverPrice | double | 成交价 |
| bossLoginAccount | string | boss 账号 |
| entrustPrice | double | 委托价 |
| deliverFee | double | 交易费 |
| dealerLoginAccount | string | 交易员账号 |

### 3.5 下单

**请求数据：**
```json
{
  "RequestId": 8,
  "RequestType": 7,
  "StockCode": "00700",
  "Price": 300.0,
  "Qty": 100,
  "Side": 1,
  "OrderType": 3,
  "Token": "1bc67cdd-2dad-4be3-8806-9316d0d0b1ac"
}
```

**响应数据：**
```json
{
  "ResponseCode": 0,
  "RequestId": "8",
  "ResponseMsg": "下单请求成功",
  "Payload": {
    "entrustNo": "9349271"
  },
  "RequestType": 7,
  "ResponseType": 7
}
```

**回报 Payload 字段说明：**

| 字段 | 类型及范围 | 说明 |
|------|------------|------|
| entrustNo | string | 订单号 |

### 3.6 订单推送

**推送数据：**
```json
{
  "ResponseCode": 0,
  "ResponseMsg": "订单回报数据",
  "Payload": {
    "side": 1,
    "processStatus": 0,
    "avgPrice": 0,
    "entrustQty": 100,
    "cumQty": 0,
    "leavesQty": 100,
    "entrustStatus": 1,
    "stockCode": "700",
    "execId": "",
    "lastQty": 0,
    "entrustNo": "9349271",
    "modifyTime": "2024-08-12T18:01:52.589",
    "createTime": "2024-08-12T18:01:52.589",
    "success": true,
    "transactTime": "2024-08-12T18:01:52.589",
    "entrustPrice": 373.6,
    "execType": "",
    "channelCode": "fix_hk",
    "lastPrice": 0
  },
  "RequestType": 0,
  "ResponseType": 101
}
```

**回报 Payload 字段说明：**

| 字段 | 类型及范围 | 说明 |
|------|------------|------|
| side | int | 方向 |
| processStatus | int | 回报处理状态(可忽略) |
| avgPrice | double | 平均成交价 |
| entrustQty | int | 委托数量 |
| cumQty | int | 累计成交数量 |
| leavesQty | int | 剩余未成交数量 |
| entrustStatus | int | 订单状态 |
| stockCode | string | 股票代码 |
| execId | string | 执行 id（可忽略） |
| lastQty | int | 本次成交数量 |
| entrustNo | string | 订单号 |
| modifyTime | string | 最新修改时间 |
| createTime | string | 创建时间 |
| success | bool | 是否下单成功 |
| transactTime | String | 交易时间 |
| entrustPrice | double | 委托价格 |
| execType | string | 执行类型(可忽略) |
| lastPrice | double | 本次成交价格 |

### 3.7 ProL2弹窗

**请求数据：**
```json
{
  "RequestId": 3,
  "RequestType": 9,
  "ShowPrieAndQty": true,
  "StockCode": "00700",
  "Price": 488.6,
  "Qty": 100,
  "OpenAutoSell": true,
  "AutoSellPrice": 489.8,
  "Token": "5dddf200-b465-4354-a5a5-6edeac957445"
}
```

## 4. 枚举

### 4.1 买卖方向说明 (side)

| 值 | 说明 |
|----|------|
| 1 | 买入 |
| 2 | 卖出 |

### 4.2 订单状态说明 (ordStatus)

| 值 | 说明 |
|----|------|
| 1 | 未报 |
| 2 | 待报 |
| 3 | 已报 |
| 4 | 部分成交 |
| 5 | 已成交 |
| 6 | 撤销中 |
| 7 | 已撤销 |
| 8 | 部分待撤 |
| 9 | 部分撤销 |
| 10 | 已拒绝 |
| 11 | 已作废 |

### 4.3 订单类型说明 (ordType)

| 值 | 说明 |
|----|------|
| 0 | 竞价限价单 |
| 1 | 竞价单 |
| 2 | 增强限价单 |
| 3 | 限价单 |
| 4 | 特别限价单 |
| 5 | 市价单 |

## 5. 接口限制

### 每秒限制频率

| 接口类型 | 限制频率 次/s |
|----------|---------------|
| 下单 | 4 |
| 撤单 | 2 |
| 查询资金 | 4 |
| 查询订单 | 4 |
| 查询持仓 | 4 |

### 每30秒限制频率

| 接口类型 | 限制频率 次/30s |
|----------|-----------------|
| 下单 | 20 |
| 撤单 | 10 |
| 查询资金 | 30 |
| 查询订单 | 30 |
| 查询持仓 | 30 |
| L2弹窗 | 150 |