# 📚 项目文档索引

本目录包含量化交易终端的所有技术文档和说明。

## 📁 文档结构概览

```
docs/
├── 量化交易终端完整设计与规格说明书.md    # 🎯 主文档（设计规格）
├── 项目结构说明.md                        # 代码架构与模块组织
├── Tauri_Sidecar_通信方案完整指南.md      # 通信架构设计与使用指南
├── 富途牛牛与华盛通接口职责说明.md        # 双平台接口分工详解
├── 开发日志.md                            # 问题记录与解决方案
├── 故障排除指南.md                        # 运维故障排除
├── 构建部署指南.md                        # 构建与部署流程
├── 文档索引.md                            # 📍 本文档（导航索引）
└── frontend-development/                   # 🎨 前端开发专项文档
    ├── README.md                           # 前端开发文档导航
    ├── UI开发完整记录.md                   # UI开发全过程记录  
    ├── UI技术架构文档.md                   # 前端技术架构设计
    └── UI开发任务拆分与进度追踪.md         # 任务管理与进度追踪
```

## 📖 文档阅读指南

### 🎯 **新用户入门**
1. [量化交易终端完整设计与规格说明书.md](量化交易终端完整设计与规格说明书.md) - 了解项目整体设计
2. [项目结构说明.md](项目结构说明.md) - 熟悉代码组织结构
3. [README.md](../README.md) - 快速开始和环境搭建

### 🏗️ **架构理解**
- [Tauri_Sidecar_通信方案完整指南.md](Tauri_Sidecar_通信方案完整指南.md) - 通信架构设计与使用指南
- [富途牛牛与华盛通接口职责说明.md](富途牛牛与华盛通接口职责说明.md) - 接口分工详解

### 💻 **开发实践**
- [构建部署指南.md](构建部署指南.md) - 构建与部署流程
- [前端开发文档](frontend-development/) - UI开发完整指南

### 🔧 **问题解决**
- [故障排除指南.md](故障排除指南.md) - 常见问题和解决方案
- [开发日志.md](开发日志.md) - 历史问题记录与解决过程

## 📋 文档列表

### 核心文档

| 文档名称                                                                       | 描述                         | 状态    |
| ------------------------------------------------------------------------------ | ---------------------------- | ------- |
| [量化交易终端完整设计与规格说明书.md](量化交易终端完整设计与规格说明书.md)     | 项目完整设计与规格说明（主文档） | ✅ 完成 |
| [项目结构说明.md](项目结构说明.md)                                             | 详细的代码结构和组织         | ✅ 完成 |
| [Tauri_Sidecar_通信方案完整指南.md](Tauri_Sidecar_通信方案完整指南.md)     | 通信架构设计与使用指南 | ✅ 完成 |

### 功能文档

| 文档名称                                                           | 描述                         | 状态    |
| ------------------------------------------------------------------ | ---------------------------- | ------- |
| [富途牛牛与华盛通接口职责说明.md](富途牛牛与华盛通接口职责说明.md) | 富途牛牛与华盛通接口职责说明 | ✅ 完成 |

### 开发指南

| 文档名称                                           | 描述                       | 状态      |
| -------------------------------------------------- | -------------------------- | --------- |
| [README.md](../README.md)                          | 项目快速开始指南           | 🔄 已更新 |
| [故障排除指南.md](故障排除指南.md)                 | 常见问题和解决方案         | 🆕 新增   |
| [构建部署指南.md](构建部署指南.md)                 | 详细的构建和部署流程       | 🆕 新增   |
| [前端开发文档](frontend-development/)             | UI开发完整指南和技术架构   | 🎉 新增   |

### 技术文档

| 文档名称                                       | 描述                   | 状态    |
| ---------------------------------------------- | ---------------------- | ------- |
| [Tauri_Sidecar_通信方案完整指南.md](Tauri_Sidecar_通信方案完整指南.md) | 通信架构设计与使用指南 | ✅ 完成 |

### 开发文档

| 文档名称                   | 描述                           | 状态    |
| -------------------------- | ------------------------------ | ------- |
| [开发日志.md](开发日志.md) | 开发过程中的问题记录和解决方案 | 🆕 新增 |

## 📖 文档分类

### 🏗️ 架构设计

-   **量化交易终端完整设计与规格说明书** - 项目主文档，包含完整的设计和规格
-   **Tauri Sidecar 通信方案完整指南** - 通信架构设计与使用的完整指南

### 🔧 功能说明

-   **富途牛牛与华盛通接口职责说明** - 双平台接口职责分工详解

### 📋 使用指南

-   **项目结构说明** - 详细的代码结构和模块组织
-   **README** - 项目快速开始和环境搭建指南
-   **故障排除指南** - 常见问题诊断和解决方案
-   **构建部署指南** - 详细的构建和部署流程

### 📈 开发文档

-   **开发日志** - 开发过程中的问题记录和解决方案
-   **前端开发专项文档** - UI开发完整记录、技术架构、任务追踪

## 🔄 文档更新记录

| 日期       | 文档                               | 更新内容                                    |
| ---------- | ---------------------------------- | ------------------------------------------- |
| 2025-07-22 | 前端开发专项文档                   | 🎉 新增：UI开发完整记录、技术架构、任务追踪 |
| 2025-07-22 | Tauri_Sidecar_通信方案完整指南     | 🎉 新增：合并架构设计与使用指南为完整指南   |
| 2025-07-22 | 量化交易终端完整设计与规格说明书   | 🔄 更新：增强技术架构和接口规格章节内容     |
| 2025-07-22 | 文档索引                           | 🔄 更新：删除重复文档，优化文档结构         |
| 2025-07-22 | 开发日志                           | 🎉 重大更新：记录 Tauri Sidecar 问题完整解决方案 |
| 2025-07-21 | Tauri Sidecar 实时通信方案设计文档 | 🔄 更新：添加优雅停止机制和心跳任务管理 |
| 2025-07-21 | 通信封装使用指南                   | 🔄 更新：添加服务生命周期管理和心跳检测 |
| 2025-07-21 | 文档索引                           | 🔄 更新：调整文档结构，添加开发文档分类 |
| 2025-07-20 | 项目结构说明                       | 🆕 新增：详细的模块化代码结构说明       |
| 2025-07-20 | 项目详细说明                       | 🔄 更新：反映新的组件化架构和通信封装   |
| 2025-07-20 | 通信封装使用指南                   | 🔄 更新：新的标准化通信接口使用方法     |
| 2025-07-20 | 文档索引                           | 🔄 更新：添加新文档，更新状态标识       |
| 2025-07-20 | 富途牛牛与华盛通接口职责说明       | 文件重命名，更准确反映文档内容          |
| 2025-07-20 | 富途牛牛与华盛通接口职责说明       | 重写文档，详细说明双平台接口职责分工    |
| 2025-07-20 | 项目详细说明                       | 将 docs/README.md 重命名为更合适的名称  |
| 2025-07-20 | 所有文档                           | 将根目录文档移动到 docs 目录统一管理    |
| 2025-07-20 | 富途牛牛功能重构说明               | 完成富途功能重构，删除交易功能          |
| 2025-07-20 | Tauri Sidecar 方案                 | 完成 Sidecar 架构设计文档               |

## 📝 文档编写规范

### 文件命名

-   使用有意义的中文名称
-   避免特殊字符，使用下划线或连字符
-   添加适当的文件扩展名 (.md)

### 内容结构

-   使用清晰的标题层级 (H1-H6)
-   添加目录索引 (TOC)
-   使用代码块和示例
-   添加图表和流程图 (如需要)

### 格式规范

-   使用 Markdown 格式
-   代码块指定语言类型
-   使用表格整理信息
-   添加适当的 emoji 图标

## 🔍 如何查找文档

### 按功能查找

-   **架构设计** → 量化交易终端设计规格书、Tauri Sidecar 通信方案
-   **接口分工** → 富途牛牛与华盛通接口职责说明
-   **开发指南** → README、项目结构说明、构建部署指南
-   **问题解决** → 故障排除指南、开发日志

### 按类型查找

-   **设计文档** → 量化交易终端设计规格书、通信架构方案
-   **使用文档** → README、构建部署指南、故障排除指南
-   **技术文档** → Tauri Sidecar 通信方案、项目结构说明
-   **开发文档** → 开发日志、问题记录

## 📞 文档反馈

如果您发现文档有以下问题，请及时反馈：

-   📝 内容错误或过时
-   🔗 链接失效
-   📖 描述不清楚
-   🆕 需要补充新内容

反馈方式：

-   内部沟通渠道
-   邮件联系
-   直接反馈给开发团队

## 🎯 待完善文档

以下文档需要后续补充：

-   [ ] 华盛通功能详细说明
-   [ ] API 接口文档
-   [ ] 测试用例文档
-   [ ] 性能优化指南
-   [ ] Python 服务开发指南

## 🔄 最新文档更新记录

| 日期       | 文档             | 更新内容                                              |
| ---------- | ---------------- | ----------------------------------------------------- |
| 2025-07-22 | 前端开发专项文档 | 🎉 新增：UI开发完整记录、技术架构、任务追踪等4个文档 |
| 2025-07-22 | 开发日志         | 🎉 重大更新：记录 Tauri Sidecar 问题完整解决方案     |
| 2025-07-22 | 构建部署指南     | 🔄 更新：简化为实用的构建指导，移除已解决问题描述     |
| 2025-07-22 | 故障排除指南     | 🔄 更新：重写为实用的故障排除指南，聚焦当前问题       |
| 2025-07-22 | README.md        | 🔄 更新：简化快速开始流程，突出一键构建功能           |

---

📅 **最后更新**: 2025-07-22  
👤 **维护者**: 开发团队  
📧 **联系方式**: 内部邮箱或直接联系
