## 🏷️ UI设计风格 - 弹出框风格

### 📋 风格概述
"弹出框风格"是专为模态对话框和配置界面设计的现代化UI风格，强调层次清晰、信息密度适中、交互友好。适用于复杂的配置界面和多步骤表单。

### 🎯 设计原则
- **信息层次化**: 通过容器嵌套和色彩对比创建清晰的信息层次
- **功能区块化**: 每个功能模块独立设计，便于理解和操作
- **视觉简洁性**: 去除不必要的装饰，专注于内容和功能
- **交互一致性**: 统一的交互模式和视觉反馈
- **🚨 文字可见性保证**: 所有文字必须有足够的颜色对比度，禁止白字与浅色背景组合

### 🔴 **强制性文字颜色规范**

#### 必须遵守的规则：
1. **所有文字颜色必须使用 `!important` 声明**
2. **禁止白色文字与白色/浅灰背景组合**
3. **颜色对比度必须 ≥ 4.5:1**
4. **不得依赖颜色继承，每个组件明确定义**

#### 标准文字颜色：
```css
/* 主要文字 */
color: #495057 !important;

/* 次要文字 */  
color: #6c757d !important;

/* 辅助文字 */
color: #868e96 !important;

/* 禁用状态 */
color: #adb5bd !important;
```

详细规范请查看：`.claude/specs/text-color-guidelines.md`

### 📐 核心布局结构

#### 1. 页面整体结构
```jsx
<div className="tab-content">
    {/* 区域标题 */}
    <div style={{ display: 'flex', alignItems: 'flex-start', justifyContent: 'space-between', marginBottom: '15px' }}>
        <div style={{ flex: 1 }}>
            <h4 style={{ margin: 0, display: 'flex', alignItems: 'center', color: '#495057' }}>
                主标题
                <span className="required-asterisk" style={{ marginLeft: '5px' }}> *</span>
            </h4>
            <p style={{ color: '#666', fontSize: '13px', margin: '5px 0 0 0', lineHeight: '1.4' }}>
                功能描述说明文字
            </p>
        </div>
    </div>

    {/* 主配置容器 */}
    <div style={{ 
        background: '#f8f9fa', 
        border: '1px solid #e9ecef', 
        borderRadius: '6px', 
        padding: '20px' 
    }}>
        {/* 内容区域 */}
    </div>
</div>
```

#### 2. 区域分隔 (多区域时使用)
```jsx
{/* 第二个区域 */}
<div style={{ marginTop: '30px', borderTop: '1px solid #eee', paddingTop: '20px' }}>
    {/* 标题 + 主容器结构同上 */}
</div>
```

### 📦 容器系统

#### 1. 主配置容器
```css
/* 灰色主容器 - 所有内容的承载区域 */
background: '#f8f9fa'
border: '1px solid #e9ecef'
borderRadius: '6px'
padding: '20px'
```

#### 2. 表单元素卡片
```css
/* 白色内容卡片 - 策略详情等重要内容 */
background: 'white'
border: '1px solid #dee2e6'
padding: '15px'
borderRadius: '4px'
boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
marginBottom: '15px'
```

#### 3. 表单字段容器
```css
/* 表单组样式 */
.form-group {
    marginBottom: '15px'
}

/* 表单标签 */
label {
    fontWeight: 'bold'
    marginBottom: '10px'
    display: 'block'
}

/* 帮助文字 */
.field-help {
    fontSize: '12px'
    color: '#868e96'
    marginTop: '5px'
}
```

### 🎨 空状态设计

#### 1. 标准空状态
```jsx
<div style={{ 
    textAlign: 'center', 
    color: '#6c757d', 
    padding: '30px',
    background: 'white',
    border: '2px dashed #dee2e6',
    borderRadius: '6px',
    marginTop: '15px'
}}>
    <div style={{ fontSize: '48px', marginBottom: '10px' }}>📋</div>
    <div style={{ fontWeight: '500' }}>主要提示文字</div>
    <div style={{ fontSize: '12px', marginTop: '5px', color: '#868e96' }}>次要说明文字</div>
</div>
```

#### 2. 空状态图标映射
- **📋** 选择/配置类空状态
- **💤** 功能未启用状态  
- **⚠️** 缺少必要条件状态

### 🔘 表单控件规范

#### 1. 单选按钮组
```jsx
<div className="radio-group">
    <label className="radio-label" style={{ 
        display: 'flex', 
        alignItems: 'center', 
        marginBottom: '8px', 
        cursor: 'pointer' 
    }}>
        <input
            type="radio"
            style={{ 
                outline: 'none',
                boxShadow: 'none'
            }}
        />
        <span>选项文字</span>
    </label>
</div>
```

#### 2. 下拉选择框
```css
/* 全宽下拉框 */
select {
    width: '100%'
}

/* 禁用选项样式 */
option:disabled {
    color: '#999'
}
```

#### 3. 输入框组合
```jsx
/* 并排输入框 */
<div className="stock-input-group">
    <input style={{ flex: '1', marginRight: '10px' }} />
    <input style={{ flex: '1' }} />
</div>
```

#### 4. 状态开关设计
```jsx
<label style={{ 
    display: 'flex', 
    alignItems: 'center', 
    cursor: 'pointer', 
    userSelect: 'none' 
}}>
    <input type="checkbox" style={{ marginRight: '8px' }} />
    <span style={{ 
        fontWeight: enabled ? 'bold' : 'normal',
        color: enabled ? '#2ed573' : '#666'
    }}>
        {enabled ? '已启用' : '未启用'}
    </span>
</label>
```

### 🎯 色彩系统

#### 1. 主要文字颜色
- **深灰色**: `#495057` (标题)
- **中灰色**: `#666` (描述文字)  
- **浅灰色**: `#6c757d` (次要文字)
- **更浅灰**: `#868e96` (帮助文字)

#### 2. 背景色系
- **主容器**: `#f8f9fa` (浅灰背景)
- **卡片**: `white` (纯白背景)
- **边框**: `#e9ecef`, `#dee2e6` (不同层级边框)

#### 3. 状态颜色
- **必填标识**: 继承系统红色
- **成功状态**: `#2ed573`
- **警告状态**: `#fff3cd` 背景 + `#856404` 文字
- **删除按钮**: `#dc3545`

### 📏 间距规范

#### 1. 容器间距
- **区域间隔**: `marginTop: '30px'` + `borderTop: '1px solid #eee'` + `paddingTop: '20px'`
- **标题到内容**: `marginBottom: '15px'`
- **主容器内边距**: `padding: '20px'`
- **卡片内边距**: `padding: '15px'`
- **空状态内边距**: `padding: '30px'`

#### 2. 元素间距
- **表单字段间**: `marginBottom: '15px'`
- **卡片间**: `marginBottom: '10px'` 或 `marginBottom: '15px'`
- **帮助文字边距**: `marginTop: '5px'`

#### 3. 边框圆角
- **主容器**: `borderRadius: '6px'`
- **卡片**: `borderRadius: '4px'`
- **按钮**: `borderRadius: '3px'`

### 🔨 实用工具类

#### 1. 必填字段标识
```jsx
<span className="required-asterisk" style={{ marginLeft: '5px' }}> *</span>
```

#### 2. 帮助文字
```jsx
<small className="field-help">帮助说明文字</small>
```

#### 3. 表单字段组
```jsx
<div className="form-group">
    <label style={{ fontWeight: 'bold', marginBottom: '10px', display: 'block' }}>
        字段标签
        <span className="required-asterisk"> *</span>
    </label>
    <input style={{ width: '100%' }} />
    <small className="field-help">帮助文字</small>
</div>
```

#### 4. 危险操作按钮
```jsx
<button style={{
    background: '#dc3545',
    color: 'white',
    border: 'none',
    padding: '4px 12px',
    borderRadius: '3px',
    cursor: 'pointer',
    fontSize: '12px'
}}>
    删除
</button>
```

### ✨ 交互细节

#### 1. 可点击元素
- 所有可交互元素添加 `cursor: 'pointer'`
- 单选按钮去除默认 `outline` 和 `boxShadow`
- label 元素支持点击切换状态

#### 2. 视觉反馈
- 卡片使用轻微阴影营造层次感
- 空状态使用虚线边框区分不同状态
- 文字字重变化表示状态差异

### 🎪 应用场景
此风格适用于：
- ✅ 配置对话框
- ✅ 多步骤表单
- ✅ 设置界面
- ✅ 数据录入页面
- ✅ 向导流程

### 📝 使用说明
当需要使用"弹出框风格"时，请按照以上规范进行设计，确保：
1. 保持容器层次的一致性
2. 使用统一的色彩和间距规范  
3. 遵循表单元素的标准样式
4. 为空状态选择合适的图标和文案