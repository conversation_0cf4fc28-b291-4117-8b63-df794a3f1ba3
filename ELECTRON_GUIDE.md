# Electron 交易系统使用指南

## 🚀 快速开始

### 1. 环境要求
- Node.js 16+
- Yarn 或 NPM
- TypeScript 4.5+

### 2. 安装依赖

```bash
# 安装前端依赖
yarn install

# 安装 Electron 主进程依赖
cd electron-main
yarn install
cd ..
```

### 3. 开发模式启动

```bash
# 启动前端开发服务器
yarn dev

# 在另一个终端启动 Electron 主进程
cd electron-main
yarn dev
```

或者使用集成启动脚本：
```bash
# 同时启动前端和 Electron
yarn electron:dev
```

## 🔧 系统架构

### 前端组件
- **Dashboard**: 主界面，自动检测环境选择合适的Hook
- **useElectronDashboard**: Electron专用Hook，与主进程IPC通信
- **electronTradingClient**: Electron通信客户端

### 主进程组件
- **TradingSystem**: 核心交易系统
- **TaskManager**: 任务管理器
- **MarketDataManager**: 行情数据管理
- **TradingManager**: 交易管理
- **IPC Handlers**: 进程间通信处理器

## 🎯 功能特性

### ✅ 已实现功能
- 交易系统初始化和状态监控
- 任务创建、启动、停止、删除
- 实时系统状态更新
- 环境自动检测和适配
- 完整的TypeScript类型支持

### 🚧 开发中功能
- 任务更新/编辑
- 实时行情数据推送
- 风险管理规则执行
- 策略执行监控
- 交易执行和仓位管理

## 🛠️ 开发说明

### 环境检测
系统会自动检测运行环境：
- **Electron环境**: 使用 `useElectronDashboard` 和真实交易系统
- **Tauri环境**: 使用 `useDashboard` 和Tauri API
- **浏览器环境**: 使用 `useDashboard` 和模拟数据

### IPC 通信
前端通过以下API与主进程通信：
```typescript
// 交易系统管理
window.electronAPI.tradingSystem.getStatus()
window.electronAPI.tradingSystem.initialize()
window.electronAPI.tradingSystem.shutdown()

// 任务管理
window.electronAPI.task.create(config)
window.electronAPI.task.start({taskId})
window.electronAPI.task.stop({taskId})
window.electronAPI.task.delete({taskId})
window.electronAPI.task.getList()
```

### 事件监听
系统支持以下实时事件：
- `trading-system:heartbeat`: 系统心跳
- `trading-system:initialized`: 系统初始化完成
- `trading-system:task-created`: 任务创建
- `trading-system:task-started`: 任务启动
- `trading-system:task-stopped`: 任务停止

## 🧪 测试

### 单元测试
```bash
cd electron-main
npm test
```

### 集成测试
```bash
cd electron-main
./scripts/test-integration.sh
```

### E2E测试
```bash
# 启动完整应用进行测试
yarn test:e2e
```

## 📝 配置说明

### 交易系统配置
配置文件: `electron-main/src/trading/config.ts`

```typescript
export const DEVELOPMENT_CONFIG = {
    market: {
        host: '127.0.0.1',
        port: 33333,                    // 富途WebSocket端口
        useHttps: false,
        encryptKey: '232df6441fb2bbdd'   // WebSocket密钥
    },
    trading: {
        huasheng: {
            host: '*************',
            port: 7777,
            account: process.env.HUASHENG_ACCOUNT || 'your_account',
            password: process.env.HUASHENG_PASSWORD || 'your_password'
        }
    }
};
```

### 环境变量
创建 `.env` 文件：
```bash
# 华盛通配置
HUASHENG_ACCOUNT=your_account
HUASHENG_PASSWORD=your_password

# 富途配置
FUTU_ENCRYPT_KEY=232df6441fb2bbdd
FUTU_HOST=127.0.0.1
FUTU_PORT=33333

# 系统配置
NODE_ENV=development
LOG_LEVEL=info
```

## 🐛 故障排除

### 常见问题

#### 1. 交易系统初始化失败
- 检查富途OpenD是否启动
- 验证WebSocket端口和密钥配置
- 查看控制台错误日志

#### 2. 任务创建失败
- 确认交易系统已初始化
- 检查任务配置参数
- 验证股票代码格式

#### 3. IPC通信错误
- 确认preload脚本正确加载
- 检查IPC通道名称拼写
- 验证参数格式

### 调试技巧

#### 1. 启用详细日志
```bash
export LOG_LEVEL=debug
yarn electron:dev
```

#### 2. 打开开发者工具
- 在应用中按 `Ctrl+Shift+I` (Windows/Linux) 或 `Cmd+Option+I` (Mac)
- 或使用菜单：窗口 → 切换开发者工具

#### 3. 查看主进程日志
```bash
# 主进程日志会输出到启动的终端
cd electron-main
yarn dev
```

## 📊 性能优化

### 建议的配置
- 任务更新间隔: 5秒
- 系统状态检查: 5秒
- 实时数据缓存: 1000条
- 最大并发任务数: 10个

### 监控指标
- 内存使用量
- CPU使用率
- IPC消息频率
- 任务执行状态

## 🔗 相关文档

- [TypeScript编译错误修复记录](../electron-main/README.md)
- [配置清理记录](../electron-main/CONFIG_CLEANUP.md)
- [任务管理系统架构](./docs/架构设计.md)
- [IPC通信协议](./docs/IPC协议.md)

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交 Pull Request

## 📜 许可证

MIT License - 详见 [LICENSE](../LICENSE) 文件