:root {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
    line-height: 1.5;
    font-weight: 400;

    color-scheme: light dark;
    color: rgba(255, 255, 255, 0.87);
    background-color: #242424;

    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    -webkit-text-size-adjust: 100%;
}

/* 全局滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

::-webkit-scrollbar-track {
    background: transparent;
}

::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
    transition: background 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 0, 0, 0.4);
}

::-webkit-scrollbar-corner {
    background: transparent;
}

/* Firefox 滚动条样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

body {
    margin: 0;
    display: flex;
    place-items: center;
    min-width: 320px;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

#root {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
}

.app {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    color: #333;
}

/* 标题栏样式 */
.header {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.title {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2563eb;
}

.title-icon {
    width: 28px;
    height: 28px;
}

.status {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.status-indicator {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-indicator.connected {
    background: #10b981;
    color: white;
}

.status-indicator.disconnected {
    background: #ef4444;
    color: white;
}

.test-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #2563eb;
    border-radius: 6px;
    background: white;
    color: #2563eb;
    cursor: pointer;
    font-size: 0.875rem;
    transition: all 0.2s;
}

.test-btn:hover:not(:disabled) {
    background: #2563eb;
    color: white;
}

.test-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 主要内容区域 */
.main {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
}

.dashboard {
    max-width: 1200px;
    margin: 0 auto;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 快速操作面板 */
.quick-actions h2 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.25rem;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-icon {
    width: 32px;
    height: 32px;
    color: #2563eb;
    margin-bottom: 1rem;
}

.action-card h3 {
    font-size: 1.1rem;
    margin-bottom: 0.5rem;
    color: #1f2937;
}

.action-card p {
    color: #6b7280;
    margin-bottom: 1rem;
    font-size: 0.875rem;
}

.action-btn {
    width: 100%;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    background: #2563eb;
    color: white;
    cursor: pointer;
    font-size: 0.875rem;
    font-weight: 500;
    transition: all 0.2s;
}

.action-btn:hover:not(:disabled) {
    background: #1d4ed8;
}

.action-btn:disabled {
    background: #9ca3af;
    cursor: not-allowed;
}

/* 状态信息 */
.status-section h2 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.25rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.status-card {
    background: white;
    padding: 1rem;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.status-card h4 {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.status-text {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
}

.status-text.connected {
    color: #10b981;
}

.status-text.error {
    color: #ef4444;
}

/* 数据展示区域 */
.data-section {
    margin-top: 2rem;
}

.data-section h2 {
    margin-bottom: 1rem;
    color: #1f2937;
    font-size: 1.25rem;
}

.data-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.data-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.data-card h4 {
    font-size: 1rem;
    color: #1f2937;
    margin-bottom: 1rem;
    font-weight: 600;
}

.data-content p {
    margin-bottom: 0.5rem;
    color: #374151;
    font-size: 0.875rem;
}

.data-content strong {
    color: #1f2937;
}

/* 资金信息样式 */
.fund-info {
    background: #f3f4f6;
    padding: 0.75rem;
    border-radius: 6px;
    margin: 0.75rem 0;
}

.fund-info p {
    margin: 0.25rem 0;
    font-size: 0.8rem;
    color: #374151;
}

/* 持仓信息样式 */
.position-info {
    background: #f3f4f6;
    padding: 0.75rem;
    border-radius: 6px;
    margin: 0.75rem 0;
}

.position-info p {
    margin: 0.25rem 0;
    font-size: 0.8rem;
    color: #374151;
}

.position-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.position-item:last-child {
    border-bottom: none;
}

.position-item p {
    margin: 0.25rem 0;
}
