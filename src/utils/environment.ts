/**
 * Electron 环境检测工具
 * 用于判断当前运行环境，确保在正确环境中调用API
 */

/**
 * 检测当前是否运行在Electron环境中
 */
export const isElectronEnvironment = (): boolean => {
    return typeof window !== 'undefined' && window.electronAPI !== undefined;
};

/**
 * 检查当前是否在浏览器开发环境中
 * @returns {boolean} 如果在浏览器环境中返回true
 */
export function isBrowserEnvironment(): boolean {
    return !isElectronEnvironment();
}

/**
 * 获取当前运行环境类型
 */
export const getEnvironmentType = (): 'electron' | 'browser' => {
    if (isElectronEnvironment()) {
        return 'electron';
    }
    return 'browser';
};

/**
 * 安全的Electron API调用 - 只有在Electron环境下才会执行
 */
export const safeElectronCall = async <T>(
    apiCall: () => Promise<T>,
    fallbackValue?: T
): Promise<T | undefined> => {
    if (isElectronEnvironment()) {
        try {
            return await apiCall();
        } catch (error) {
            console.error('Electron API调用失败:', error);
            return fallbackValue;
        }
    }
    console.warn('当前不在Electron环境中，跳过API调用');
    return fallbackValue;
};

/**
 * 打印环境信息
 */
export function logEnvironmentInfo(): void {
    const environment = getEnvironmentType();
    switch (environment) {
        case 'electron':
            console.log('⚡ 当前运行在Electron桌面应用环境中');
            break;
        case 'browser':
            console.log('🌐 当前运行在浏览器环境中');
            break;
        default:
            console.log('❓ 未知的运行环境');
    }
}