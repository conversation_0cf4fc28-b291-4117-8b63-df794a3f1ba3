// 交易演示页面 - 展示新的通信封装使用方式
import React from 'react';
import { useTradingDemo } from '@/hooks';
import {
    AdapterStatus,
    ControlPanel,
    DataQueryPanel,
    DataDisplay,
    RealtimeDataStream,
    LoadingOverlay
} from '@/components';

const TradingDemo: React.FC = () => {
    const {
        futuConnected,
        huashengConnected,
        loading,
        quote,
        funds,
        realtimeData,
        startFutuAdapter,
        startHuashengAdapter,
        connectFutu,
        connectHuasheng,
        getFutuQuote,
        getHuashengFunds,
        subscribeFutuRealtime,
        placeFutuOrder
    } = useTradingDemo();

    return (
        <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
            <h1>🚀 新通信封装演示</h1>

            <AdapterStatus
                futuConnected={futuConnected}
                huashengConnected={huashengConnected}
            />

            <ControlPanel
                futuConnected={futuConnected}
                huashengConnected={huashengConnected}
                loading={loading}
                onStartFutu={startFutuAdapter}
                onStartHuasheng={startHuashengAdapter}
                onConnectFutu={connectFutu}
                onConnectHuasheng={connectHuasheng}
            />

            <DataQueryPanel
                futuConnected={futuConnected}
                huashengConnected={huashengConnected}
                loading={loading}
                onGetFutuQuote={getFutuQuote}
                onGetHuashengFunds={getHuashengFunds}
                onSubscribeFutuRealtime={subscribeFutuRealtime}
                onPlaceFutuOrder={placeFutuOrder}
            />

            <DataDisplay quote={quote} funds={funds} />

            <RealtimeDataStream realtimeData={realtimeData} />

            <LoadingOverlay loading={loading} />
        </div>
    );
};

export default TradingDemo;
