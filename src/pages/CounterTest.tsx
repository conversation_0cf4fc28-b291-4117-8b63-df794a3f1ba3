import React, { useState, useEffect } from 'react';
import { electronTradingClient } from '@/communication';

// 简化的类型定义，用于演示
interface CounterData {
    counter: number;
    timestamp: number;
}

interface CounterResponse {
    success: boolean;
    data?: any;
    message?: string;
}

export const CounterTest: React.FC = () => {
    const [isStarted, setIsStarted] = useState(false);
    const [counterData, setCounterData] = useState<CounterData | null>(null);
    const [status, setStatus] = useState<string>('未启动');
    const [loading, setLoading] = useState(false);
    const [logs, setLogs] = useState<string[]>([]);

    const addLog = (message: string) => {
        const timestamp = new Date().toLocaleTimeString();
        setLogs(prev => [...prev.slice(-9), `[${timestamp}] ${message}`]);
    };

    useEffect(() => {
        // 检查系统状态
        const checkSystemStatus = async () => {
            try {
                const response = await electronTradingClient.getTradingSystemStatus();
                if (response.success && response.data?.initialized) {
                    setIsStarted(true);
                    setStatus('系统已初始化');
                    addLog('交易系统已初始化');
                } else {
                    setIsStarted(false);
                    setStatus('系统未初始化');
                    addLog('交易系统未初始化');
                }
            } catch (error) {
                console.error('检查系统状态失败:', error);
                setStatus('检查失败');
                addLog(`状态检查失败: ${error}`);
            }
        };

        // 设置实时数据监听
        electronTradingClient.onSystemHeartbeat((data) => {
            if (data) {
                const mockCounter = {
                    counter: data.activeTasksCount || 0,
                    timestamp: Date.now() / 1000
                };
                setCounterData(mockCounter);
                addLog(`系统心跳: 活跃任务数 ${mockCounter.counter}`);
            }
        });

        checkSystemStatus();

        // 定期检查状态
        const statusInterval = setInterval(checkSystemStatus, 5000);

        return () => {
            clearInterval(statusInterval);
        };
    }, []);

    const handleStart = async () => {
        setLoading(true);
        try {
            addLog('正在初始化交易系统...');
            const response = await electronTradingClient.initializeTradingSystem();
            
            if (response.success) {
                setIsStarted(true);
                setStatus('系统已初始化');
                addLog('交易系统初始化成功');
            } else {
                setStatus('初始化失败');
                addLog(`初始化失败: ${response.message}`);
            }
        } catch (error) {
            addLog(`初始化失败: ${error}`);
            setStatus('初始化失败');
        } finally {
            setLoading(false);
        }
    };

    const handleStop = async () => {
        setLoading(true);
        try {
            addLog('正在关闭交易系统...');
            const response = await electronTradingClient.shutdownTradingSystem();
            
            if (response.success) {
                setIsStarted(false);
                setStatus('系统已关闭');
                setCounterData(null);
                addLog('交易系统已关闭');
            } else {
                addLog(`关闭失败: ${response.message}`);
            }
        } catch (error) {
            addLog(`关闭失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handlePing = async () => {
        setLoading(true);
        try {
            const response = await electronTradingClient.getTradingSystemStatus();
            addLog(`系统状态响应: ${JSON.stringify(response.data)}`);
        } catch (error) {
            addLog(`获取状态失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handleGetCounter = async () => {
        setLoading(true);
        try {
            const response = await electronTradingClient.getTaskList();
            if (response.success) {
                const taskCount = response.data?.length || 0;
                addLog(`当前任务数量: ${taskCount}`);
            } else {
                addLog(`获取任务列表失败: ${response.message}`);
            }
        } catch (error) {
            addLog(`获取任务列表失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handleResetCounter = async () => {
        setLoading(true);
        try {
            // 模拟重置操作 - 重新初始化系统
            await handleStop();
            setTimeout(async () => {
                await handleStart();
                addLog('系统已重置');
            }, 1000);
        } catch (error) {
            addLog(`重置失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    const handleGetStatus = async () => {
        setLoading(true);
        try {
            const response = await electronTradingClient.getTradingSystemStatus();
            addLog(`详细状态: ${JSON.stringify(response, null, 2)}`);
        } catch (error) {
            addLog(`获取详细状态失败: ${error}`);
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="p-6 max-w-4xl mx-auto">
            <h1 className="text-2xl font-bold mb-6">计数器服务测试</h1>
            
            {/* 状态显示 */}
            <div className="bg-gray-100 p-4 rounded-lg mb-6">
                <div className="grid grid-cols-2 gap-4">
                    <div>
                        <span className="font-semibold">服务状态: </span>
                        <span className={`px-2 py-1 rounded text-sm ${
                            isStarted ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                            {status}
                        </span>
                    </div>
                    <div>
                        <span className="font-semibold">当前计数: </span>
                        <span className="text-xl font-mono">
                            {counterData ? counterData.counter : '--'}
                        </span>
                    </div>
                </div>
                {counterData && (
                    <div className="mt-2 text-sm text-gray-600">
                        最后更新: {new Date(counterData.timestamp * 1000).toLocaleTimeString()}
                    </div>
                )}
            </div>

            {/* 控制按钮 */}
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                <button
                    onClick={handleStart}
                    disabled={loading || isStarted}
                    className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {loading ? '启动中...' : '启动服务'}
                </button>
                
                <button
                    onClick={handleStop}
                    disabled={loading || !isStarted}
                    className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    {loading ? '停止中...' : '停止服务'}
                </button>
                
                <button
                    onClick={handlePing}
                    disabled={loading || !isStarted}
                    className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    Ping 测试
                </button>
                
                <button
                    onClick={handleGetCounter}
                    disabled={loading || !isStarted}
                    className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    获取计数器
                </button>
                
                <button
                    onClick={handleResetCounter}
                    disabled={loading || !isStarted}
                    className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    重置计数器
                </button>
                
                <button
                    onClick={handleGetStatus}
                    disabled={loading}
                    className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    获取状态
                </button>
            </div>

            {/* 日志显示 */}
            <div className="bg-black text-green-400 p-4 rounded-lg font-mono text-sm">
                <div className="mb-2 font-semibold">操作日志:</div>
                <div className="h-48 overflow-y-auto">
                    {logs.length === 0 ? (
                        <div className="text-gray-500">暂无日志...</div>
                    ) : (
                        logs.map((log, index) => (
                            <div key={index} className="mb-1">
                                {log}
                            </div>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
};
