import React, { useState } from 'react';
import { useElectronDashboard } from '@/hooks/useElectronDashboard';
import { isElectronEnvironment } from '@/utils/environment';
import { GlobalControls } from '@/components/Dashboard/GlobalControls';
import { TaskList } from '@/components/Dashboard/TaskList';
import { TaskConfigModal } from '@/components/Dashboard/TaskConfigModal';
import { TaskDetailsModal } from '@/components/Dashboard/TaskDetailsModal';
import { TaskLogModal } from '@/components/Dashboard/TaskLogModal';
import { MarketConfigModal } from '@/components/Dashboard/MarketConfigModal';
import { TradingConfigModal } from '@/components/Dashboard/TradingConfigModal';
import { LoadingOverlay } from '@/components';
import { LogEntry } from '@/types';

export const Dashboard: React.FC = () => {
    // 检测环境
    const isElectron = isElectronEnvironment();
    
    // 日志模态框状态
    const [showLogModal, setShowLogModal] = useState(false);
    const [currentTaskLogs, setCurrentTaskLogs] = useState<LogEntry[]>([]);
    const [logModalTask, setLogModalTask] = useState<any>(null);

    // 配置模态框状态
    const [showMarketConfigModal, setShowMarketConfigModal] = useState(false);
    const [showTradingConfigModal, setShowTradingConfigModal] = useState(false);

    // 使用Electron专用的Hook - 必须在所有条件判断之前调用
    const {
        // 状态
        tasks,
        globalStatus,
        isLoading,
        selectedTask,
        showConfigModal,
        showDetailsModal,
        
        // 操作函数
        startAllTasks,
        stopAllTasks,
        deleteTask,
        toggleTask,
        liquidateTask,
        copyTask,
        getTaskLogs,
        
        // Modal 控制
        openConfigModal,
        closeConfigModal,
        openDetailsModal,
        closeDetailsModal,
        
        // 配置相关
        saveTaskConfig,
        editTask
    } = useElectronDashboard();

    // 如果不在 Electron 环境中，显示提示信息
    if (!isElectron) {
        return (
            <div className="min-h-screen bg-gray-50 flex items-center justify-center">
                <div className="max-w-md mx-auto text-center p-8 bg-white rounded-lg shadow-lg">
                    <div className="text-6xl mb-4">🖥️</div>
                    <h1 className="text-2xl font-bold text-gray-800 mb-4">
                        量化交易终端
                    </h1>
                    <p className="text-gray-600 mb-6">
                        此应用需要在 Electron 桌面环境中运行以访问完整的交易功能。
                    </p>
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                        <h3 className="font-semibold text-blue-800 mb-2">如何启动桌面版：</h3>
                        <code className="text-sm bg-blue-100 px-2 py-1 rounded">
                            yarn electron:dev
                        </code>
                    </div>
                    <p className="text-sm text-gray-500">
                        当前运行在浏览器环境中
                    </p>
                </div>
            </div>
        );
    }

    // 处理查看日志
    const handleViewLogs = async (taskId: string) => {
        const task = tasks.find(t => t.id === taskId);
        if (!task) return;

        try {
            const logs = await getTaskLogs(taskId);
            setCurrentTaskLogs(logs);
            setLogModalTask(task);
            setShowLogModal(true);
        } catch (error) {
            console.error('获取任务日志失败:', error);
            alert('获取日志失败，请重试');
        }
    };

    // 关闭日志模态框
    const handleCloseLogModal = () => {
        setShowLogModal(false);
        setCurrentTaskLogs([]);
        setLogModalTask(null);
    };

    // 处理行情配置保存
    const handleSaveMarketConfig = async (marketConfig: any) => {
        try {
            const response = await window.electronAPI.config.update({
                updates: { market: marketConfig }
            });
            
            if (response.success) {
                alert('行情配置已保存，重启应用后生效');
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error('保存行情配置失败:', error);
            throw error;
        }
    };

    // 处理交易配置保存
    const handleSaveTradingConfig = async (tradingConfig: any) => {
        try {
            const response = await window.electronAPI.config.update({
                updates: { trading: tradingConfig }
            });
            
            if (response.success) {
                alert('交易配置已保存，重启应用后生效');
            } else {
                throw new Error(response.message);
            }
        } catch (error) {
            console.error('保存交易配置失败:', error);
            throw error;
        }
    };

    return (
        <div className="dashboard">
            {/* 全局控制区 */}
            <GlobalControls
                globalStatus={globalStatus}
                onAddTask={() => openConfigModal()}
                onStartAll={startAllTasks}
                onStopAll={stopAllTasks}
                isLoading={isLoading}
                onMarketConfigClick={() => setShowMarketConfigModal(true)}
                onTradingConfigClick={() => setShowTradingConfigModal(true)}
            />

            {/* 任务列表区 */}
            <div className="dashboard-content">
                <TaskList
                    tasks={tasks}
                    onToggleTask={toggleTask}
                    onShowDetails={openDetailsModal}
                    onEditTask={editTask}
                    onDeleteTask={deleteTask}
                    onLiquidateTask={liquidateTask}
                    onCopyTask={copyTask}
                    onViewLogs={handleViewLogs}
                />
            </div>

            {/* 任务配置模态窗口 */}
            {showConfigModal && (
                <TaskConfigModal
                    task={selectedTask}
                    onSave={saveTaskConfig}
                    onClose={closeConfigModal}
                />
            )}

            {/* 任务详细信息模态窗口 */}
            {showDetailsModal && selectedTask && (
                <TaskDetailsModal
                    task={selectedTask}
                    onClose={closeDetailsModal}
                />
            )}

            {/* 任务日志模态窗口 */}
            {showLogModal && logModalTask && (
                <TaskLogModal
                    task={logModalTask}
                    logs={currentTaskLogs}
                    onClose={handleCloseLogModal}
                />
            )}

            {/* 行情配置模态窗口 */}
            <MarketConfigModal
                isOpen={showMarketConfigModal}
                onClose={() => setShowMarketConfigModal(false)}
                onSave={handleSaveMarketConfig}
            />

            {/* 交易配置模态窗口 */}
            <TradingConfigModal
                isOpen={showTradingConfigModal}
                onClose={() => setShowTradingConfigModal(false)}
                onSave={handleSaveTradingConfig}
            />

            {/* 加载遮罩 */}
            <LoadingOverlay loading={isLoading} />
        </div>
    );
};