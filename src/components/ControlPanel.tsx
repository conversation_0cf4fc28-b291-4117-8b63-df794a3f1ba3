// 控制面板组件
import React from 'react';

interface ControlPanelProps {
    futuConnected: boolean;
    huashengConnected: boolean;
    loading: boolean;
    onStartFutu: () => void;
    onStartHuasheng: () => void;
    onConnectFutu: () => void;
    onConnectHuasheng: () => void;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
    futuConnected,
    huashengConnected,
    loading,
    onStartFutu,
    onStartHuasheng,
    onConnectFutu,
    onConnectHuasheng
}) => {
    return (
        <div style={{ marginBottom: '20px' }}>
            <h2>🎮 控制面板</h2>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                <button onClick={onStartFutu} disabled={loading || futuConnected}>
                    启动富途适配器
                </button>
                <button onClick={onStartHuasheng} disabled={loading || huashengConnected}>
                    启动华盛适配器
                </button>
                <button onClick={onConnectFutu} disabled={loading || !futuConnected}>
                    连接富途服务
                </button>
                <button onClick={onConnectHuasheng} disabled={loading || !huashengConnected}>
                    连接华盛服务
                </button>
            </div>
        </div>
    );
};

export default ControlPanel;
