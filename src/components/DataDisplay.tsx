// 数据显示组件
import React from 'react';

interface DataDisplayProps {
    quote: any;
    funds: any;
}

const DataDisplay: React.FC<DataDisplayProps> = ({ quote, funds }) => {
    if (!quote && !funds) {
        return null;
    }

    return (
        <div style={{ display: 'flex', gap: '20px' }}>
            {/* 报价数据 */}
            {quote && (
                <div style={{ flex: 1, border: '1px solid #ccc', padding: '10px' }}>
                    <h3>📈 富途报价数据</h3>
                    <pre>{JSON.stringify(quote, null, 2)}</pre>
                </div>
            )}

            {/* 资金数据 */}
            {funds && (
                <div style={{ flex: 1, border: '1px solid #ccc', padding: '10px' }}>
                    <h3>💰 华盛资金数据</h3>
                    <pre>{JSON.stringify(funds, null, 2)}</pre>
                </div>
            )}
        </div>
    );
};

export default DataDisplay;
