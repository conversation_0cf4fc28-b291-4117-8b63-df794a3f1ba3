import React, { useEffect, useState } from 'react';
import { createPortal } from 'react-dom';

interface PortalProps {
    children: React.ReactNode;
    container?: Element | null;
}

export const Portal: React.FC<PortalProps> = ({ 
    children, 
    container 
}) => {
    const [portalContainer, setPortalContainer] = useState<Element | null>(null);

    useEffect(() => {
        // 使用提供的容器或默认使用 document.body
        const targetContainer = container || document.body;
        setPortalContainer(targetContainer);
    }, [container]);

    // 如果还没有确定容器，不渲染任何内容
    if (!portalContainer) {
        return null;
    }

    // 使用 React Portal 将内容渲染到指定容器
    return createPortal(children, portalContainer);
};

interface PortalPositionProps {
    children: React.ReactNode;
    triggerRef: React.RefObject<HTMLElement>;
    placement?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
    offset?: { x: number; y: number };
    container?: Element | null;
    className?: string;
    style?: React.CSSProperties;
}

export const PortalWithPosition: React.FC<PortalPositionProps> = ({
    children,
    triggerRef,
    placement = 'bottom-right',
    offset = { x: 0, y: 4 },
    container,
    className,
    style
}) => {
    const [position, setPosition] = useState<{ top: number; left: number } | null>(null);
    const [menuElement, setMenuElement] = useState<HTMLElement | null>(null);

    // 获取菜单元素的ref回调
    const menuRef = React.useCallback((element: HTMLElement | null) => {
        setMenuElement(element);
    }, []);

    useEffect(() => {
        const calculatePosition = () => {
            if (!triggerRef.current) return;

            const triggerRect = triggerRef.current.getBoundingClientRect();
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;
            
            // 获取实际菜单尺寸，如果还没有渲染则使用默认值
            const menuWidth = menuElement?.offsetWidth || 160;
            const menuHeight = menuElement?.offsetHeight || 200;
            
            let top: number;
            let left: number;

            // 根据placement计算基础位置
            switch (placement) {
                case 'bottom-left':
                    top = triggerRect.bottom + offset.y;
                    left = triggerRect.left + offset.x;
                    break;
                case 'bottom-right':
                    top = triggerRect.bottom + offset.y;
                    left = triggerRect.right + offset.x - menuWidth;
                    break;
                case 'top-left':
                    top = triggerRect.top - offset.y - menuHeight;
                    left = triggerRef.left + offset.x;
                    break;
                case 'top-right':
                    top = triggerRect.top - offset.y - menuHeight;
                    left = triggerRect.right + offset.x - menuWidth;
                    break;
                default:
                    top = triggerRect.bottom + offset.y;
                    left = triggerRect.right + offset.x - menuWidth;
            }

            // 边界检测和自动调整
            // 水平边界检测
            if (left < 8) {
                left = 8; // 左边距
            } else if (left + menuWidth > viewportWidth - 8) {
                left = viewportWidth - menuWidth - 8; // 右边距
            }

            // 垂直边界检测
            if (top < 8) {
                top = 8; // 上边距
            } else if (top + menuHeight > viewportHeight - 8) {
                // 如果下方空间不足，尝试显示在上方
                const topSpaceAvailable = triggerRect.top - menuHeight - offset.y;
                if (topSpaceAvailable > 8) {
                    top = triggerRect.top - menuHeight - offset.y;
                } else {
                    top = viewportHeight - menuHeight - 8; // 底部边距
                }
            }

            setPosition({ top, left });
        };

        // 初次计算位置
        calculatePosition();

        // 如果菜单元素已经存在，重新计算位置（用于获取准确尺寸）
        if (menuElement) {
            // 使用requestAnimationFrame确保DOM已更新
            requestAnimationFrame(calculatePosition);
        }

        // 监听窗口大小变化和滚动事件
        const handleResize = () => calculatePosition();
        const handleScroll = () => calculatePosition();

        window.addEventListener('resize', handleResize);
        window.addEventListener('scroll', handleScroll, true);

        return () => {
            window.removeEventListener('resize', handleResize);
            window.removeEventListener('scroll', handleScroll, true);
        };
    }, [triggerRef, placement, offset, menuElement]);

    if (!position) {
        return null;
    }

    const portalContent = (
        <div
            ref={(el) => {
                menuRef(el);
                // 如果有外部的ref需要，也可以在这里处理
            }}
            className={className}
            style={{
                position: 'fixed',
                top: position.top,
                left: position.left,
                zIndex: 50000, // 确保在最顶层
                ...style
            }}
        >
            {children}
        </div>
    );

    return <Portal container={container}>{portalContent}</Portal>;
};