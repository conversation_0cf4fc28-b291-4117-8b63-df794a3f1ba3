import React, { useState, useRef, useEffect } from 'react';
import { PortalWithPosition } from './Portal';

interface DropdownMenuItem {
    label: string;
    icon?: string;
    action: () => void;
    disabled?: boolean;
    style?: 'normal' | 'danger' | 'warning' | 'success';
    description?: string;
}

interface DropdownMenuProps {
    items: DropdownMenuItem[];
    isOpen: boolean;
    onToggle: (open: boolean) => void;
    trigger?: React.ReactNode;
    placement?: 'bottom-left' | 'bottom-right' | 'top-left' | 'top-right';
    size?: 'sm' | 'md' | 'lg';
}

export const DropdownMenu: React.FC<DropdownMenuProps> = ({
    items,
    isOpen,
    onToggle,
    trigger,
    placement = 'bottom-right',
    size = 'md'
}) => {
    const dropdownRef = useRef<HTMLDivElement>(null);
    const triggerRef = useRef<HTMLButtonElement>(null);
    const containerRef = useRef<HTMLDivElement>(null);
    const actualTriggerRef = useRef<HTMLElement>(null);

    // 点击外部关闭
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (
                containerRef.current &&
                !containerRef.current.contains(event.target as Node)
            ) {
                onToggle(false);
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
            document.addEventListener('touchstart', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
            document.removeEventListener('touchstart', handleClickOutside);
        };
    }, [isOpen, onToggle]);

    // ESC键关闭
    useEffect(() => {
        const handleEscapeKey = (event: KeyboardEvent) => {
            if (event.key === 'Escape' && isOpen) {
                onToggle(false);
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscapeKey);
        }

        return () => {
            document.removeEventListener('keydown', handleEscapeKey);
        };
    }, [isOpen, onToggle]);

    // 获取尺寸样式
    const getSizeStyles = () => {
        switch (size) {
            case 'sm':
                return {
                    trigger: { padding: '4px 8px', fontSize: '12px' },
                    menu: { minWidth: '120px' },
                    item: { padding: '6px 12px', fontSize: '12px' }
                };
            case 'lg':
                return {
                    trigger: { padding: '10px 16px', fontSize: '16px' },
                    menu: { minWidth: '200px' },
                    item: { padding: '12px 16px', fontSize: '14px' }
                };
            default: // md
                return {
                    trigger: { padding: '6px 12px', fontSize: '14px' },
                    menu: { minWidth: '160px' },
                    item: { padding: '8px 12px', fontSize: '13px' }
                };
        }
    };

    const sizeStyles = getSizeStyles();


    // 获取菜单项样式
    const getItemStyle = (item: DropdownMenuItem): React.CSSProperties => {
        const baseStyle: React.CSSProperties = {
            ...sizeStyles.item,
            display: 'flex',
            alignItems: 'center',
            gap: '8px',
            cursor: item.disabled ? 'not-allowed' : 'pointer',
            border: 'none',
            background: 'transparent',
            width: '100%',
            textAlign: 'left',
            transition: 'all 0.2s ease'
        };

        // 根据样式设置颜色
        let itemColor = '#495057'; // 默认颜色
        switch (item.style) {
            case 'danger':
                itemColor = '#dc3545';
                break;
            case 'warning':
                itemColor = '#fd7e14';
                break;
            case 'success':
                itemColor = '#28a745';
                break;
            default:
                itemColor = '#495057';
        }

        // 禁用状态的样式
        if (item.disabled) {
            return {
                ...baseStyle,
                color: '#9ca3af', // 禁用时使用灰色，确保可见性
                opacity: 0.6
            };
        }

        // 正常状态的样式
        return {
            ...baseStyle,
            color: itemColor
        };
    };

    const handleItemClick = (item: DropdownMenuItem) => {
        if (!item.disabled) {
            item.action();
            onToggle(false);
        }
    };

    const handleKeyDown = (event: React.KeyboardEvent, item: DropdownMenuItem) => {
        if (event.key === 'Enter' || event.key === ' ') {
            event.preventDefault();
            handleItemClick(item);
        }
    };


    // 处理自定义trigger的ref传递
    const handleTriggerRef = (element: HTMLElement | null) => {
        if (element) {
            actualTriggerRef.current = element;
        }
    };

    // 克隆trigger并添加ref
    const clonedTrigger = trigger ? 
        React.cloneElement(trigger as React.ReactElement, {
            ref: handleTriggerRef
        }) : null;

    return (
        <div ref={containerRef} style={{ position: 'relative', display: 'inline-block' }}>
            {clonedTrigger || 
                <button
                    ref={(el) => {
                        triggerRef.current = el;
                        actualTriggerRef.current = el;
                    }}
                    style={{
                        ...sizeStyles.trigger,
                        backgroundColor: '#6c757d',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '4px',
                        transition: 'all 0.2s ease'
                    }}
                    onClick={() => onToggle(!isOpen)}
                    aria-expanded={isOpen}
                    aria-haspopup="true"
                >
                    <span>⋯</span>
                </button>
            }
            
            {isOpen && actualTriggerRef.current && (
                <PortalWithPosition
                    triggerRef={actualTriggerRef}
                    placement={placement}
                    offset={{ x: 0, y: 4 }}
                >
                    <div
                        ref={dropdownRef}
                        style={{
                            ...sizeStyles.menu,
                            backgroundColor: 'white',
                            border: '1px solid #dee2e6',
                            borderRadius: '6px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
                            overflow: 'hidden',
                            animation: 'dropdownSlideIn 0.2s ease-out'
                        }}
                        role="menu"
                        aria-orientation="vertical"
                    >
                        {items.map((item, index) => (
                            <button
                                key={index}
                                style={getItemStyle(item)}
                                onClick={() => handleItemClick(item)}
                                onKeyDown={(e) => handleKeyDown(e, item)}
                                disabled={item.disabled}
                                role="menuitem"
                                tabIndex={item.disabled ? -1 : 0}
                                title={item.description}
                                onMouseEnter={(e) => {
                                    if (!item.disabled) {
                                        const target = e.currentTarget;
                                        if (item.style === 'danger') {
                                            target.style.backgroundColor = '#f8d7da';
                                            target.style.color = '#721c24';
                                        } else if (item.style === 'warning') {
                                            target.style.backgroundColor = '#fff3cd';
                                            target.style.color = '#856404';
                                        } else if (item.style === 'success') {
                                            target.style.backgroundColor = '#d4edda';
                                            target.style.color = '#155724';
                                        } else {
                                            target.style.backgroundColor = '#f8f9fa';
                                            target.style.color = '#212529';
                                        }
                                    }
                                }}
                                onMouseLeave={(e) => {
                                    const target = e.currentTarget;
                                    target.style.backgroundColor = 'transparent';
                                    if (item.disabled) {
                                        target.style.color = '#9ca3af'; // 禁用状态保持灰色
                                    } else if (item.style === 'danger') {
                                        target.style.color = '#dc3545';
                                    } else if (item.style === 'warning') {
                                        target.style.color = '#fd7e14';
                                    } else if (item.style === 'success') {
                                        target.style.color = '#28a745';
                                    } else {
                                        target.style.color = '#495057';
                                    }
                                }}
                            >
                                {item.icon && <span>{item.icon}</span>}
                                <span>{item.label}</span>
                            </button>
                        ))}
                    </div>
                </PortalWithPosition>
            )}
        </div>
    );
};

// 添加动画样式
const dropdownStyleSheet = document.createElement('style');
dropdownStyleSheet.textContent = `
    @keyframes dropdownSlideIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
`;
if (!document.head.querySelector('style[data-dropdown-styles]')) {
    dropdownStyleSheet.setAttribute('data-dropdown-styles', 'true');
    document.head.appendChild(dropdownStyleSheet);
}