import React from 'react';
import { AnimatedComponent } from './AnimationSystem';

export const TaskCardSkeleton: React.FC = () => {
    return (
        <AnimatedComponent animation="fadeIn" trigger={true}>
            <div className="task-card skeleton" style={{
                background: '#f8f9fa',
                border: '1px solid #e9ecef',
                borderRadius: '8px',
                padding: '16px',
                position: 'relative',
                overflow: 'hidden'
            }}>
                {/* 骨架屏动画效果 */}
                <style>{`
                    @keyframes skeleton-loading {
                        0% { background-position: 200% 0; }
                        100% { background-position: -200% 0; }
                    }
                    
                    .skeleton-item {
                        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                        background-size: 200% 100%;
                        animation: skeleton-loading 1.5s infinite;
                        border-radius: 4px;
                    }
                `}</style>

                {/* 头部信息 */}
                <div className="skeleton-header" style={{ marginBottom: '16px' }}>
                    <div className="skeleton-item" style={{ 
                        height: '20px', 
                        width: '60%', 
                        marginBottom: '8px' 
                    }}></div>
                    <div className="skeleton-item" style={{ 
                        height: '16px', 
                        width: '40%' 
                    }}></div>
                </div>

                {/* 状态指示器 */}
                <div className="skeleton-status" style={{ 
                    position: 'absolute', 
                    top: '16px', 
                    right: '16px' 
                }}>
                    <div className="skeleton-item" style={{ 
                        height: '24px', 
                        width: '80px', 
                        borderRadius: '12px' 
                    }}></div>
                </div>

                {/* 盈亏显示区域 */}
                <div className="skeleton-metrics" style={{ 
                    background: 'white',
                    border: '1px solid #dee2e6',
                    borderRadius: '6px',
                    padding: '12px',
                    margin: '16px 0',
                    textAlign: 'center'
                }}>
                    <div className="skeleton-item" style={{ 
                        height: '32px', 
                        width: '70%', 
                        margin: '0 auto 8px' 
                    }}></div>
                    <div className="skeleton-item" style={{ 
                        height: '16px', 
                        width: '50%', 
                        margin: '0 auto' 
                    }}></div>
                </div>

                {/* 持仓信息 */}
                <div className="skeleton-position" style={{ marginBottom: '16px' }}>
                    <div className="skeleton-item" style={{ 
                        height: '14px', 
                        width: '45%', 
                        marginBottom: '6px' 
                    }}></div>
                    <div className="skeleton-item" style={{ 
                        height: '14px', 
                        width: '35%' 
                    }}></div>
                </div>

                {/* 操作按钮 */}
                <div className="skeleton-actions" style={{ 
                    display: 'flex', 
                    gap: '8px',
                    justifyContent: 'space-between' 
                }}>
                    <div className="skeleton-item" style={{ 
                        height: '32px', 
                        width: '100px', 
                        borderRadius: '16px' 
                    }}></div>
                    <div className="skeleton-item" style={{ 
                        height: '32px', 
                        width: '100px', 
                        borderRadius: '16px' 
                    }}></div>
                </div>
            </div>
        </AnimatedComponent>
    );
};

// 列表骨架屏
interface TaskListSkeletonProps {
    count?: number;
}

export const TaskListSkeleton: React.FC<TaskListSkeletonProps> = ({ count = 6 }) => {
    return (
        <div className="task-list-skeleton">
            {/* 头部骨架屏 */}
            <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '24px' 
            }}>
                <div>
                    <div className="skeleton-item" style={{ 
                        height: '28px', 
                        width: '120px', 
                        marginBottom: '8px' 
                    }}></div>
                    <div className="skeleton-item" style={{ 
                        height: '16px', 
                        width: '200px' 
                    }}></div>
                </div>
                <div style={{ display: 'flex', gap: '8px' }}>
                    {[1, 2, 3].map(i => (
                        <div key={i} className="skeleton-item" style={{ 
                            height: '36px', 
                            width: '100px', 
                            borderRadius: '18px' 
                        }}></div>
                    ))}
                </div>
            </div>

            {/* 任务卡片骨架屏 */}
            <div style={{
                display: 'grid',
                gap: '20px',
                gridTemplateColumns: 'repeat(auto-fill, minmax(300px, 1fr))'
            }}>
                {Array.from({ length: count }, (_, i) => (
                    <TaskCardSkeleton key={i} />
                ))}
            </div>
        </div>
    );
};