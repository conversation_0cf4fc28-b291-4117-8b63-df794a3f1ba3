import React, { useEffect, useState, useRef } from 'react';

// 动画配置类型
export interface AnimationConfig {
    duration?: number;
    delay?: number;
    easing?: 'ease' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'linear';
    fillMode?: 'none' | 'forwards' | 'backwards' | 'both';
    iterationCount?: number | 'infinite';
    direction?: 'normal' | 'reverse' | 'alternate' | 'alternate-reverse';
}

export interface TransitionConfig {
    property?: string;
    duration?: number;
    easing?: string;
    delay?: number;
}

// 动画类型定义
export type AnimationType = 
    | 'fadeIn' | 'fadeOut' | 'fadeInUp' | 'fadeInDown'
    | 'slideIn' | 'slideOut' | 'slideInLeft' | 'slideInRight'
    | 'scaleIn' | 'scaleOut' | 'bounce' | 'pulse'
    | 'shake' | 'rotate' | 'flip' | 'elastic';

// 预设动画配置
const animationPresets: Record<AnimationType, AnimationConfig> = {
    fadeIn: { duration: 300, easing: 'ease-out' },
    fadeOut: { duration: 300, easing: 'ease-in' },
    fadeInUp: { duration: 400, easing: 'ease-out' },
    fadeInDown: { duration: 400, easing: 'ease-out' },
    slideIn: { duration: 300, easing: 'ease-out' },
    slideOut: { duration: 300, easing: 'ease-in' },
    slideInLeft: { duration: 350, easing: 'ease-out' },
    slideInRight: { duration: 350, easing: 'ease-out' },
    scaleIn: { duration: 250, easing: 'ease-out' },
    scaleOut: { duration: 250, easing: 'ease-in' },
    bounce: { duration: 600, easing: 'ease-out' },
    pulse: { duration: 1000, iterationCount: 'infinite', easing: 'ease-in-out' },
    shake: { duration: 500, easing: 'ease-in-out' },
    rotate: { duration: 300, easing: 'ease-in-out' },
    flip: { duration: 600, easing: 'ease-in-out' },
    elastic: { duration: 800, easing: 'ease-out' }
};

// 动画Hook
export const useAnimation = (
    type: AnimationType,
    trigger: boolean = true,
    config?: AnimationConfig
) => {
    const [isAnimating, setIsAnimating] = useState(false);
    const [isVisible, setIsVisible] = useState(false);
    const elementRef = useRef<HTMLElement>(null);

    const finalConfig = { ...animationPresets[type], ...config };

    useEffect(() => {
        if (trigger && elementRef.current) {
            setIsAnimating(true);
            setIsVisible(true);

            const element = elementRef.current;
            element.style.animation = `${type} ${finalConfig.duration}ms ${finalConfig.easing} ${finalConfig.delay || 0}ms ${finalConfig.fillMode || 'both'}`;

            const handleAnimationEnd = () => {
                setIsAnimating(false);
                if (type.includes('Out')) {
                    setIsVisible(false);
                }
            };

            element.addEventListener('animationend', handleAnimationEnd);

            return () => {
                element.removeEventListener('animationend', handleAnimationEnd);
            };
        }
    }, [trigger, type, finalConfig]);

    return {
        ref: elementRef,
        isAnimating,
        isVisible,
        style: {
            opacity: isVisible ? 1 : 0,
            // 移除 pointerEvents 限制，允许动画期间的交互
            // pointerEvents: isAnimating ? 'none' : 'auto'  // ❌ 原来的问题代码
        } as React.CSSProperties
    };
};

// 交错动画Hook
export const useStaggeredAnimation = (
    items: any[],
    animationType: AnimationType,
    staggerDelay: number = 100,
    config?: AnimationConfig
) => {
    const [animationStates, setAnimationStates] = useState<boolean[]>([]);

    useEffect(() => {
        if (items.length > 0) {
            // 重置动画状态
            setAnimationStates(new Array(items.length).fill(false));

            // 交错触发动画
            items.forEach((_, index) => {
                setTimeout(() => {
                    setAnimationStates(prev => {
                        const newStates = [...prev];
                        newStates[index] = true;
                        return newStates;
                    });
                }, index * staggerDelay);
            });
        }
    }, [items.length, staggerDelay]);

    return animationStates;
};

// 值变化高亮Hook
export const useValueChangeHighlight = (value: any, highlightDuration: number = 1000) => {
    const [isHighlighted, setIsHighlighted] = useState(false);
    const prevValueRef = useRef(value);

    useEffect(() => {
        if (prevValueRef.current !== value) {
            setIsHighlighted(true);
            const timer = setTimeout(() => setIsHighlighted(false), highlightDuration);
            prevValueRef.current = value;
            return () => clearTimeout(timer);
        }
    }, [value, highlightDuration]);

    return isHighlighted;
};

// 动画组件
interface AnimatedComponentProps {
    children: React.ReactNode;
    animation: AnimationType;
    trigger?: boolean;
    delay?: number;
    config?: AnimationConfig;
    className?: string;
    style?: React.CSSProperties;
}

export const AnimatedComponent: React.FC<AnimatedComponentProps> = ({
    children,
    animation,
    trigger = true,
    delay = 0,
    config,
    className,
    style
}) => {
    const { ref, isVisible, style: animationStyle } = useAnimation(animation, trigger, { ...config, delay });

    return (
        <div
            ref={ref as React.RefObject<HTMLDivElement>}
            className={className}
            style={{ ...style, ...animationStyle }}
        >
            {children}
        </div>
    );
};

// 交错动画组件
interface StaggeredAnimationProps {
    children: React.ReactNode[];
    animation: AnimationType;
    staggerDelay?: number;
    config?: AnimationConfig;
    className?: string;
}

export const StaggeredAnimation: React.FC<StaggeredAnimationProps> = ({
    children,
    animation,
    staggerDelay = 100,
    config,
    className
}) => {
    const animationStates = useStaggeredAnimation(children, animation, staggerDelay, config);

    return (
        <div className={className}>
            {children.map((child, index) => (
                <AnimatedComponent
                    key={index}
                    animation={animation}
                    trigger={animationStates[index]}
                    config={config}
                >
                    {child}
                </AnimatedComponent>
            ))}
        </div>
    );
};

// 数值变化高亮组件
interface ValueHighlightProps {
    value: any;
    children: React.ReactNode;
    highlightColor?: string;
    highlightDuration?: number;
    className?: string;
    style?: React.CSSProperties;
}

export const ValueHighlight: React.FC<ValueHighlightProps> = ({
    value,
    children,
    highlightColor = 'rgba(255, 235, 59, 0.3)',
    highlightDuration = 1000,
    className,
    style
}) => {
    const isHighlighted = useValueChangeHighlight(value, highlightDuration);

    return (
        <div
            className={className}
            style={{
                ...style,
                backgroundColor: isHighlighted ? highlightColor : 'transparent',
                transition: `background-color ${highlightDuration}ms ease`
            }}
        >
            {children}
        </div>
    );
};

// 可配置动画系统
interface AnimationSystemConfig {
    enableAnimations: boolean;
    animationSpeed: 'slow' | 'normal' | 'fast';
    respectReducedMotion: boolean;
}

const defaultAnimationConfig: AnimationSystemConfig = {
    enableAnimations: true,
    animationSpeed: 'normal',
    respectReducedMotion: true
};

class AnimationSystem {
    private config: AnimationSystemConfig;

    constructor(config?: Partial<AnimationSystemConfig>) {
        this.config = { ...defaultAnimationConfig, ...config };
        
        // 检查用户的动画偏好
        if (this.config.respectReducedMotion && this.prefersReducedMotion()) {
            this.config.enableAnimations = false;
        }
    }

    private prefersReducedMotion(): boolean {
        return window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    }

    private getSpeedMultiplier(): number {
        switch (this.config.animationSpeed) {
            case 'slow': return 1.5;
            case 'fast': return 0.5;
            default: return 1;
        }
    }

    public isEnabled(): boolean {
        return this.config.enableAnimations;
    }

    public getDuration(baseDuration: number): number {
        if (!this.config.enableAnimations) return 0;
        return baseDuration * this.getSpeedMultiplier();
    }

    public updateConfig(newConfig: Partial<AnimationSystemConfig>): void {
        this.config = { ...this.config, ...newConfig };
    }

    public getConfig(): AnimationSystemConfig {
        return { ...this.config };
    }
}

// 全局动画系统实例
export const animationSystem = new AnimationSystem();

// 动画系统Hook
export const useAnimationSystem = () => {
    const [config, setConfig] = useState(animationSystem.getConfig());

    const updateConfig = (newConfig: Partial<AnimationSystemConfig>) => {
        animationSystem.updateConfig(newConfig);
        setConfig(animationSystem.getConfig());
    };

    return {
        config,
        updateConfig,
        isEnabled: animationSystem.isEnabled(),
        getDuration: (duration: number) => animationSystem.getDuration(duration)
    };
};

// 性能监控Hook
export const useAnimationPerformance = () => {
    const [performanceData, setPerformanceData] = useState({
        averageFrameTime: 0,
        droppedFrames: 0,
        isPerformanceGood: true
    });

    useEffect(() => {
        let frameCount = 0;
        let lastTime = performance.now();
        let totalFrameTime = 0;
        let droppedFrames = 0;

        const measurePerformance = (currentTime: number) => {
            const deltaTime = currentTime - lastTime;
            frameCount++;
            totalFrameTime += deltaTime;

            // 检测掉帧（假设60FPS，每帧约16.67ms）
            if (deltaTime > 20) {
                droppedFrames++;
            }

            // 每秒更新一次性能数据
            if (frameCount >= 60) {
                const averageFrameTime = totalFrameTime / frameCount;
                const isPerformanceGood = droppedFrames < 5; // 容忍少量掉帧

                setPerformanceData({
                    averageFrameTime,
                    droppedFrames,
                    isPerformanceGood
                });

                // 重置计数器
                frameCount = 0;
                totalFrameTime = 0;
                droppedFrames = 0;
            }

            lastTime = currentTime;
            requestAnimationFrame(measurePerformance);
        };

        const animationId = requestAnimationFrame(measurePerformance);

        return () => {
            cancelAnimationFrame(animationId);
        };
    }, []);

    return performanceData;
};

// CSS动画样式注入
const injectAnimationStyles = () => {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; }
            to { opacity: 0; }
        }
        
        @keyframes fadeInUp {
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }
        
        @keyframes fadeInDown {
            from { 
                opacity: 0; 
                transform: translateY(-30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); }
            to { transform: translateX(0); }
        }
        
        @keyframes slideOut {
            from { transform: translateX(0); }
            to { transform: translateX(100%); }
        }
        
        @keyframes slideInLeft {
            from { 
                opacity: 0;
                transform: translateX(-50px); 
            }
            to { 
                opacity: 1;
                transform: translateX(0); 
            }
        }
        
        @keyframes slideInRight {
            from { 
                opacity: 0;
                transform: translateX(50px); 
            }
            to { 
                opacity: 1;
                transform: translateX(0); 
            }
        }
        
        @keyframes scaleIn {
            from { 
                opacity: 0;
                transform: scale(0.8); 
            }
            to { 
                opacity: 1;
                transform: scale(1); 
            }
        }
        
        @keyframes scaleOut {
            from { 
                opacity: 1;
                transform: scale(1); 
            }
            to { 
                opacity: 0;
                transform: scale(0.8); 
            }
        }
        
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% {
                transform: translate3d(0,0,0);
            }
            40%, 43% {
                transform: translate3d(0, -10px, 0);
            }
            70% {
                transform: translate3d(0, -5px, 0);
            }
            90% {
                transform: translate3d(0, -2px, 0);
            }
        }
        
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
                transform: scale(1);
            }
            50% {
                opacity: 0.7;
                transform: scale(1.05);
            }
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
            20%, 40%, 60%, 80% { transform: translateX(5px); }
        }
        
        @keyframes rotate {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        @keyframes flip {
            0% { transform: perspective(400px) rotateY(0); }
            40% { transform: perspective(400px) rotateY(-15deg); }
            50% { transform: perspective(400px) rotateY(-15deg); }
            80% { transform: perspective(400px) rotateY(0); }
            100% { transform: perspective(400px) rotateY(0); }
        }
        
        @keyframes elastic {
            0% {
                transform: scale(0);
            }
            55% {
                transform: scale(1.1);
            }
            75% {
                transform: scale(0.9);
            }
            100% {
                transform: scale(1);
            }
        }
        
        /* 减少动画设置的支持 */
        @media (prefers-reduced-motion: reduce) {
            *,
            *::before,
            *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
        
        /* 工具类 */
        .animate-disabled * {
            animation: none !important;
            transition: none !important;
        }
        
        .animate-fast * {
            animation-duration: 0.15s !important;
            transition-duration: 0.15s !important;
        }
        
        .animate-slow * {
            animation-duration: 0.6s !important;
            transition-duration: 0.6s !important;
        }
    `;

    if (!document.head.querySelector('style[data-animation-system]')) {
        styleSheet.setAttribute('data-animation-system', 'true');
        document.head.appendChild(styleSheet);
    }
};

// 初始化动画系统
injectAnimationStyles();