import React from 'react';

interface SkeletonProps {
    width?: string | number;
    height?: string | number;
    borderRadius?: string | number;
    className?: string;
    style?: React.CSSProperties;
    animated?: boolean;
}

export const Skeleton: React.FC<SkeletonProps> = ({
    width = '100%',
    height = '20px',
    borderRadius = '4px',
    className,
    style,
    animated = true
}) => {
    return (
        <div
            className={`skeleton ${animated ? 'skeleton-animated' : ''} ${className || ''}`}
            style={{
                width,
                height,
                borderRadius,
                backgroundColor: '#f0f0f0',
                backgroundImage: animated 
                    ? 'linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)'
                    : undefined,
                backgroundSize: animated ? '200% 100%' : undefined,
                animation: animated ? 'skeleton-loading 1.5s infinite' : undefined,
                ...style
            }}
        />
    );
};

interface TaskCardSkeletonProps {
    count?: number;
}

export const TaskCardSkeleton: React.FC<TaskCardSkeletonProps> = ({ count = 1 }) => {
    const skeletons = Array.from({ length: count }, (_, index) => (
        <div
            key={index}
            style={{
                background: '#f8f9fa',
                border: '1px solid #e9ecef',
                borderRadius: '8px',
                padding: '16px',
                position: 'relative'
            }}
        >
            {/* 头部骨架 */}
            <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'flex-start', 
                marginBottom: '12px' 
            }}>
                <div style={{ flex: 1, minWidth: 0 }}>
                    <Skeleton height="20px" width="60%" style={{ marginBottom: '8px' }} />
                    <Skeleton height="16px" width="40%" />
                </div>
                <Skeleton height="24px" width="80px" borderRadius="12px" />
            </div>

            {/* 内容区域骨架 */}
            <div style={{
                background: 'white',
                border: '1px solid #dee2e6',
                borderRadius: '6px',
                padding: '12px',
                marginTop: '8px'
            }}>
                {/* 盈亏区域骨架 */}
                <div style={{
                    padding: '12px',
                    borderRadius: '6px',
                    backgroundColor: '#f8f9fa',
                    border: '1px solid #e9ecef',
                    textAlign: 'center',
                    marginBottom: '12px'
                }}>
                    <Skeleton height="24px" width="50%" style={{ margin: '0 auto 4px auto' }} />
                    <Skeleton height="16px" width="30%" style={{ margin: '0 auto' }} />
                </div>

                {/* 持仓信息骨架 */}
                <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: '1fr 1fr', 
                    gap: '12px', 
                    marginBottom: '12px' 
                }}>
                    <div>
                        <Skeleton height="12px" width="50%" style={{ marginBottom: '4px' }} />
                        <Skeleton height="16px" width="70%" />
                    </div>
                    <div>
                        <Skeleton height="12px" width="40%" style={{ marginBottom: '4px' }} />
                        <Skeleton height="16px" width="80%" />
                    </div>
                </div>

                {/* 辅助信息骨架 */}
                <div style={{ 
                    display: 'grid', 
                    gridTemplateColumns: '1fr 1fr', 
                    gap: '12px' 
                }}>
                    <Skeleton height="12px" width="90%" />
                    <Skeleton height="12px" width="80%" />
                </div>
            </div>

            {/* 操作按钮骨架 */}
            <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                gap: '8px',
                paddingTop: '12px',
                borderTop: '1px solid #f0f0f0',
                marginTop: '12px'
            }}>
                <div style={{ display: 'flex', gap: '8px' }}>
                    <Skeleton height="32px" width="70px" borderRadius="4px" />
                    <Skeleton height="32px" width="60px" borderRadius="4px" />
                </div>
                <Skeleton height="24px" width="24px" borderRadius="4px" />
            </div>
        </div>
    ));

    return <>{skeletons}</>;
};

interface LoadingStateProps {
    loading: boolean;
    children: React.ReactNode;
    skeleton?: React.ReactNode;
    overlay?: boolean;
    message?: string;
}

export const LoadingState: React.FC<LoadingStateProps> = ({
    loading,
    children,
    skeleton,
    overlay = false,
    message = '加载中...'
}) => {
    if (loading) {
        if (overlay) {
            return (
                <div style={{ position: 'relative' }}>
                    {children}
                    <div
                        style={{
                            position: 'absolute',
                            inset: 0,
                            backgroundColor: 'rgba(255, 255, 255, 0.8)',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            zIndex: 10,
                            borderRadius: '8px'
                        }}
                    >
                        <div style={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: '8px',
                            padding: '12px 20px',
                            backgroundColor: 'white',
                            borderRadius: '6px',
                            boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                            border: '1px solid #dee2e6'
                        }}>
                            <div
                                style={{
                                    width: '16px',
                                    height: '16px',
                                    border: '2px solid #f3f3f3',
                                    borderTop: '2px solid #007bff',
                                    borderRadius: '50%',
                                    animation: 'spin 1s linear infinite'
                                }}
                            />
                            <span style={{ fontSize: '14px', color: '#6c757d' }}>{message}</span>
                        </div>
                    </div>
                </div>
            );
        }

        return skeleton || <TaskCardSkeleton />;
    }

    return <>{children}</>;
};

// 错误边界组件
interface ErrorBoundaryProps {
    children: React.ReactNode;
    fallback?: React.ComponentType<{ error: Error; retry: () => void }>;
    onError?: (error: Error, errorInfo: React.ErrorInfo) => void;
}

interface ErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
}

export class ErrorBoundary extends React.Component<ErrorBoundaryProps, ErrorBoundaryState> {
    constructor(props: ErrorBoundaryProps) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error: Error): ErrorBoundaryState {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
        console.error('ErrorBoundary caught an error:', error, errorInfo);
        this.props.onError?.(error, errorInfo);
    }

    retry = () => {
        this.setState({ hasError: false, error: null });
    };

    render() {
        if (this.state.hasError) {
            const FallbackComponent = this.props.fallback || DefaultErrorFallback;
            return <FallbackComponent error={this.state.error!} retry={this.retry} />;
        }

        return this.props.children;
    }
}

// 默认错误回退组件
interface ErrorFallbackProps {
    error: Error;
    retry: () => void;
}

const DefaultErrorFallback: React.FC<ErrorFallbackProps> = ({ error, retry }) => {
    return (
        <div style={{
            background: '#f8f9fa',
            border: '1px solid #e9ecef',
            borderRadius: '8px',
            padding: '20px',
            textAlign: 'center'
        }}>
            <div style={{ fontSize: '48px', marginBottom: '12px' }}>⚠️</div>
            <h3 style={{ 
                margin: '0 0 8px 0', 
                fontSize: '18px', 
                color: '#495057',
                fontWeight: '600'
            }}>
                组件加载失败
            </h3>
            <p style={{ 
                margin: '0 0 16px 0', 
                fontSize: '14px', 
                color: '#6c757d',
                lineHeight: '1.4'
            }}>
                {error.message || '未知错误'}
            </p>
            <button
                onClick={retry}
                style={{
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    cursor: 'pointer',
                    fontSize: '14px',
                    fontWeight: '500',
                    transition: 'background-color 0.2s ease'
                }}
                onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = '#0056b3';
                }}
                onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = '#007bff';
                }}
            >
                重试
            </button>
        </div>
    );
};

// TaskCard 专用错误边界
interface TaskCardErrorBoundaryProps {
    children: React.ReactNode;
    taskId: string;
}

export const TaskCardErrorBoundary: React.FC<TaskCardErrorBoundaryProps> = ({ 
    children, 
    taskId 
}) => {
    return (
        <ErrorBoundary
            fallback={({ error, retry }) => (
                <div style={{
                    background: '#f8f9fa',
                    border: '1px solid #e9ecef',
                    borderLeft: '4px solid #dc3545',
                    borderRadius: '8px',
                    padding: '16px',
                    minHeight: '200px',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center'
                }}>
                    <div style={{ fontSize: '32px', marginBottom: '8px' }}>💥</div>
                    <h4 style={{ 
                        margin: '0 0 4px 0', 
                        fontSize: '14px', 
                        color: '#dc3545',
                        fontWeight: '600'
                    }}>
                        任务卡片错误
                    </h4>
                    <p style={{ 
                        margin: '0 0 12px 0', 
                        fontSize: '12px', 
                        color: '#6c757d',
                        textAlign: 'center'
                    }}>
                        任务 {taskId} 加载失败
                    </p>
                    <button
                        onClick={retry}
                        style={{
                            backgroundColor: '#dc3545',
                            color: 'white',
                            border: 'none',
                            padding: '4px 12px',
                            borderRadius: '3px',
                            cursor: 'pointer',
                            fontSize: '12px'
                        }}
                    >
                        重试
                    </button>
                </div>
            )}
            onError={(error, errorInfo) => {
                console.error(`TaskCard ${taskId} error:`, error, errorInfo);
            }}
        >
            {children}
        </ErrorBoundary>
    );
};

// 网络错误处理组件
interface NetworkErrorProps {
    error: Error;
    onRetry: () => void;
    loading?: boolean;
}

export const NetworkError: React.FC<NetworkErrorProps> = ({ 
    error, 
    onRetry, 
    loading = false 
}) => {
    const isNetworkError = error.message.includes('network') || 
                          error.message.includes('fetch') ||
                          error.name === 'TypeError';

    return (
        <div style={{
            background: '#fff3cd',
            border: '1px solid #ffeaa7',
            borderRadius: '6px',
            padding: '16px',
            textAlign: 'center'
        }}>
            <div style={{ fontSize: '32px', marginBottom: '8px' }}>
                {isNetworkError ? '🌐' : '❌'}
            </div>
            <h4 style={{ 
                margin: '0 0 4px 0', 
                fontSize: '16px', 
                color: '#856404',
                fontWeight: '600'
            }}>
                {isNetworkError ? '网络连接错误' : '操作失败'}
            </h4>
            <p style={{ 
                margin: '0 0 12px 0', 
                fontSize: '14px', 
                color: '#856404'
            }}>
                {isNetworkError 
                    ? '请检查网络连接并重试' 
                    : error.message || '未知错误'}
            </p>
            <button
                onClick={onRetry}
                disabled={loading}
                style={{
                    backgroundColor: loading ? '#6c757d' : '#856404',
                    color: 'white',
                    border: 'none',
                    padding: '8px 16px',
                    borderRadius: '4px',
                    cursor: loading ? 'not-allowed' : 'pointer',
                    fontSize: '14px',
                    display: 'inline-flex',
                    alignItems: 'center',
                    gap: '6px'
                }}
            >
                {loading && (
                    <div style={{
                        width: '12px',
                        height: '12px',
                        border: '2px solid #ffffff40',
                        borderTop: '2px solid #ffffff',
                        borderRadius: '50%',
                        animation: 'spin 1s linear infinite'
                    }} />
                )}
                {loading ? '重试中...' : '重试'}
            </button>
        </div>
    );
};

// 注入CSS动画
const injectLoadingStyles = () => {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
        @keyframes skeleton-loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .skeleton {
            pointer-events: none;
        }
        
        .skeleton-animated {
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s infinite;
        }
        
        /* 高对比度模式下的骨架屏 */
        @media (prefers-contrast: high) {
            .skeleton {
                background-color: #d0d0d0 !important;
                background-image: linear-gradient(90deg, #d0d0d0 25%, #c0c0c0 50%, #d0d0d0 75%) !important;
            }
        }
        
        /* 减少动画设置下的骨架屏 */
        @media (prefers-reduced-motion: reduce) {
            .skeleton-animated {
                animation: none !important;
                background-image: none !important;
                background-color: #e0e0e0 !important;
            }
        }
    `;

    if (!document.head.querySelector('style[data-loading-styles]')) {
        styleSheet.setAttribute('data-loading-styles', 'true');
        document.head.appendChild(styleSheet);
    }
};

// 初始化样式
injectLoadingStyles();