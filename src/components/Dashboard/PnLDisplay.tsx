import React from 'react';

interface PnLDisplayProps {
    pnl: number;
    pnlPercentage: number;
    trend?: 'up' | 'down' | 'stable';
    showTrend?: boolean;
    size?: 'small' | 'medium' | 'large';
}

export const PnLDisplay: React.FC<PnLDisplayProps> = ({ 
    pnl, 
    pnlPercentage, 
    trend, 
    showTrend = false,
    size = 'medium'
}) => {
    const isProfit = pnl > 0;
    const isLoss = pnl < 0;
    const isNeutral = pnl === 0;

    // 格式化货币
    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'HKD',
            minimumFractionDigits: 2
        }).format(value);
    };

    // 获取尺寸样式
    const getSizeStyles = () => {
        switch (size) {
            case 'small':
                return {
                    container: { padding: '8px' },
                    amount: { fontSize: '16px' },
                    percentage: { fontSize: '12px' }
                };
            case 'large':
                return {
                    container: { padding: '16px' },
                    amount: { fontSize: '24px' },
                    percentage: { fontSize: '16px' }
                };
            default: // medium
                return {
                    container: { padding: '12px' },
                    amount: { fontSize: '20px' },
                    percentage: { fontSize: '14px' }
                };
        }
    };

    const sizeStyles = getSizeStyles();

    // 获取背景和边框样式
    const getBackgroundStyle = () => {
        if (isProfit) {
            return {
                background: 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)',
                border: '1px solid #28a745',
                color: '#155724'
            };
        } else if (isLoss) {
            return {
                background: 'linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%)',
                border: '1px solid #dc3545',
                color: '#721c24'
            };
        } else {
            return {
                background: '#f8f9fa',
                border: '1px solid #e9ecef',
                color: '#6c757d'
            };
        }
    };

    const backgroundStyle = getBackgroundStyle();

    return (
        <div 
            className="pnl-display"
            style={{
                ...sizeStyles.container,
                borderRadius: '6px',
                textAlign: 'center',
                margin: '8px 0',
                transition: 'all 0.2s ease',
                position: 'relative',
                overflow: 'hidden',
                ...backgroundStyle
            }}
        >
            {/* 主要盈亏数值 */}
            <div 
                className="pnl-amount"
                style={{
                    ...sizeStyles.amount,
                    fontWeight: 'bold',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: '4px',
                    marginBottom: '4px'
                }}
            >
                {isProfit && (
                    <span 
                        className="trend-icon"
                        style={{ 
                            fontSize: '12px', 
                            animation: 'trendPulse 1.5s infinite',
                            color: 'inherit'
                        }}
                    >
                        ▲
                    </span>
                )}
                {isLoss && (
                    <span 
                        className="trend-icon"
                        style={{ 
                            fontSize: '12px', 
                            animation: 'trendPulse 1.5s infinite',
                            color: 'inherit'
                        }}
                    >
                        ▼
                    </span>
                )}
                {formatCurrency(pnl)}
            </div>

            {/* 盈亏百分比 */}
            <div 
                className="pnl-percentage"
                style={{
                    ...sizeStyles.percentage,
                    opacity: 0.8
                }}
            >
                ({pnlPercentage > 0 ? '+' : ''}{pnlPercentage.toFixed(2)}%)
            </div>

            {/* 趋势指示器（可选） */}
            {showTrend && trend && (
                <div 
                    className="trend-indicator"
                    style={{
                        position: 'absolute',
                        top: '4px',
                        right: '4px',
                        fontSize: '10px',
                        opacity: 0.6
                    }}
                >
                    {trend === 'up' && '📈'}
                    {trend === 'down' && '📉'}
                    {trend === 'stable' && '➡️'}
                </div>
            )}

            {/* 动画高亮效果（数值变化时） */}
            <div 
                className="value-highlight"
                style={{
                    position: 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    background: 'rgba(255, 235, 59, 0.3)',
                    borderRadius: '6px',
                    opacity: 0,
                    pointerEvents: 'none',
                    animation: 'none' // 将在数值变化时触发
                }}
            />
        </div>
    );
};

// TrendIndicator 子组件
interface TrendIndicatorProps {
    trend: 'up' | 'down' | 'stable';
    size?: 'small' | 'medium' | 'large';
}

export const TrendIndicator: React.FC<TrendIndicatorProps> = ({ trend, size = 'medium' }) => {
    const getSize = () => {
        switch (size) {
            case 'small': return '12px';
            case 'large': return '20px';
            default: return '16px';
        }
    };

    const getIcon = () => {
        switch (trend) {
            case 'up': return '📈';
            case 'down': return '📉';
            case 'stable': return '➡️';
        }
    };

    return (
        <span 
            className="trend-indicator"
            style={{
                fontSize: getSize(),
                display: 'inline-block',
                animation: 'trendPulse 2s infinite'
            }}
        >
            {getIcon()}
        </span>
    );
};

// 数值变化高亮效果 Hook
export const usePnLHighlight = (pnl: number) => {
    const [shouldHighlight, setShouldHighlight] = React.useState(false);
    const prevPnlRef = React.useRef(pnl);

    React.useEffect(() => {
        if (prevPnlRef.current !== pnl) {
            setShouldHighlight(true);
            const timer = setTimeout(() => setShouldHighlight(false), 1000);
            prevPnlRef.current = pnl;
            return () => clearTimeout(timer);
        }
    }, [pnl]);

    return shouldHighlight;
};