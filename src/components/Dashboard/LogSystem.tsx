import React from 'react';
import { LogEntry, LogLevel, LogCategory } from '../../types';

interface LogSystemProps {
    logs: LogEntry[];
    maxItems?: number;
    filterCategory?: LogCategory;
}

export const LogSystem: React.FC<LogSystemProps> = ({ 
    logs, 
    maxItems = 50,
    filterCategory 
}) => {
    const filteredLogs = filterCategory 
        ? logs.filter(log => log.category === filterCategory)
        : logs;
    
    const displayLogs = filteredLogs
        .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
        .slice(0, maxItems);

    const getLevelClass = (level: LogLevel) => {
        switch (level) {
            case 'error':
                return 'log-error';
            case 'warning':
                return 'log-warning';
            case 'info':
                return 'log-info';
            case 'debug':
                return 'log-debug';
            default:
                return '';
        }
    };

    const getLevelIcon = (level: LogLevel) => {
        switch (level) {
            case 'error':
                return '❌';
            case 'warning':
                return '⚠️';
            case 'info':
                return 'ℹ️';
            case 'debug':
                return '🔍';
            default:
                return '📝';
        }
    };

    const getCategoryName = (category: LogCategory) => {
        switch (category) {
            case 'strategy':
                return '策略';
            case 'trading':
                return '交易';
            case 'system':
                return '系统';
            default:
                return '未知';
        }
    };

    return (
        <div className="log-system">
            <div className="log-system-header">
                <h4>
                    {filterCategory ? `${getCategoryName(filterCategory)}日志` : '综合日志'}
                </h4>
                <span className="log-count">
                    {displayLogs.length} / {filteredLogs.length} 条记录
                </span>
            </div>
            
            <div className="log-system-content">
                <div className="log-list">
                    {displayLogs.length === 0 ? (
                        <div className="empty-logs">
                            <p>暂无日志记录</p>
                        </div>
                    ) : (
                        displayLogs.map((log) => (
                            <div key={log.id} className={`log-entry ${getLevelClass(log.level)}`}>
                                <div className="log-header">
                                    <span className="log-level">
                                        {getLevelIcon(log.level)}
                                    </span>
                                    <span className="log-timestamp">
                                        {log.timestamp.toLocaleString()}
                                    </span>
                                    <span className="log-category">
                                        [{getCategoryName(log.category)}]
                                    </span>
                                    {log.taskId && (
                                        <span className="log-task-id">
                                            任务:{log.taskId.slice(-8)}
                                        </span>
                                    )}
                                </div>
                                <div className="log-message">
                                    {log.message}
                                </div>
                                {log.details && Object.keys(log.details).length > 0 && (
                                    <div className="log-details">
                                        <details>
                                            <summary>详细信息</summary>
                                            <pre>{JSON.stringify(log.details, null, 2)}</pre>
                                        </details>
                                    </div>
                                )}
                            </div>
                        ))
                    )}
                </div>
            </div>
        </div>
    );
};