import React, { useEffect, useRef } from 'react';
import { Task } from '../../types';

// ARIA Live Region 组件
interface AriaLiveRegionProps {
    message: string;
    politeness?: 'polite' | 'assertive' | 'off';
    atomic?: boolean;
    relevant?: 'additions' | 'removals' | 'text' | 'all';
}

export const AriaLiveRegion: React.FC<AriaLiveRegionProps> = ({
    message,
    politeness = 'polite',
    atomic = true,
    relevant = 'additions'
}) => {
    return (
        <div
            role="status"
            aria-live={politeness}
            aria-atomic={atomic}
            aria-relevant={relevant}
            className="sr-only"
            style={{
                position: 'absolute',
                left: '-10000px',
                width: '1px',
                height: '1px',
                overflow: 'hidden'
            }}
        >
            {message}
        </div>
    );
};

// 可访问的任务卡片
interface AccessibleTaskCardProps {
    task: Task;
    index: number;
    totalCount: number;
    isSelected: boolean;
    onSelect: () => void;
    children: React.ReactNode;
}

export const AccessibleTaskCard: React.FC<AccessibleTaskCardProps> = ({
    task,
    index,
    totalCount,
    isSelected,
    onSelect,
    children
}) => {
    const cardRef = useRef<HTMLDivElement>(null);

    // 自动聚焦选中的卡片
    useEffect(() => {
        if (isSelected && cardRef.current) {
            cardRef.current.focus();
        }
    }, [isSelected]);

    // 获取任务状态的中文描述
    const getStatusLabel = (status: string) => {
        const labels = {
            running: '运行中',
            paused: '已暂停',
            stopped: '已停止',
            error: '错误',
            liquidated: '已清仓'
        };
        return labels[status] || status;
    };

    // 获取盈亏状态描述
    const getPnLLabel = (pnl: number) => {
        if (pnl > 0) return `盈利 ${Math.abs(pnl)} 港币`;
        if (pnl < 0) return `亏损 ${Math.abs(pnl)} 港币`;
        return '持平';
    };

    return (
        <div
            ref={cardRef}
            role="article"
            aria-label={`任务 ${index + 1} / ${totalCount}`}
            aria-describedby={`task-${task.id}-description`}
            aria-selected={isSelected}
            tabIndex={isSelected ? 0 : -1}
            onClick={onSelect}
            onKeyDown={(e) => {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    onSelect();
                }
            }}
            style={{
                outline: isSelected ? '2px solid #007bff' : 'none',
                outlineOffset: '2px'
            }}
        >
            {/* 屏幕阅读器的详细描述 */}
            <div
                id={`task-${task.id}-description`}
                className="sr-only"
            >
                {task.stockName} {task.stockCode}，
                使用策略：{task.strategyName}，
                当前状态：{getStatusLabel(task.status)}，
                {getPnLLabel(task.pnl)}，
                持仓：{task.position} 股
            </div>

            {children}
        </div>
    );
};

// 可访问的按钮组
interface AccessibleButtonGroupProps {
    buttons: Array<{
        id: string;
        label: string;
        onClick: () => void;
        disabled?: boolean;
        variant?: 'primary' | 'secondary' | 'danger';
        icon?: string;
        ariaLabel?: string;
    }>;
}

export const AccessibleButtonGroup: React.FC<AccessibleButtonGroupProps> = ({ buttons }) => {
    return (
        <div
            role="group"
            aria-label="操作按钮组"
            style={{ display: 'flex', gap: '8px' }}
        >
            {buttons.map((button, index) => (
                <button
                    key={button.id}
                    onClick={button.onClick}
                    disabled={button.disabled}
                    aria-label={button.ariaLabel || button.label}
                    aria-disabled={button.disabled}
                    tabIndex={button.disabled ? -1 : 0}
                    className={`btn btn-${button.variant || 'secondary'}`}
                    style={{
                        display: 'flex',
                        alignItems: 'center',
                        gap: '4px'
                    }}
                >
                    {button.icon && <span aria-hidden="true">{button.icon}</span>}
                    <span>{button.label}</span>
                </button>
            ))}
        </div>
    );
};

// 可访问的状态指示器
interface AccessibleStatusIndicatorProps {
    status: string;
    label: string;
}

export const AccessibleStatusIndicator: React.FC<AccessibleStatusIndicatorProps> = ({
    status,
    label
}) => {
    const getStatusConfig = () => {
        const configs = {
            running: { icon: '▶️', color: '#28a745', description: '任务正在运行' },
            paused: { icon: '⏸️', color: '#ffc107', description: '任务已暂停' },
            stopped: { icon: '⏹️', color: '#6c757d', description: '任务已停止' },
            error: { icon: '❌', color: '#dc3545', description: '任务出现错误' },
            liquidated: { icon: '💧', color: '#007bff', description: '任务已清仓' }
        };
        return configs[status] || { icon: '❓', color: '#6c757d', description: '未知状态' };
    };

    const config = getStatusConfig();

    return (
        <div
            role="status"
            aria-label={config.description}
            style={{
                display: 'inline-flex',
                alignItems: 'center',
                gap: '4px',
                padding: '4px 8px',
                borderRadius: '4px',
                backgroundColor: `${config.color}20`,
                color: config.color,
                fontWeight: '500'
            }}
        >
            <span aria-hidden="true">{config.icon}</span>
            <span>{label}</span>
        </div>
    );
};

// 可访问的表单控件
interface AccessibleFormControlProps {
    id: string;
    label: string;
    type?: 'text' | 'number' | 'select';
    value: string | number;
    onChange: (value: string | number) => void;
    options?: Array<{ value: string; label: string }>;
    required?: boolean;
    helpText?: string;
    error?: string;
}

export const AccessibleFormControl: React.FC<AccessibleFormControlProps> = ({
    id,
    label,
    type = 'text',
    value,
    onChange,
    options,
    required,
    helpText,
    error
}) => {
    const inputId = `form-control-${id}`;
    const helpId = helpText ? `${inputId}-help` : undefined;
    const errorId = error ? `${inputId}-error` : undefined;

    return (
        <div className="form-group">
            <label htmlFor={inputId} style={{ display: 'block', marginBottom: '4px' }}>
                {label}
                {required && <span aria-label="必填项" style={{ color: '#dc3545' }}> *</span>}
            </label>

            {type === 'select' && options ? (
                <select
                    id={inputId}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    aria-describedby={[helpId, errorId].filter(Boolean).join(' ')}
                    aria-invalid={!!error}
                    aria-required={required}
                    style={{
                        width: '100%',
                        padding: '8px',
                        borderRadius: '4px',
                        border: error ? '1px solid #dc3545' : '1px solid #ced4da'
                    }}
                >
                    {options.map(option => (
                        <option key={option.value} value={option.value}>
                            {option.label}
                        </option>
                    ))}
                </select>
            ) : (
                <input
                    id={inputId}
                    type={type}
                    value={value}
                    onChange={(e) => onChange(e.target.value)}
                    aria-describedby={[helpId, errorId].filter(Boolean).join(' ')}
                    aria-invalid={!!error}
                    aria-required={required}
                    style={{
                        width: '100%',
                        padding: '8px',
                        borderRadius: '4px',
                        border: error ? '1px solid #dc3545' : '1px solid #ced4da'
                    }}
                />
            )}

            {helpText && (
                <small id={helpId} style={{ display: 'block', marginTop: '4px', color: '#6c757d' }}>
                    {helpText}
                </small>
            )}

            {error && (
                <small id={errorId} role="alert" style={{ display: 'block', marginTop: '4px', color: '#dc3545' }}>
                    {error}
                </small>
            )}
        </div>
    );
};

// Skip Navigation 组件
export const SkipNavigation: React.FC = () => {
    return (
        <a
            href="#main-content"
            className="skip-nav"
            style={{
                position: 'absolute',
                left: '-10000px',
                top: 'auto',
                width: '1px',
                height: '1px',
                overflow: 'hidden',
                ':focus': {
                    position: 'static',
                    width: 'auto',
                    height: 'auto'
                }
            }}
        >
            跳转到主要内容
        </a>
    );
};

// 可访问的颜色对比度检查器
export const checkColorContrast = (foreground: string, background: string): number => {
    // 简化的对比度计算（实际应用中应使用更准确的算法）
    const getLuminance = (color: string) => {
        const rgb = color.match(/\d+/g);
        if (!rgb) return 0;
        
        const [r, g, b] = rgb.map(Number);
        const [rs, gs, bs] = [r, g, b].map(c => {
            c = c / 255;
            return c <= 0.03928 ? c / 12.92 : Math.pow((c + 0.055) / 1.055, 2.4);
        });
        
        return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs;
    };

    const l1 = getLuminance(foreground);
    const l2 = getLuminance(background);
    const lmax = Math.max(l1, l2);
    const lmin = Math.min(l1, l2);
    
    return (lmax + 0.05) / (lmin + 0.05);
};

// 高对比度模式检测
export const useHighContrastMode = () => {
    const [isHighContrast, setIsHighContrast] = useState(false);

    useEffect(() => {
        const mediaQuery = window.matchMedia('(prefers-contrast: high)');
        
        const handleChange = (e: MediaQueryListEvent) => {
            setIsHighContrast(e.matches);
        };

        setIsHighContrast(mediaQuery.matches);
        mediaQuery.addEventListener('change', handleChange);

        return () => mediaQuery.removeEventListener('change', handleChange);
    }, []);

    return isHighContrast;
};

// 焦点环样式
export const focusRingStyles = {
    outline: '2px solid #007bff',
    outlineOffset: '2px',
    borderRadius: '4px'
};

// WCAG 合规性检查
export const wcagCompliance = {
    minContrastNormal: 4.5,
    minContrastLarge: 3,
    minTouchTarget: 44, // 像素
    maxFlashFrequency: 3, // 每秒
    
    checkContrast: (ratio: number, isLargeText: boolean = false) => {
        const threshold = isLargeText ? wcagCompliance.minContrastLarge : wcagCompliance.minContrastNormal;
        return ratio >= threshold;
    },
    
    checkTouchTarget: (width: number, height: number) => {
        return width >= wcagCompliance.minTouchTarget && height >= wcagCompliance.minTouchTarget;
    }
};