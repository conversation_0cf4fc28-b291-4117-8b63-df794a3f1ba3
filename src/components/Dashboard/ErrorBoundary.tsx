import React, { Component, ErrorInfo, ReactNode } from 'react';
import { getButtonClasses, textClasses } from '@/styles/componentStyles';

interface TaskCardErrorBoundaryProps {
    children: ReactNode;
    taskId?: string;
    onRetry?: () => void;
}

interface TaskCardErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
    errorInfo: ErrorInfo | null;
}

export class TaskCardErrorBoundary extends Component<
    TaskCardErrorBoundaryProps,
    TaskCardErrorBoundaryState
> {
    constructor(props: TaskCardErrorBoundaryProps) {
        super(props);
        this.state = {
            hasError: false,
            error: null,
            errorInfo: null
        };
    }

    static getDerivedStateFromError(error: Error): Partial<TaskCardErrorBoundaryState> {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error('TaskCard Error:', error, errorInfo);
        this.setState({ errorInfo });
        
        // 可以在这里上报错误到监控服务
        if (this.props.taskId) {
            console.error(`Error in TaskCard ${this.props.taskId}:`, error);
        }
    }

    handleRetry = () => {
        this.setState({ hasError: false, error: null, errorInfo: null });
        this.props.onRetry?.();
    };

    render() {
        if (this.state.hasError) {
            return (
                <div className="task-card error-card" style={{
                    background: '#fff5f5',
                    border: '2px dashed #dc3545',
                    borderRadius: '8px',
                    padding: '24px',
                    textAlign: 'center'
                }}>
                    <div className="error-content">
                        <div className="error-icon" style={{
                            fontSize: '48px',
                            marginBottom: '16px'
                        }}>
                            ⚠️
                        </div>
                        <h3 className={textClasses.title} style={{
                            color: '#dc3545',
                            marginBottom: '8px'
                        }}>
                            任务卡片加载失败
                        </h3>
                        <p className={textClasses.subtitle} style={{
                            marginBottom: '16px',
                            maxWidth: '300px',
                            margin: '0 auto 16px'
                        }}>
                            {this.state.error?.message || '发生了未知错误'}
                        </p>
                        <button 
                            className={getButtonClasses('primary')}
                            onClick={this.handleRetry}
                            style={{
                                backgroundColor: '#dc3545',
                                border: 'none'
                            }}
                        >
                            重试
                        </button>
                        
                        {/* 开发环境显示错误详情 */}
                        {process.env.NODE_ENV === 'development' && this.state.errorInfo && (
                            <details style={{
                                marginTop: '16px',
                                textAlign: 'left',
                                fontSize: '12px',
                                color: '#666'
                            }}>
                                <summary style={{ cursor: 'pointer' }}>错误详情</summary>
                                <pre style={{
                                    marginTop: '8px',
                                    padding: '8px',
                                    background: '#f8f9fa',
                                    borderRadius: '4px',
                                    overflow: 'auto'
                                }}>
                                    {this.state.errorInfo.componentStack}
                                </pre>
                            </details>
                        )}
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}

// 全局错误边界
interface GlobalErrorBoundaryState {
    hasError: boolean;
    error: Error | null;
}

export class GlobalErrorBoundary extends Component<
    { children: ReactNode },
    GlobalErrorBoundaryState
> {
    constructor(props: { children: ReactNode }) {
        super(props);
        this.state = { hasError: false, error: null };
    }

    static getDerivedStateFromError(error: Error): GlobalErrorBoundaryState {
        return { hasError: true, error };
    }

    componentDidCatch(error: Error, errorInfo: ErrorInfo) {
        console.error('Global Error:', error, errorInfo);
        // 这里可以集成错误监控服务
    }

    render() {
        if (this.state.hasError) {
            return (
                <div style={{
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    minHeight: '100vh',
                    padding: '20px',
                    background: '#f8f9fa'
                }}>
                    <div style={{
                        textAlign: 'center',
                        maxWidth: '500px'
                    }}>
                        <div style={{ fontSize: '72px', marginBottom: '24px' }}>😵</div>
                        <h1 style={{ 
                            fontSize: '24px', 
                            marginBottom: '16px',
                            color: '#343a40'
                        }}>
                            哎呀，出错了！
                        </h1>
                        <p style={{ 
                            fontSize: '16px', 
                            color: '#6c757d',
                            marginBottom: '24px',
                            lineHeight: '1.5'
                        }}>
                            应用程序遇到了意外错误。请刷新页面重试，如果问题持续存在，请联系技术支持。
                        </p>
                        <button
                            onClick={() => window.location.reload()}
                            style={{
                                padding: '12px 24px',
                                fontSize: '16px',
                                color: 'white',
                                background: '#007bff',
                                border: 'none',
                                borderRadius: '6px',
                                cursor: 'pointer'
                            }}
                        >
                            刷新页面
                        </button>
                    </div>
                </div>
            );
        }

        return this.props.children;
    }
}