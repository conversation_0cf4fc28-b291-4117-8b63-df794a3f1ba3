import React, { useState, useEffect } from 'react';
import { TaskStatus } from '../../types';
import { getStatusIcon, getStatusColor } from '../../styles/componentStyles';

export type FilterStatus = 'all' | TaskStatus;

interface TaskFilterProps {
    currentFilter: FilterStatus;
    statusCounts: Record<FilterStatus, number>;
    onFilterChange: (filter: FilterStatus) => void;
    animated?: boolean;
    size?: 'sm' | 'md' | 'lg';
    variant?: 'pills' | 'tabs' | 'buttons';
}

interface FilterConfig {
    label: string;
    icon: string;
    color: string;
    bgColor: string;
    activeColor: string;
    activeBgColor: string;
}

export const TaskFilter: React.FC<TaskFilterProps> = ({
    currentFilter,
    statusCounts,
    onFilterChange,
    animated = true,
    size = 'md',
    variant = 'pills'
}) => {
    const [isAnimating, setIsAnimating] = useState(false);

    // 筛选器配置
    const filterConfigs: Record<FilterStatus, FilterConfig> = {
        all: {
            label: '全部',
            icon: '📊',
            color: '#6c757d',
            bgColor: '#f8f9fa',
            activeColor: '#495057',
            activeBgColor: '#e9ecef'
        },
        running: {
            label: '运行中',
            icon: '▶️',
            color: '#28a745',
            bgColor: '#d4edda',
            activeColor: '#155724',
            activeBgColor: '#c3e6cb'
        },
        paused: {
            label: '暂停',
            icon: '⏸️',
            color: '#ffc107',
            bgColor: '#fff3cd',
            activeColor: '#856404',
            activeBgColor: '#ffeaa7'
        },
        stopped: {
            label: '停止',
            icon: '⏹️',
            color: '#6c757d',
            bgColor: '#e9ecef',
            activeColor: '#495057',
            activeBgColor: '#ced4da'
        },
        error: {
            label: '错误',
            icon: '❌',
            color: '#dc3545',
            bgColor: '#f8d7da',
            activeColor: '#721c24',
            activeBgColor: '#f5c6cb'
        },
        liquidated: {
            label: '已清仓',
            icon: '💧',
            color: '#007bff',
            bgColor: '#d1ecf1',
            activeColor: '#0c5460',
            activeBgColor: '#bee5eb'
        }
    };

    // 获取尺寸样式
    const getSizeStyles = () => {
        switch (size) {
            case 'sm':
                return {
                    button: { padding: '4px 8px', fontSize: '12px', gap: '4px' },
                    badge: { padding: '2px 6px', fontSize: '10px' }
                };
            case 'lg':
                return {
                    button: { padding: '12px 16px', fontSize: '16px', gap: '8px' },
                    badge: { padding: '4px 8px', fontSize: '12px' }
                };
            default: // md
                return {
                    button: { padding: '6px 12px', fontSize: '14px', gap: '6px' },
                    badge: { padding: '2px 8px', fontSize: '11px' }
                };
        }
    };

    const sizeStyles = getSizeStyles();

    // 获取变体样式
    const getVariantStyles = (isActive: boolean, config: FilterConfig) => {
        const baseStyle = {
            ...sizeStyles.button,
            display: 'inline-flex',
            alignItems: 'center',
            border: 'none',
            borderRadius: variant === 'pills' ? '20px' : variant === 'tabs' ? '6px 6px 0 0' : '6px',
            cursor: 'pointer',
            transition: 'all 0.2s ease',
            fontWeight: '500',
            textDecoration: 'none',
            position: 'relative' as const,
            overflow: 'hidden' as const
        };

        if (isActive) {
            return {
                ...baseStyle,
                backgroundColor: config.activeBgColor,
                color: config.activeColor,
                border: `1px solid ${config.color}`,
                transform: 'scale(1.02)',
                boxShadow: `0 2px 8px ${config.color}20`
            };
        }

        return {
            ...baseStyle,
            backgroundColor: 'white',
            color: config.color,
            border: '1px solid #dee2e6',
            ':hover': {
                backgroundColor: config.bgColor,
                borderColor: config.color,
                transform: 'translateY(-1px)'
            }
        };
    };

    // 处理筛选器点击
    const handleFilterClick = (filter: FilterStatus) => {
        if (currentFilter !== filter) {
            if (animated) {
                setIsAnimating(true);
                setTimeout(() => setIsAnimating(false), 300);
            }
            onFilterChange(filter);
        }
    };

    // 持久化筛选状态
    useEffect(() => {
        localStorage.setItem('taskFilter', currentFilter);
    }, [currentFilter]);

    // 获取显示的筛选器列表（只显示有任务的筛选器）
    const getVisibleFilters = (): FilterStatus[] => {
        const allFilters: FilterStatus[] = ['all', 'running', 'paused', 'stopped', 'error', 'liquidated'];
        return allFilters.filter(filter => {
            if (filter === 'all') return true;
            return statusCounts[filter] > 0;
        });
    };

    const visibleFilters = getVisibleFilters();

    return (
        <div 
            className="task-filter"
            style={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: '8px',
                alignItems: 'center',
                justifyContent: 'flex-start'
            }}
        >
            {visibleFilters.map((filter) => {
                const config = filterConfigs[filter];
                const isActive = currentFilter === filter;
                const count = statusCounts[filter];
                
                return (
                    <button
                        key={filter}
                        style={getVariantStyles(isActive, config)}
                        onClick={() => handleFilterClick(filter)}
                        onMouseEnter={(e) => {
                            if (!isActive) {
                                const target = e.currentTarget;
                                target.style.backgroundColor = config.bgColor;
                                target.style.borderColor = config.color;
                                target.style.transform = 'translateY(-1px)';
                            }
                        }}
                        onMouseLeave={(e) => {
                            if (!isActive) {
                                const target = e.currentTarget;
                                target.style.backgroundColor = 'white';
                                target.style.borderColor = '#dee2e6';
                                target.style.transform = 'translateY(0)';
                            }
                        }}
                        aria-pressed={isActive}
                        role="tab"
                        tabIndex={0}
                        title={`筛选${config.label}任务 (${count}个)`}
                    >
                        {/* 图标 */}
                        <span 
                            style={{ 
                                display: size === 'sm' ? 'none' : 'inline',
                                fontSize: size === 'lg' ? '18px' : '14px'
                            }}
                            className={animated && isActive ? 'filter-icon-active' : ''}
                        >
                            {config.icon}
                        </span>
                        
                        {/* 标签 */}
                        <span>{config.label}</span>
                        
                        {/* 数量徽章 */}
                        <span 
                            style={{
                                ...sizeStyles.badge,
                                backgroundColor: isActive ? config.activeColor : config.color,
                                color: 'white',
                                borderRadius: '12px',
                                fontWeight: '600',
                                minWidth: size === 'sm' ? '16px' : '20px',
                                textAlign: 'center',
                                opacity: animated && isAnimating ? 0.6 : 1,
                                transition: 'all 0.2s ease'
                            }}
                        >
                            {count}
                        </span>

                        {/* 激活指示器 */}
                        {isActive && (
                            <div
                                style={{
                                    position: 'absolute',
                                    bottom: 0,
                                    left: 0,
                                    right: 0,
                                    height: '2px',
                                    backgroundColor: config.color,
                                    animation: animated ? 'slideIn 0.3s ease-out' : 'none'
                                }}
                            />
                        )}
                    </button>
                );
            })}
        </div>
    );
};

// 筛选器状态持久化Hook
export const useFilterPersistence = (initialFilter: FilterStatus = 'all') => {
    const [filter, setFilter] = useState<FilterStatus>(() => {
        const saved = localStorage.getItem('taskFilter');
        return (saved as FilterStatus) || initialFilter;
    });

    const updateFilter = (newFilter: FilterStatus) => {
        setFilter(newFilter);
        localStorage.setItem('taskFilter', newFilter);
    };

    return [filter, updateFilter] as const;
};

// 筛选器动画样式
const filterStyleSheet = document.createElement('style');
filterStyleSheet.textContent = `
    @keyframes slideIn {
        from {
            transform: scaleX(0);
            opacity: 0;
        }
        to {
            transform: scaleX(1);
            opacity: 1;
        }
    }
    
    @keyframes filterIconActive {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.2); }
    }
    
    .filter-icon-active {
        animation: filterIconActive 0.6s ease-in-out;
    }
    
    /* 移动端优化 */
    @media (max-width: 576px) {
        .task-filter {
            justify-content: center !important;
            gap: 4px !important;
        }
    }
    
    /* 平板优化 */
    @media (min-width: 577px) and (max-width: 768px) {
        .task-filter {
            justify-content: flex-start !important;
        }
    }
    
    /* 桌面优化 */
    @media (min-width: 769px) {
        .task-filter {
            justify-content: flex-end !important;
        }
    }
`;

if (!document.head.querySelector('style[data-filter-styles]')) {
    filterStyleSheet.setAttribute('data-filter-styles', 'true');
    document.head.appendChild(filterStyleSheet);
}

// 筛选结果统计组件
interface FilterStatsProps {
    totalTasks: number;
    filteredTasks: number;
    currentFilter: FilterStatus;
    filterLabel?: string;
}

export const FilterStats: React.FC<FilterStatsProps> = ({
    totalTasks,
    filteredTasks,
    currentFilter,
    filterLabel
}) => {
    if (currentFilter === 'all') {
        return (
            <p style={{ 
                fontSize: '14px', 
                color: '#6c757d', 
                margin: '8px 0 0 0',
                fontStyle: 'italic'
            }}>
                显示全部 {totalTasks} 个任务
            </p>
        );
    }

    return (
        <p style={{ 
            fontSize: '14px', 
            color: '#6c757d', 
            margin: '8px 0 0 0',
            fontStyle: 'italic'
        }}>
            {filterLabel || currentFilter} 任务：{filteredTasks} 个 / 共 {totalTasks} 个
        </p>
    );
};