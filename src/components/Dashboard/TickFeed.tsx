import React from 'react';
import { MockTickData } from '../../types';

interface TickFeedProps {
    tickData: MockTickData[];
    maxItems?: number;
}

export const TickFeed: React.FC<TickFeedProps> = ({ 
    tickData, 
    maxItems = 20 
}) => {
    const displayData = tickData.slice(0, maxItems);

    const formatVolume = (volume: number) => {
        if (volume >= 10000) {
            return `${(volume / 10000).toFixed(1)}万`;
        }
        return volume.toLocaleString();
    };

    const formatPrice = (price: number) => {
        return price.toFixed(2);
    };

    const getDirectionClass = (direction: MockTickData['direction']) => {
        switch (direction) {
            case 'buy':
                return 'tick-buy';
            case 'sell':
                return 'tick-sell';
            default:
                return 'tick-neutral';
        }
    };

    const getDirectionText = (direction: MockTickData['direction']) => {
        switch (direction) {
            case 'buy':
                return '买';
            case 'sell':
                return '卖';
            default:
                return '-';
        }
    };

    return (
        <div className="tick-feed">
            <div className="tick-feed-header">
                <h4>逐笔成交</h4>
                <span className="tick-count">最新 {displayData.length} 笔</span>
            </div>
            
            <div className="tick-feed-content">
                <div className="tick-table">
                    <div className="tick-header">
                        <div className="col-time">时间</div>
                        <div className="col-price">价格</div>
                        <div className="col-volume">数量</div>
                        <div className="col-direction">方向</div>
                        <div className="col-broker">经纪商</div>
                    </div>
                    
                    <div className="tick-body">
                        {displayData.length === 0 ? (
                            <div className="empty-ticks">
                                <p>暂无成交数据</p>
                            </div>
                        ) : (
                            displayData.map((tick, index) => (
                                <div key={`${tick.timestamp.getTime()}-${index}`} 
                                     className={`tick-row ${getDirectionClass(tick.direction)}`}>
                                    <div className="col-time">
                                        {tick.timestamp.toLocaleTimeString()}
                                    </div>
                                    <div className="col-price">
                                        {formatPrice(tick.price)}
                                    </div>
                                    <div className="col-volume">
                                        {formatVolume(tick.volume)}
                                    </div>
                                    <div className="col-direction">
                                        {getDirectionText(tick.direction)}
                                    </div>
                                    <div className="col-broker">
                                        {tick.brokerId || '-'}
                                    </div>
                                </div>
                            ))
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};