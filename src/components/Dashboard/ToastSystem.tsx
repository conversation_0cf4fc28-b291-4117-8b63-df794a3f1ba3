import React, { useState, useEffect, createContext, useContext, useCallback } from 'react';
import ReactDOM from 'react-dom';
import { AnimatedComponent } from './AnimationSystem';

// Toast类型定义
export type ToastType = 'success' | 'error' | 'warning' | 'info';

export interface ToastMessage {
    id: string;
    type: ToastType;
    title?: string;
    message: string;
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
}

interface ToastContextType {
    toasts: ToastMessage[];
    showToast: (message: Omit<ToastMessage, 'id'>) => void;
    removeToast: (id: string) => void;
}

const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Toast组件
const Toast: React.FC<{
    toast: ToastMessage;
    onRemove: (id: string) => void;
}> = ({ toast, onRemove }) => {
    const [isExiting, setIsExiting] = useState(false);

    useEffect(() => {
        const timer = setTimeout(() => {
            handleRemove();
        }, toast.duration || 5000);

        return () => clearTimeout(timer);
    }, [toast.id, toast.duration]);

    const handleRemove = () => {
        setIsExiting(true);
        setTimeout(() => {
            onRemove(toast.id);
        }, 300);
    };

    const getToastStyles = () => {
        const styles = {
            success: {
                icon: '✅',
                bgColor: '#d4edda',
                borderColor: '#c3e6cb',
                textColor: '#155724'
            },
            error: {
                icon: '❌',
                bgColor: '#f8d7da',
                borderColor: '#f5c6cb',
                textColor: '#721c24'
            },
            warning: {
                icon: '⚠️',
                bgColor: '#fff3cd',
                borderColor: '#ffeaa7',
                textColor: '#856404'
            },
            info: {
                icon: 'ℹ️',
                bgColor: '#d1ecf1',
                borderColor: '#bee5eb',
                textColor: '#0c5460'
            }
        };
        return styles[toast.type];
    };

    const style = getToastStyles();

    return (
        <AnimatedComponent 
            animation={isExiting ? 'slideOut' : 'slideInRight'} 
            trigger={true}
        >
            <div
                className="toast"
                style={{
                    minWidth: '300px',
                    maxWidth: '500px',
                    padding: '16px',
                    marginBottom: '12px',
                    background: style.bgColor,
                    border: `1px solid ${style.borderColor}`,
                    borderRadius: '6px',
                    color: style.textColor,
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: '12px',
                    position: 'relative'
                }}
                role="alert"
                aria-live="polite"
            >
                {/* Icon */}
                <span style={{ fontSize: '20px', flexShrink: 0 }}>
                    {style.icon}
                </span>

                {/* Content */}
                <div style={{ flex: 1 }}>
                    {toast.title && (
                        <h4 style={{
                            margin: '0 0 4px 0',
                            fontSize: '14px',
                            fontWeight: '600'
                        }}>
                            {toast.title}
                        </h4>
                    )}
                    <p style={{
                        margin: 0,
                        fontSize: '14px',
                        lineHeight: '1.4'
                    }}>
                        {toast.message}
                    </p>
                    {toast.action && (
                        <button
                            onClick={toast.action.onClick}
                            style={{
                                marginTop: '8px',
                                padding: '4px 8px',
                                fontSize: '12px',
                                color: style.textColor,
                                background: 'transparent',
                                border: `1px solid ${style.textColor}`,
                                borderRadius: '4px',
                                cursor: 'pointer'
                            }}
                        >
                            {toast.action.label}
                        </button>
                    )}
                </div>

                {/* Close button */}
                <button
                    onClick={handleRemove}
                    style={{
                        position: 'absolute',
                        top: '8px',
                        right: '8px',
                        background: 'transparent',
                        border: 'none',
                        color: style.textColor,
                        cursor: 'pointer',
                        padding: '4px',
                        fontSize: '16px',
                        opacity: 0.5,
                        transition: 'opacity 0.2s'
                    }}
                    onMouseEnter={(e) => {
                        e.currentTarget.style.opacity = '1';
                    }}
                    onMouseLeave={(e) => {
                        e.currentTarget.style.opacity = '0.5';
                    }}
                    aria-label="关闭通知"
                >
                    ×
                </button>
            </div>
        </AnimatedComponent>
    );
};

// Toast容器组件
const ToastContainer: React.FC<{ toasts: ToastMessage[]; onRemove: (id: string) => void }> = ({ 
    toasts, 
    onRemove 
}) => {
    return ReactDOM.createPortal(
        <div
            className="toast-container"
            style={{
                position: 'fixed',
                top: '20px',
                right: '20px',
                zIndex: 10000,
                pointerEvents: 'none'
            }}
        >
            <div style={{ pointerEvents: 'auto' }}>
                {toasts.map(toast => (
                    <Toast key={toast.id} toast={toast} onRemove={onRemove} />
                ))}
            </div>
        </div>,
        document.body
    );
};

// Toast Provider
export const ToastProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
    const [toasts, setToasts] = useState<ToastMessage[]>([]);

    const showToast = useCallback((message: Omit<ToastMessage, 'id'>) => {
        const id = Date.now().toString();
        setToasts(prev => [...prev, { ...message, id }]);
    }, []);

    const removeToast = useCallback((id: string) => {
        setToasts(prev => prev.filter(t => t.id !== id));
    }, []);

    return (
        <ToastContext.Provider value={{ toasts, showToast, removeToast }}>
            {children}
            <ToastContainer toasts={toasts} onRemove={removeToast} />
        </ToastContext.Provider>
    );
};

// Hook使用Toast
export const useToast = () => {
    const context = useContext(ToastContext);
    if (!context) {
        throw new Error('useToast must be used within ToastProvider');
    }
    return context;
};

// 便捷函数
let toastInstance: {
    showToast: (message: Omit<ToastMessage, 'id'>) => void;
} | null = null;

export const toast = {
    success: (message: string, options?: Partial<ToastMessage>) => {
        toastInstance?.showToast({
            type: 'success',
            message,
            ...options
        });
    },
    error: (message: string, options?: Partial<ToastMessage>) => {
        toastInstance?.showToast({
            type: 'error',
            message,
            ...options
        });
    },
    warning: (message: string, options?: Partial<ToastMessage>) => {
        toastInstance?.showToast({
            type: 'warning',
            message,
            ...options
        });
    },
    info: (message: string, options?: Partial<ToastMessage>) => {
        toastInstance?.showToast({
            type: 'info',
            message,
            ...options
        });
    }
};

// 初始化全局Toast实例
export const initializeToast = () => {
    const container = document.createElement('div');
    document.body.appendChild(container);

    const ToastWrapper = () => {
        const [toasts, setToasts] = useState<ToastMessage[]>([]);

        const showToast = useCallback((message: Omit<ToastMessage, 'id'>) => {
            const id = Date.now().toString();
            setToasts(prev => [...prev, { ...message, id }]);
        }, []);

        const removeToast = useCallback((id: string) => {
            setToasts(prev => prev.filter(t => t.id !== id));
        }, []);

        // 设置全局实例
        useEffect(() => {
            toastInstance = { showToast };
            return () => {
                toastInstance = null;
            };
        }, [showToast]);

        return <ToastContainer toasts={toasts} onRemove={removeToast} />;
    };

    ReactDOM.render(<ToastWrapper />, container);
};