import React, { useState, useCallback } from 'react';
import { AnimatedComponent } from './AnimationSystem';

interface LoadingButtonProps {
    onClick: () => Promise<void> | void;
    children: React.ReactNode;
    className?: string;
    style?: React.CSSProperties;
    loadingText?: string;
    disabled?: boolean;
    variant?: 'primary' | 'secondary' | 'danger';
}

export const LoadingButton: React.FC<LoadingButtonProps> = ({
    onClick,
    children,
    className = '',
    style,
    loadingText = '处理中...',
    disabled = false,
    variant = 'primary'
}) => {
    const [isLoading, setIsLoading] = useState(false);

    const handleClick = useCallback(async () => {
        if (isLoading || disabled) return;

        setIsLoading(true);
        try {
            await onClick();
        } catch (error) {
            console.error('Button action failed:', error);
        } finally {
            setIsLoading(false);
        }
    }, [onClick, isLoading, disabled]);

    const getVariantStyles = () => {
        const baseStyles = {
            padding: '8px 16px',
            borderRadius: '6px',
            border: 'none',
            cursor: isLoading || disabled ? 'not-allowed' : 'pointer',
            transition: 'all 0.2s ease',
            display: 'inline-flex',
            alignItems: 'center',
            gap: '8px',
            fontSize: '14px',
            fontWeight: '500'
        };

        const variantStyles = {
            primary: {
                backgroundColor: '#007bff',
                color: 'white',
                opacity: isLoading || disabled ? 0.6 : 1
            },
            secondary: {
                backgroundColor: '#6c757d',
                color: 'white',
                opacity: isLoading || disabled ? 0.6 : 1
            },
            danger: {
                backgroundColor: '#dc3545',
                color: 'white',
                opacity: isLoading || disabled ? 0.6 : 1
            }
        };

        return { ...baseStyles, ...variantStyles[variant] };
    };

    return (
        <button
            className={className}
            style={{ ...getVariantStyles(), ...style }}
            onClick={handleClick}
            disabled={isLoading || disabled}
        >
            {isLoading ? (
                <>
                    <LoadingSpinner size="small" />
                    <span>{loadingText}</span>
                </>
            ) : (
                children
            )}
        </button>
    );
};

// 加载指示器组件
interface LoadingSpinnerProps {
    size?: 'small' | 'medium' | 'large';
    color?: string;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
    size = 'medium',
    color = 'currentColor'
}) => {
    const getSizeStyles = () => {
        switch (size) {
            case 'small':
                return { width: '16px', height: '16px', borderWidth: '2px' };
            case 'large':
                return { width: '48px', height: '48px', borderWidth: '4px' };
            default:
                return { width: '24px', height: '24px', borderWidth: '3px' };
        }
    };

    const sizeStyles = getSizeStyles();

    return (
        <div
            className="loading-spinner"
            style={{
                ...sizeStyles,
                border: `${sizeStyles.borderWidth} solid transparent`,
                borderTopColor: color,
                borderRadius: '50%',
                animation: 'spin 0.8s linear infinite'
            }}
        >
            <style>{`
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `}</style>
        </div>
    );
};

// 加载覆盖层
interface LoadingOverlayProps {
    isLoading: boolean;
    message?: string;
    fullScreen?: boolean;
}

export const LoadingOverlay: React.FC<LoadingOverlayProps> = ({
    isLoading,
    message = '加载中...',
    fullScreen = false
}) => {
    if (!isLoading) return null;

    return (
        <AnimatedComponent animation="fadeIn" trigger={isLoading}>
            <div
                className="loading-overlay"
                style={{
                    position: fullScreen ? 'fixed' : 'absolute',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(255, 255, 255, 0.9)',
                    display: 'flex',
                    flexDirection: 'column',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 1000
                }}
            >
                <LoadingSpinner size="large" color="#007bff" />
                {message && (
                    <p style={{
                        marginTop: '16px',
                        fontSize: '16px',
                        color: '#6c757d'
                    }}>
                        {message}
                    </p>
                )}
            </div>
        </AnimatedComponent>
    );
};

// 操作状态管理Hook
export const useOperationState = <T extends any[]>(
    operation: (...args: T) => Promise<void>
) => {
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<Error | null>(null);
    const [isSuccess, setIsSuccess] = useState(false);

    const execute = useCallback(async (...args: T) => {
        setIsLoading(true);
        setError(null);
        setIsSuccess(false);

        try {
            await operation(...args);
            setIsSuccess(true);
        } catch (err) {
            setError(err as Error);
            throw err;
        } finally {
            setIsLoading(false);
        }
    }, [operation]);

    const reset = useCallback(() => {
        setIsLoading(false);
        setError(null);
        setIsSuccess(false);
    }, []);

    return {
        isLoading,
        error,
        isSuccess,
        execute,
        reset
    };
};

// 内联加载状态
interface InlineLoadingProps {
    isLoading: boolean;
    size?: 'small' | 'medium';
    text?: string;
}

export const InlineLoading: React.FC<InlineLoadingProps> = ({
    isLoading,
    size = 'small',
    text = '加载中'
}) => {
    if (!isLoading) return null;

    return (
        <div style={{
            display: 'inline-flex',
            alignItems: 'center',
            gap: '8px',
            color: '#6c757d',
            fontSize: size === 'small' ? '12px' : '14px'
        }}>
            <LoadingSpinner size={size} />
            <span>{text}</span>
        </div>
    );
};

// 数据加载状态容器
interface DataLoadingContainerProps {
    isLoading: boolean;
    isEmpty: boolean;
    error?: Error | null;
    children: React.ReactNode;
    loadingComponent?: React.ReactNode;
    emptyComponent?: React.ReactNode;
    errorComponent?: React.ReactNode;
}

export const DataLoadingContainer: React.FC<DataLoadingContainerProps> = ({
    isLoading,
    isEmpty,
    error,
    children,
    loadingComponent,
    emptyComponent,
    errorComponent
}) => {
    if (error) {
        return (
            <>
                {errorComponent || (
                    <div style={{
                        textAlign: 'center',
                        padding: '48px',
                        color: '#dc3545'
                    }}>
                        <div style={{ fontSize: '48px', marginBottom: '16px' }}>❌</div>
                        <p>加载失败: {error.message}</p>
                    </div>
                )}
            </>
        );
    }

    if (isLoading) {
        return (
            <>
                {loadingComponent || (
                    <div style={{
                        textAlign: 'center',
                        padding: '48px'
                    }}>
                        <LoadingSpinner size="large" />
                        <p style={{ marginTop: '16px', color: '#6c757d' }}>加载中...</p>
                    </div>
                )}
            </>
        );
    }

    if (isEmpty) {
        return (
            <>
                {emptyComponent || (
                    <div style={{
                        textAlign: 'center',
                        padding: '48px',
                        color: '#6c757d'
                    }}>
                        <div style={{ fontSize: '48px', marginBottom: '16px' }}>📋</div>
                        <p>暂无数据</p>
                    </div>
                )}
            </>
        );
    }

    return <>{children}</>;
};