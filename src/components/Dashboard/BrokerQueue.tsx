import React from 'react';
import { MockBrokerQueue, MockBrokerQueueEntry } from '../../types';

interface BrokerQueueProps {
    brokerQueue: MockBrokerQueue[];
}

export const BrokerQueue: React.FC<BrokerQueueProps> = ({ brokerQueue }) => {
    const formatVolume = (volume: number) => {
        if (volume >= 10000) {
            return `${(volume / 10000).toFixed(1)}万`;
        }
        return volume.toLocaleString();
    };

    const groupedByPrice = brokerQueue.reduce((acc, queue) => {
        const key = `${queue.priceLevel}-${queue.side}`;
        if (!acc[key]) {
            acc[key] = queue;
        }
        return acc;
    }, {} as Record<string, MockBrokerQueue>);

    const sortedQueues = Object.values(groupedByPrice)
        .sort((a, b) => {
            if (a.side === 'buy' && b.side === 'sell') return -1;
            if (a.side === 'sell' && b.side === 'buy') return 1;
            return b.priceLevel - a.priceLevel;
        });

    return (
        <div className="broker-queue">
            <div className="broker-queue-header">
                <h4>经纪商队列</h4>
                <span className="queue-count">
                    {brokerQueue.length > 0 ? `${brokerQueue[0].stockCode}` : ''}
                </span>
            </div>
            
            <div className="broker-queue-content">
                {sortedQueues.length === 0 ? (
                    <div className="empty-queue">
                        <p>暂无经纪商队列数据</p>
                    </div>
                ) : (
                    sortedQueues.map((queue, index) => (
                        <div key={`${queue.priceLevel}-${queue.side}-${index}`} 
                             className={`price-level ${queue.side === 'buy' ? 'buy-side' : 'sell-side'}`}>
                            <div className="price-level-header">
                                <span className="price-level-info">
                                    {queue.side === 'buy' ? '买' : '卖'} {queue.priceLevel.toFixed(2)}
                                </span>
                                <span className="total-volume">
                                    总量: {formatVolume(
                                        queue.brokers.reduce((sum, broker) => sum + broker.volume, 0)
                                    )}
                                </span>
                            </div>
                            
                            <div className="brokers-list">
                                {queue.brokers.map((broker, brokerIndex) => (
                                    <div key={broker.brokerId} className="broker-item">
                                        <div className="broker-info">
                                            <span className="broker-name">{broker.brokerName}</span>
                                            <span className="broker-id">({broker.brokerId})</span>
                                        </div>
                                        <div className="broker-stats">
                                            <span className="volume">
                                                {formatVolume(broker.volume)}
                                            </span>
                                            <span className="orders">
                                                {broker.orders}单
                                            </span>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    ))
                )}
            </div>
        </div>
    );
};