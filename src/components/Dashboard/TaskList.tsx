import React, { useState, useMemo } from "react";
import { Task } from "@/types";
import { TaskCard } from "./TaskCard";
import { TaskFilter, FilterStatus, FilterStats, useFilterPersistence } from "./TaskFilter";
import { StaggeredAnimation, AnimatedComponent, animationSystem } from "./AnimationSystem";
import { layoutClasses, getButtonClasses, textClasses } from "@/styles/componentStyles";

interface TaskListProps {
    tasks: Task[];
    onToggleTask: (taskId: string) => void;
    onShowDetails: (taskId: string) => void;
    onEditTask: (taskId: string) => void;
    onDeleteTask: (taskId: string) => void;
    onLiquidateTask: (taskId: string) => void;
    onCopyTask: (taskId: string) => void;
    onViewLogs: (taskId: string) => void;
}

export const TaskList: React.FC<TaskListProps> = ({ tasks, onToggleTask, onShowDetails, onEditTask, onDeleteTask, onLiquidateTask, onCopyTask, onViewLogs }) => {
    // 使用持久化的筛选状态
    const [filterStatus, setFilterStatus] = useFilterPersistence("all");

    // 计算各状态的任务数量
    const statusCounts = useMemo(() => {
        return {
            all: tasks.length,
            running: tasks.filter((t) => t.status === "running").length,
            paused: tasks.filter((t) => t.status === "paused").length,
            stopped: tasks.filter((t) => t.status === "stopped").length,
            error: tasks.filter((t) => t.status === "error").length,
            liquidated: tasks.filter((t) => t.status === "liquidated").length
        };
    }, [tasks]);

    // 根据筛选状态过滤任务
    const filteredTasks = useMemo(() => {
        if (filterStatus === "all") {
            return tasks;
        }
        return tasks.filter((task) => task.status === filterStatus);
    }, [tasks, filterStatus]);

    // 处理筛选点击（保持兼容性）
    const handleFilterClick = (status: FilterStatus) => {
        setFilterStatus(status);
    };

    if (tasks.length === 0) {
        return (
            <div className="flex flex-col items-center justify-center min-h-[400px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                <div className="text-center space-y-4">
                    <div className="text-6xl">📋</div>
                    <h3 className={`${textClasses.title} text-xl`}>暂无策略任务</h3>
                    <p className={`${textClasses.subtitle} max-w-md`}>点击"添加新任务"开始创建您的第一个量化交易策略</p>
                </div>
            </div>
        );
    }

    return (
        <div className="task-list-container">
            {/* 头部标题和筛选器 - 固定区域 */}
            <div className="task-list-header">
                <div className={layoutClasses.pageHeader}>
                    <div>
                        <h2 className={`${textClasses.title} text-xl md:text-2xl mb-0`}>策略任务</h2>
                    </div>

                    {/* 新的筛选器组件 */}
                    <TaskFilter currentFilter={filterStatus} statusCounts={statusCounts} onFilterChange={handleFilterClick} animated={true} size="sm" variant="pills" />
                </div>
            </div>

            {/* 任务网格 - 可滚动区域 */}
            <div className="task-list-content">
                <div className={layoutClasses.taskGrid}>
                    {filteredTasks.map((task, index) => (
                        <AnimatedComponent
                            key={task.id}
                            animation="fadeInUp"
                            trigger={true}
                            delay={index * 50}
                            config={{
                                duration: animationSystem.getDuration(300),
                                fillMode: "both"
                            }}
                        >
                            <TaskCard
                                task={task}
                                onToggle={onToggleTask}
                                onShowDetails={onShowDetails}
                                onEdit={onEditTask}
                                onDelete={onDeleteTask}
                                onLiquidate={onLiquidateTask}
                                onCopy={onCopyTask}
                                onViewLogs={onViewLogs}
                            />
                        </AnimatedComponent>
                    ))}
                </div>

                {/* 无匹配任务的空状态 */}
                {filteredTasks.length === 0 && filterStatus !== "all" && (
                    <div className="flex flex-col items-center justify-center min-h-[300px] bg-gray-50 rounded-lg border-2 border-dashed border-gray-200">
                        <div className="text-center space-y-4">
                            <div className="text-5xl">🔍</div>
                            <h3 className={`${textClasses.title} text-lg`}>无匹配任务</h3>
                            <p className={`${textClasses.subtitle} max-w-md`}>当前筛选条件下没有找到相关任务</p>
                            <button className={`${getButtonClasses("secondary")} hover:scale-105 active:scale-95 transition-all duration-200`} onClick={() => handleFilterClick("all")}>
                                <span className="mr-2">📊</span>
                                查看全部任务
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};
