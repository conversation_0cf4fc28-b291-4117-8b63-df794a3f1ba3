import React, { useState, useEffect, useCallback, useMemo } from 'react';
import ReactDOM from 'react-dom';
import { AnimatedComponent } from './AnimationSystem';
import { getButtonClasses } from '../../styles/componentStyles';

interface ConfirmDialogProps {
    isOpen: boolean;
    onConfirm: () => void | Promise<void>;
    onCancel: () => void;
    title: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    variant?: 'danger' | 'warning' | 'info';
    confirmButtonVariant?: 'primary' | 'danger';
}

export const ConfirmDialog: React.FC<ConfirmDialogProps> = ({
    isOpen,
    onConfirm,
    onCancel,
    title,
    message,
    confirmText = '确认',
    cancelText = '取消',
    variant = 'warning',
    confirmButtonVariant = 'primary'
}) => {
    console.log('ConfirmDialog 渲染:', { isOpen, title, message });
    const [isConfirming, setIsConfirming] = useState(false);

    const handleConfirm = useCallback(async () => {
        setIsConfirming(true);
        try {
            await onConfirm();
        } finally {
            setIsConfirming(false);
        }
    }, [onConfirm]);

    const handleKeyDown = useCallback((e: KeyboardEvent) => {
        if (e.key === 'Escape') {
            onCancel();
        } else if (e.key === 'Enter') {
            handleConfirm();
        }
    }, [onCancel, handleConfirm]);

    useEffect(() => {
        if (isOpen) {
            document.addEventListener('keydown', handleKeyDown);
            return () => document.removeEventListener('keydown', handleKeyDown);
        }
    }, [isOpen, handleKeyDown]);

    if (!isOpen) return null;

    const getVariantStyles = () => {
        const styles = {
            danger: { icon: '🚨', color: '#dc3545', bgColor: '#f8d7da' },
            warning: { icon: '⚠️', color: '#ffc107', bgColor: '#fff3cd' },
            info: { icon: 'ℹ️', color: '#007bff', bgColor: '#d1ecf1' }
        };
        return styles[variant];
    };

    const variantStyle = getVariantStyles();

    return ReactDOM.createPortal(
        <AnimatedComponent animation="fadeIn" trigger={isOpen}>
            <div
                className="confirm-dialog-overlay"
                style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    zIndex: 9999,
                    padding: '20px'
                }}
                onClick={(e) => {
                    if (e.target === e.currentTarget) onCancel();
                }}
            >
                <AnimatedComponent animation="scaleIn" trigger={isOpen}>
                    <div
                        className="confirm-dialog"
                        style={{
                            background: 'white',
                            borderRadius: '8px',
                            maxWidth: '400px',
                            width: '100%',
                            boxShadow: '0 10px 30px rgba(0, 0, 0, 0.2)',
                            overflow: 'hidden'
                        }}
                        role="dialog"
                        aria-labelledby="confirm-dialog-title"
                        aria-describedby="confirm-dialog-message"
                    >
                        {/* Header */}
                        <div style={{
                            background: variantStyle.bgColor,
                            padding: '16px 20px',
                            borderBottom: `1px solid ${variantStyle.color}20`
                        }}>
                            <h3 
                                id="confirm-dialog-title"
                                style={{
                                    margin: 0,
                                    fontSize: '18px',
                                    fontWeight: '600',
                                    color: '#212529',
                                    display: 'flex',
                                    alignItems: 'center',
                                    gap: '8px'
                                }}
                            >
                                <span style={{ fontSize: '20px' }}>{variantStyle.icon}</span>
                                {title}
                            </h3>
                        </div>

                        {/* Body */}
                        <div style={{ padding: '20px' }}>
                            <p 
                                id="confirm-dialog-message"
                                style={{
                                    margin: 0,
                                    fontSize: '14px',
                                    lineHeight: '1.5',
                                    color: '#495057'
                                }}
                            >
                                {message}
                            </p>
                        </div>

                        {/* Footer */}
                        <div style={{
                            padding: '16px 20px',
                            borderTop: '1px solid #e9ecef',
                            display: 'flex',
                            gap: '8px',
                            justifyContent: 'flex-end'
                        }}>
                            <button
                                className={getButtonClasses('secondary')}
                                onClick={onCancel}
                                disabled={isConfirming}
                            >
                                {cancelText}
                            </button>
                            <button
                                className={getButtonClasses(confirmButtonVariant)}
                                onClick={handleConfirm}
                                disabled={isConfirming}
                                style={{
                                    minWidth: '80px',
                                    position: 'relative'
                                }}
                            >
                                {isConfirming ? (
                                    <>
                                        <span style={{ opacity: 0 }}>{confirmText}</span>
                                        <div style={{
                                            position: 'absolute',
                                            top: '50%',
                                            left: '50%',
                                            transform: 'translate(-50%, -50%)'
                                        }}>
                                            <div className="spinner-small" style={{
                                                width: '16px',
                                                height: '16px',
                                                border: '2px solid transparent',
                                                borderTopColor: 'white',
                                                borderRadius: '50%',
                                                animation: 'spin 0.8s linear infinite'
                                            }}></div>
                                        </div>
                                    </>
                                ) : confirmText}
                            </button>
                        </div>
                    </div>
                </AnimatedComponent>
            </div>
        </AnimatedComponent>,
        document.body
    );
};

// 快捷确认函数
interface ConfirmOptions {
    title?: string;
    message: string;
    confirmText?: string;
    cancelText?: string;
    variant?: 'danger' | 'warning' | 'info';
}

export const confirm = (options: ConfirmOptions): Promise<boolean> => {
    return new Promise((resolve) => {
        const container = document.createElement('div');
        document.body.appendChild(container);

        const cleanup = () => {
            ReactDOM.unmountComponentAtNode(container);
            document.body.removeChild(container);
        };

        const handleConfirm = () => {
            cleanup();
            resolve(true);
        };

        const handleCancel = () => {
            cleanup();
            resolve(false);
        };

        ReactDOM.render(
            <ConfirmDialog
                isOpen={true}
                onConfirm={handleConfirm}
                onCancel={handleCancel}
                title={options.title || '确认操作'}
                message={options.message}
                confirmText={options.confirmText}
                cancelText={options.cancelText}
                variant={options.variant}
                confirmButtonVariant={options.variant === 'danger' ? 'danger' : 'primary'}
            />,
            container
        );
    });
};

// Hook形式使用
export const useConfirmDialog = () => {
    const [dialogState, setDialogState] = useState<{
        isOpen: boolean;
        options: ConfirmOptions;
        resolve?: (value: boolean) => void;
    }>({
        isOpen: false,
        options: { message: '' }
    });

    const showConfirm = useCallback((options: ConfirmOptions): Promise<boolean> => {
        console.log('showConfirm 被调用:', options);
        return new Promise((resolve) => {
            console.log('设置 dialog state:', { isOpen: true, options });
            setDialogState({
                isOpen: true,
                options,
                resolve
            });
        });
    }, []);

    const handleConfirm = useCallback(() => {
        console.log('用户点击确认');
        if (dialogState.resolve) {
            dialogState.resolve(true);
        }
        setDialogState(prev => ({ ...prev, isOpen: false, resolve: undefined }));
    }, [dialogState.resolve]);

    const handleCancel = useCallback(() => {
        console.log('用户点击取消');
        if (dialogState.resolve) {
            dialogState.resolve(false);
        }
        setDialogState(prev => ({ ...prev, isOpen: false, resolve: undefined }));
    }, [dialogState.resolve]);

    // 使用 React.memo 来稳定 DialogComponent 的引用
    const DialogComponent = React.memo(() => {
        console.log('DialogComponent 渲染，dialogState:', dialogState);
        return (
            <ConfirmDialog
                isOpen={dialogState.isOpen}
                onConfirm={handleConfirm}
                onCancel={handleCancel}
                title={dialogState.options.title || '确认操作'}
                message={dialogState.options.message}
                confirmText={dialogState.options.confirmText}
                cancelText={dialogState.options.cancelText}
                variant={dialogState.options.variant}
                confirmButtonVariant={dialogState.options.variant === 'danger' ? 'danger' : 'primary'}
            />
        );
    });

    return { showConfirm, ConfirmDialog: DialogComponent };
};