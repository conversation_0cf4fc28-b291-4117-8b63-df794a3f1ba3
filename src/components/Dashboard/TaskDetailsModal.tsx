import React, { useState, useEffect } from 'react';
import { Task, MockOrderBook, MockTickData, MockBrokerQueue, LogEntry } from '@/types';
import { OrderBook } from './OrderBook';
import { TickFeed } from './TickFeed';
import { BrokerQueue } from './BrokerQueue';
import { LogSystem } from './LogSystem';
import { useSimpleModalOverlay } from '@/hooks/useModalOverlay';

interface TaskDetailsModalProps {
    task: Task;
    onClose: () => void;
}

type LogTabType = 'strategy' | 'trading' | 'system' | 'all';

export const TaskDetailsModal: React.FC<TaskDetailsModalProps> = ({
    task,
    onClose
}) => {
    const [activeLogTab, setActiveLogTab] = useState<LogTabType>('all');
    const [showFullLogs, setShowFullLogs] = useState(false);

    // 使用统一的遮罩层点击处理
    const { handleOverlayClick } = useSimpleModalOverlay(onClose);
    
    // 翻译映射
    const strategyTypeMap: Record<string, string> = {
        'strategy_a_big_order_monitor': '策略A: 大单监控',
        'strategy_b_breakout_chase': '策略B: 突破追涨', 
        'strategy_c_reversal': '策略C: 反转交易',
        'strategy_d_momentum': '策略D: 动量交易'
    };
    
    const paramNameMap: Record<string, string> = {
        // 策略参数
        'monitorThreshold': '监控阈值',
        'durationSeconds': '持续时间(秒)',
        'targetBrokers': '目标经纪商',
        'orderSize': '订单大小',
        'breakoutPeriod': '突破周期',
        'volumeMultiplier': '成交量倍数',
        'pullbackPercent': '回调百分比',
        'oversoldLevel': '超卖水平',
        'rebounds': '反弹次数',
        'volumeFilter': '成交量过滤',
        'lossRatio': '止损比例',
        'stopPrice': '止损价格',
        'basePrice': '基准价格',
        'priceOffset': '价格偏移',
        'direction': '方向',  
        'timeoutSeconds': '超时时间(秒)',
        'timeoutAction': '超时动作',
        'priceCondition': '价格条件',
        'requiresSellStrategy': '需要卖出策略',
        // 卖出策略参数
        'profitPercent': '盈利比例(%)',
        'sellRatio': '卖出比例',
        'holdingMinutes': '持仓时间(分钟)',
        'priorityLevels': '优先级别',
        'priceLevel': '价格档位',
        'quantity': '数量',
        'maxSlippageBps': '最大滑点(基点)',
        // 风控条件参数
        'lossPercentage': '止损比例(%)',
        'profitPercentage': '止盈比例(%)',
        'maxHoldingMinutes': '最大持仓时间(分)',
        'priceDeviationPercent': '价格偏离比例(%)',
        'volumeShrinkageThreshold': '成交量萎缩阈值',
        'volatilityThreshold': '波动率阈值',
        'orderBookSpreadBps': '买卖价差(基点)',
        'trailingStopDistance': '跟踪止损距离',
        'consecutiveLossLimit': '连续亏损次数',
        'trigger': '触发方式',
        'price': '目标价格',
        'timePeriod': '时间周期',
        'timeUnit': '时间单位',
        'targetGroup': '监测对象',
        'behaviorType': '行为类型',
        'volumeThreshold': '成交量阈值',
        // 清仓策略参数
        'aggressiveness': '激进程度',
        'sliceSize': '分片大小',
        'intervalSeconds': '执行间隔(秒)',
        'timeHorizonMinutes': '时间窗口(分钟)',
        'participationRate': '参与率(%)',
        'displaySize': '显示数量',
        'refreshIntervalMs': '刷新间隔(毫秒)'
    };
    
    const riskConditionTypeMap: Record<string, string> = {
        'price': '价格条件',
        'pnl_ratio': '盈亏比例',
        'behavior': '行为条件',
        'time': '时间条件',
        'fast_stop_loss': '快速止损',
        'fast_take_profit': '快速止盈',
        'time_limit': '时间限制',
        'price_deviation': '价格偏离',
        'volume_shrinkage': '成交量萎缩',
        'volatility': '波动率',
        'orderbook_anomaly': '盘口异常',
        'trailing_stop': '跟踪止损',
        'time_based_risk': '时间风险',
        'consecutive_loss_circuit_breaker': '连续亏损熔断'
    };
    
    const sellConditionTypeMap: Record<string, string> = {
        'profit_target': '盈利目标',
        'time_exit': '时间出场',
        'volume_exit': '成交量出场',
        'technical_exit': '技术指标出场',
        'drawdown_exit': '回撤出场'
    };
    
    const sellExecutionTypeMap: Record<string, string> = {
        'priority_levels': '优先级别执行',
        'market_order': '市价单执行',
        'limit_order': '限价单执行',
        'twap_execution': 'TWAP执行',
        'iceberg_execution': '冰山执行'
    };
    
    const liquidationStrategyMap: Record<string, string> = {
        'immediate_market': '即时市价单',
        'smart_execution': '智能限价单',
        'sliced_execution': '分片执行算法',
        'twap_execution': 'TWAP算法',
        'vwap_execution': 'VWAP算法',
        'iceberg_execution': '冰山算法',
        // 向后兼容旧的类型名
        'market': '市价单',
        'limit_optimized': '限价优化',
        'twap_vwap': 'TWAP/VWAP算法'
    };
    
    const paramValueMap: Record<string, string> = {
        'bid1': '买一价',
        'ask1': '卖一价',
        'last': '最新价',
        'up': '向上',
        'down': '向下',
        'cancel_and_market': '撤单转市价',
        'cancel_only': '仅撤单',
        'true': '是',
        'false': '否',
        // 风控相关参数值
        'touch': '触及',
        'break': '突破',
        'above': '高于',
        'below': '低于',
        'buy': '买入',
        'sell': '卖出',
        'seconds': '秒',
        'minutes': '分钟',
        'hours': '小时',
        // 清仓策略参数值
        'low': '保守',
        'medium': '平衡',
        'high': '激进'
    };
    
    // 翻译函数
    const translateStrategyType = (type: string) => {
        return strategyTypeMap[type] || type;
    };
    
    const translateParamName = (name: string) => {
        return paramNameMap[name] || name;
    };
    
    const translateParamValue = (value: any): string => {
        if (typeof value === 'string') {
            return paramValueMap[value] || value;
        }
        if (typeof value === 'boolean') {
            return value ? '是' : '否';
        }
        if (Array.isArray(value)) {
            return value.map(v => translateParamValue(v)).join(', ');
        }
        return String(value);
    };
    
    const translateLiquidationType = (type: string) => {
        return liquidationStrategyMap[type] || type;
    };
    
    const translateRiskConditionType = (type: string) => {
        return riskConditionTypeMap[type] || type;
    };
    
    const translateSellConditionType = (type: string) => {
        return sellConditionTypeMap[type] || type;
    };
    
    const translateSellExecutionType = (type: string) => {
        return sellExecutionTypeMap[type] || type;
    };
    
    // 模拟实时数据
    const [mockOrderBook, setMockOrderBook] = useState<MockOrderBook>({
        stockCode: task.stockCode,
        bids: Array.from({ length: 10 }, (_, i) => ({
            price: 320.5 - i * 0.1,
            volume: Math.floor(Math.random() * 50000) + 10000,
            brokerIds: ['GS', 'MS', 'UBS'].slice(0, Math.floor(Math.random() * 3) + 1)
        })),
        asks: Array.from({ length: 10 }, (_, i) => ({
            price: 320.6 + i * 0.1,
            volume: Math.floor(Math.random() * 50000) + 10000,
            brokerIds: ['HSBC', 'CITIC', 'HT'].slice(0, Math.floor(Math.random() * 3) + 1)
        })),
        timestamp: new Date()
    });

    const [mockTickData, setMockTickData] = useState<MockTickData[]>([]);
    
    const [mockBrokerQueue, setMockBrokerQueue] = useState<MockBrokerQueue[]>([
        {
            stockCode: task.stockCode,
            side: 'buy',
            priceLevel: 320.5,
            brokers: [
                { brokerId: 'GS001', brokerName: '高盛', volume: 15000, orders: 3 },
                { brokerId: 'MS002', brokerName: '摩根士丹利', volume: 22000, orders: 5 },
                { brokerId: 'UBS003', brokerName: '瑞银', volume: 8000, orders: 2 }
            ],
            timestamp: new Date()
        },
        {
            stockCode: task.stockCode,
            side: 'sell',
            priceLevel: 320.6,
            brokers: [
                { brokerId: 'HSBC001', brokerName: '汇丰', volume: 18000, orders: 4 },
                { brokerId: 'CITIC002', brokerName: '中信', volume: 12000, orders: 3 }
            ],
            timestamp: new Date()
        }
    ]);

    const [mockLogs, setMockLogs] = useState<LogEntry[]>([
        {
            id: 'log_1',
            timestamp: new Date(Date.now() - 5000),
            level: 'info',
            category: 'strategy',
            taskId: task.id,
            message: '策略监控启动，开始监控大单信号',
            details: { threshold: 10000 }
        },
        {
            id: 'log_2',
            timestamp: new Date(Date.now() - 10000),
            level: 'info',
            category: 'trading',
            taskId: task.id,
            message: '连接交易服务器成功',
            details: { server: 'trading.example.com' }
        },
        {
            id: 'log_3',
            timestamp: new Date(Date.now() - 15000),
            level: 'warning',
            category: 'system',
            taskId: task.id,
            message: '检测到网络延迟较高',
            details: { latency: 150 }
        },
        {
            id: 'log_4',
            timestamp: new Date(Date.now() - 20000),
            level: 'info',
            category: 'strategy',
            taskId: task.id,
            message: '发现符合条件的大单信号',
            details: { volume: 25000, broker: 'GS001' }
        }
    ]);

    // 模拟实时数据更新
    useEffect(() => {
        const interval = setInterval(() => {
            // 更新tick数据
            const newTick: MockTickData = {
                stockCode: task.stockCode,
                price: 320.5 + (Math.random() - 0.5) * 0.2,
                volume: Math.floor(Math.random() * 10000) + 1000,
                direction: Math.random() > 0.5 ? 'buy' : 'sell',
                timestamp: new Date(),
                brokerId: ['GS001', 'MS002', 'UBS003', 'HSBC001'][Math.floor(Math.random() * 4)]
            };
            
            setMockTickData(prev => [newTick, ...prev.slice(0, 49)]);

            // 偶尔添加新日志
            if (Math.random() > 0.8) {
                const newLog: LogEntry = {
                    id: `log_${Date.now()}`,
                    timestamp: new Date(),
                    level: Math.random() > 0.7 ? 'warning' : 'info',
                    category: ['strategy', 'trading', 'system'][Math.floor(Math.random() * 3)] as any,
                    taskId: task.id,
                    message: [
                        '策略信号检测中...',
                        '市场数据更新',
                        '风控检查通过',
                        '网络连接正常'
                    ][Math.floor(Math.random() * 4)]
                };
                setMockLogs(prev => [newLog, ...prev.slice(0, 49)]);
            }
        }, 3000);

        return () => clearInterval(interval);
    }, [task.stockCode, task.id]);

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'HKD',
            minimumFractionDigits: 2
        }).format(value);
    };

    const formatDateTime = (date: Date) => {
        return new Intl.DateTimeFormat('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        }).format(date);
    };

    const getFilteredLogs = () => {
        if (activeLogTab === 'all') return mockLogs;
        return mockLogs.filter(log => log.category === activeLogTab);
    };

    return (
        <div className="modal-overlay" onClick={handleOverlayClick}>
            <div className="modal-content modal-large task-details-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>详细信息 - {task.stockName} {task.stockCode}</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>
                
                <div className="modal-body">
                    <div className="details-layout">
                        {/* 左侧：基本信息和配置 */}
                        <div className="details-sidebar">
                            {/* 基本信息 */}
                            <div className="details-section">
                                <h3>基本信息</h3>
                                <div className="info-grid">
                                    <div className="info-item">
                                        <label>任务名称:</label>
                                        <span>{task.name}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>股票代码:</label>
                                        <span>{task.stockCode}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>股票名称:</label>
                                        <span>{task.stockName}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>策略类型:</label>
                                        <span>{task.strategyName}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>当前状态:</label>
                                        <span className={`status-badge status-${task.status}`}>
                                            {task.status === 'running' ? '运行中' : 
                                             task.status === 'paused' ? '已暂停' : 
                                             task.status === 'error' ? '错误' : '已停止'}
                                        </span>
                                    </div>
                                    <div className="info-item">
                                        <label>创建时间:</label>
                                        <span>{formatDateTime(task.createdAt)}</span>
                                    </div>
                                    <div className="info-item">
                                        <label>更新时间:</label>
                                        <span>{formatDateTime(task.updatedAt)}</span>
                                    </div>
                                </div>
                            </div>

                            {/* 持仓信息 */}
                            <div className="details-section">
                                <h3>持仓信息</h3>
                                <div className="info-grid">
                                    <div className="info-item">
                                        <label>持仓数量:</label>
                                        <span>{task.position.toLocaleString()} 股</span>
                                    </div>
                                    {task.avgCost && (
                                        <div className="info-item">
                                            <label>平均成本:</label>
                                            <span>{formatCurrency(task.avgCost)}</span>
                                        </div>
                                    )}
                                    <div className="info-item">
                                        <label>浮动盈亏:</label>
                                        <span className={task.pnl >= 0 ? 'profit' : 'loss'}>
                                            {formatCurrency(task.pnl)}
                                        </span>
                                    </div>
                                    {task.position > 0 && task.avgCost && (
                                        <div className="info-item">
                                            <label>持仓市值:</label>
                                            <span>{formatCurrency(task.position * task.avgCost + task.pnl)}</span>
                                        </div>
                                    )}
                                </div>
                            </div>

                            {/* 策略配置 */}
                            <div className="details-section">
                                <h3>策略配置</h3>
                                <div className="strategy-config">
                                    <div className="config-item">
                                        <label>策略类型:</label>
                                        <span>{translateStrategyType(task.strategyConfig.strategyType)}</span>
                                    </div>
                                    <div className="config-params">
                                        <label>策略参数:</label>
                                        <div className="params-list">
                                            {Object.entries(task.strategyConfig.params || {}).map(([key, value]) => (
                                                <div key={key} className="param-item">
                                                    <span className="param-key">{translateParamName(key)}:</span>
                                                    <span className="param-value">
                                                        {translateParamValue(value)}
                                                    </span>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* 卖出策略配置 */}
                            {task.strategyConfig.sellStrategy?.enabled && (
                                <div className="details-section">
                                    <h3>卖出策略</h3>
                                    <div className="strategy-config">
                                        <div className="config-item">
                                            <label>策略状态:</label>
                                            <span>已启用</span>
                                        </div>
                                        <div className="config-item">
                                            <label>卖出条件数:</label>
                                            <span>{task.strategyConfig.sellStrategy.conditions?.length || 0}</span>
                                        </div>
                                        <div className="config-item">
                                            <label>执行方式:</label>
                                            <span>{translateSellExecutionType(task.strategyConfig.sellStrategy.execution?.type || '-')}</span>
                                        </div>
                                        
                                        {/* 卖出条件详情 */}
                                        {task.strategyConfig.sellStrategy.conditions && task.strategyConfig.sellStrategy.conditions.length > 0 && (
                                            <div className="config-params">
                                                <label>卖出条件:</label>
                                                <div className="params-list">
                                                    {task.strategyConfig.sellStrategy.conditions.map((condition, index) => (
                                                        <div key={condition.id} className="condition-block">
                                                            <div className="param-item condition-header">
                                                                <span className="param-key">条件{index + 1}:</span>
                                                                <span className="param-value">{translateSellConditionType(condition.type)}</span>
                                                            </div>
                                                            <div className="condition-params">
                                                                {Object.entries(condition.params).map(([key, value]) => (
                                                                    <div key={key} className="param-item param-detail">
                                                                        <span className="param-key">• {translateParamName(key)}:</span>
                                                                        <span className="param-value">{translateParamValue(value)}</span>
                                                                    </div>
                                                                ))}
                                                            </div>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                        
                                        {/* 执行参数详情 */}
                                        {task.strategyConfig.sellStrategy.execution?.params && Object.keys(task.strategyConfig.sellStrategy.execution.params).length > 0 && (
                                            <div className="config-params">
                                                <label>执行参数:</label>
                                                <div className="params-list">
                                                    {Object.entries(task.strategyConfig.sellStrategy.execution.params).map(([key, value]) => (
                                                        <div key={key} className="param-item">
                                                            <span className="param-key">{translateParamName(key)}:</span>
                                                            <span className="param-value">
                                                                {translateParamValue(value)}
                                                            </span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        )}
                                    </div>
                                </div>
                            )}

                            {/* 风控配置 */}
                            <div className="details-section">
                                <h3>风控配置</h3>
                                <div className="risk-config">
                                    <div className="config-item">
                                        <label>触发逻辑:</label>
                                        <span>{task.riskConfig.triggerLogic === 'any' ? '满足任一条件' : '满足全部条件'}</span>
                                    </div>
                                    <div className="config-item">
                                        <label>风险条件数:</label>
                                        <span>{task.riskConfig.conditions.length}</span>
                                    </div>
                                    <div className="config-item">
                                        <label>清仓策略:</label>
                                        <span>{translateLiquidationType(task.riskConfig.liquidationStrategy.type)}</span>
                                    </div>
                                    
                                    {/* 风险条件详情 */}
                                    <div className="config-params">
                                        <label>风险条件:</label>
                                        <div className="params-list">
                                            {task.riskConfig.conditions.map((condition, index) => (
                                                <div key={condition.id} className="condition-block">
                                                    <div className="param-item condition-header">
                                                        <span className="param-key">条件{index + 1}:</span>
                                                        <span className="param-value">{translateRiskConditionType(condition.type)}</span>
                                                    </div>
                                                    <div className="condition-params">
                                                        {Object.entries(condition.params).map(([key, value]) => (
                                                            <div key={key} className="param-item param-detail">
                                                                <span className="param-key">• {translateParamName(key)}:</span>
                                                                <span className="param-value">{translateParamValue(value)}</span>
                                                            </div>
                                                        ))}
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                    
                                    {/* 清仓参数详情 */}
                                    {Object.keys(task.riskConfig.liquidationStrategy.params || {}).length > 0 && (
                                        <div className="config-params">
                                            <label>清仓参数:</label>
                                            <div className="params-list">
                                                {Object.entries(task.riskConfig.liquidationStrategy.params || {}).map(([key, value]) => (
                                                    <div key={key} className="param-item">
                                                        <span className="param-key">{translateParamName(key)}:</span>
                                                        <span className="param-value">
                                                            {translateParamValue(value)}
                                                        </span>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* 右侧：实时数据区域 */}
                        <div className="details-main">
                            <div className="realtime-data-tabs">
                                {/* 十档行情 - 独占一行 */}
                                <div className="data-tab-group">
                                    <div className="data-tab active">
                                        <OrderBook orderBook={mockOrderBook} />
                                    </div>
                                </div>
                                
                                {/* 逐笔成交和经纪商队列 - 左右并排 */}
                                <div className="data-tab-group tick-broker-row">
                                    <div className="data-tab tick-feed-tab">
                                        <TickFeed tickData={mockTickData} maxItems={15} />
                                    </div>
                                    <div className="data-tab broker-queue-tab">
                                        <BrokerQueue brokerQueue={mockBrokerQueue} />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div className="modal-footer">
                    {/* 左侧：任务日志状态 */}
                    <div className="footer-log-status" onClick={() => setShowFullLogs(true)}>
                        <span className="log-status-icon">📋</span>
                        <span className="log-status-text">
                            {getFilteredLogs().length > 0 
                                ? `${getFilteredLogs()[0].timestamp.toLocaleTimeString()} ${getFilteredLogs()[0].message.substring(0, 30)}${getFilteredLogs()[0].message.length > 30 ? '...' : ''}`
                                : '暂无日志记录'
                            }
                        </span>
                        <span className="log-status-count">({getFilteredLogs().length})</span>
                    </div>
                    
                    {/* 右侧：操作按钮 */}
                    <div className="footer-actions">
                        <button className="btn btn-primary" onClick={onClose}>
                            关闭
                        </button>
                    </div>
                </div>
                
                {/* 完整日志模态框 */}
                {showFullLogs && (
                    <div className="modal-overlay" onClick={() => setShowFullLogs(false)}>
                        <div className="modal-content modal-medium" onClick={e => e.stopPropagation()}>
                            <div className="modal-header">
                                <h2>任务日志 - {task.stockName} {task.stockCode}</h2>
                                <button className="modal-close" onClick={() => setShowFullLogs(false)}>×</button>
                            </div>
                            <div className="modal-body">
                                <div className="log-tabs-header">
                                    <div className="log-tabs">
                                        <button 
                                            className={`tab-button ${activeLogTab === 'all' ? 'active' : ''}`}
                                            onClick={() => setActiveLogTab('all')}
                                        >
                                            全部
                                        </button>
                                        <button 
                                            className={`tab-button ${activeLogTab === 'strategy' ? 'active' : ''}`}
                                            onClick={() => setActiveLogTab('strategy')}
                                        >
                                            策略日志
                                        </button>
                                        <button 
                                            className={`tab-button ${activeLogTab === 'trading' ? 'active' : ''}`}
                                            onClick={() => setActiveLogTab('trading')}
                                        >
                                            交易日志
                                        </button>
                                        <button 
                                            className={`tab-button ${activeLogTab === 'system' ? 'active' : ''}`}
                                            onClick={() => setActiveLogTab('system')}
                                        >
                                            系统日志
                                        </button>
                                    </div>
                                </div>
                                <div className="log-content">
                                    <LogSystem 
                                        logs={getFilteredLogs()}
                                        maxItems={50}
                                    />
                                </div>
                            </div>
                            <div className="modal-footer">
                                <button className="btn btn-secondary" onClick={() => setShowFullLogs(false)}>
                                    关闭
                                </button>
                            </div>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};