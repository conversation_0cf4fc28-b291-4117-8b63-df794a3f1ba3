import React from 'react';
import { useAnimationSystem, useAnimationPerformance } from './AnimationSystem';

interface AnimationControlPanelProps {
    visible?: boolean;
    onToggle?: () => void;
}

export const AnimationControlPanel: React.FC<AnimationControlPanelProps> = ({ 
    visible = false, 
    onToggle 
}) => {
    const { config, updateConfig, isEnabled } = useAnimationSystem();
    const performanceData = useAnimationPerformance();

    if (!visible) {
        return (
            <button
                onClick={onToggle}
                style={{
                    position: 'fixed',
                    bottom: '20px',
                    right: '20px',
                    width: '50px',
                    height: '50px',
                    borderRadius: '50%',
                    backgroundColor: '#007bff',
                    color: 'white',
                    border: 'none',
                    cursor: 'pointer',
                    fontSize: '20px',
                    boxShadow: '0 4px 12px rgba(0, 123, 255, 0.3)',
                    zIndex: 1000,
                    transition: 'all 0.2s ease'
                }}
                title="动画控制面板"
            >
                🎬
            </button>
        );
    }

    return (
        <div
            style={{
                position: 'fixed',
                bottom: '20px',
                right: '20px',
                width: '300px',
                backgroundColor: 'white',
                border: '1px solid #dee2e6',
                borderRadius: '8px',
                boxShadow: '0 8px 32px rgba(0, 0, 0, 0.1)',
                padding: '16px',
                zIndex: 1000,
                fontSize: '14px'
            }}
        >
            {/* 标题栏 */}
            <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '16px',
                paddingBottom: '8px',
                borderBottom: '1px solid #eee'
            }}>
                <h4 style={{ margin: 0, fontSize: '16px', fontWeight: '600' }}>
                    🎬 动画控制
                </h4>
                <button
                    onClick={onToggle}
                    style={{
                        background: 'none',
                        border: 'none',
                        fontSize: '18px',
                        cursor: 'pointer',
                        padding: '4px'
                    }}
                >
                    ✕
                </button>
            </div>

            {/* 动画开关 */}
            <div style={{ marginBottom: '16px' }}>
                <label style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    cursor: 'pointer',
                    fontWeight: '500'
                }}>
                    <input
                        type="checkbox"
                        checked={config.enableAnimations}
                        onChange={(e) => updateConfig({ enableAnimations: e.target.checked })}
                        style={{ marginRight: '8px' }}
                    />
                    启用动画效果
                </label>
            </div>

            {/* 动画速度 */}
            <div style={{ marginBottom: '16px' }}>
                <label style={{ 
                    display: 'block', 
                    marginBottom: '8px',
                    fontWeight: '500'
                }}>
                    动画速度
                </label>
                <select
                    value={config.animationSpeed}
                    onChange={(e) => updateConfig({ 
                        animationSpeed: e.target.value as 'slow' | 'normal' | 'fast' 
                    })}
                    style={{
                        width: '100%',
                        padding: '6px 8px',
                        border: '1px solid #dee2e6',
                        borderRadius: '4px',
                        backgroundColor: 'white'
                    }}
                    disabled={!config.enableAnimations}
                >
                    <option value="slow">慢速 (1.5x)</option>
                    <option value="normal">正常 (1.0x)</option>
                    <option value="fast">快速 (0.5x)</option>
                </select>
            </div>

            {/* 减少动画选项 */}
            <div style={{ marginBottom: '16px' }}>
                <label style={{ 
                    display: 'flex', 
                    alignItems: 'center', 
                    cursor: 'pointer',
                    fontSize: '13px'
                }}>
                    <input
                        type="checkbox"
                        checked={config.respectReducedMotion}
                        onChange={(e) => updateConfig({ respectReducedMotion: e.target.checked })}
                        style={{ marginRight: '8px' }}
                    />
                    尊重系统减少动画设置
                </label>
            </div>

            {/* 性能监控 */}
            <div style={{ 
                backgroundColor: '#f8f9fa',
                padding: '12px',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
            }}>
                <h5 style={{ 
                    margin: '0 0 8px 0', 
                    fontSize: '14px', 
                    fontWeight: '600',
                    color: '#495057'
                }}>
                    性能监控
                </h5>
                
                <div style={{ fontSize: '12px', color: '#6c757d' }}>
                    <div style={{ marginBottom: '4px' }}>
                        平均帧时间: {performanceData.averageFrameTime.toFixed(2)}ms
                    </div>
                    <div style={{ marginBottom: '4px' }}>
                        掉帧数: {performanceData.droppedFrames}
                    </div>
                    <div style={{ 
                        display: 'flex', 
                        alignItems: 'center',
                        gap: '4px'
                    }}>
                        <div
                            style={{
                                width: '8px',
                                height: '8px',
                                borderRadius: '50%',
                                backgroundColor: performanceData.isPerformanceGood ? '#28a745' : '#dc3545'
                            }}
                        />
                        性能状态: {performanceData.isPerformanceGood ? '良好' : '较差'}
                    </div>
                </div>

                {!performanceData.isPerformanceGood && (
                    <div style={{ 
                        marginTop: '8px',
                        padding: '6px',
                        backgroundColor: '#fff3cd',
                        border: '1px solid #ffeaa7',
                        borderRadius: '4px',
                        fontSize: '11px',
                        color: '#856404'
                    }}>
                        💡 性能较差，建议关闭动画或降低动画速度
                    </div>
                )}
            </div>

            {/* 快速操作 */}
            <div style={{ 
                marginTop: '12px',
                display: 'flex',
                gap: '8px'
            }}>
                <button
                    onClick={() => updateConfig({ 
                        enableAnimations: false 
                    })}
                    style={{
                        flex: 1,
                        padding: '6px 12px',
                        backgroundColor: '#6c757d',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px'
                    }}
                >
                    禁用全部
                </button>
                <button
                    onClick={() => updateConfig({ 
                        enableAnimations: true,
                        animationSpeed: 'normal'
                    })}
                    style={{
                        flex: 1,
                        padding: '6px 12px',
                        backgroundColor: '#007bff',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '12px'
                    }}
                >
                    恢复默认
                </button>
            </div>
        </div>
    );
};