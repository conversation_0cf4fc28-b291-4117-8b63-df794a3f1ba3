import React, { useState, useEffect } from 'react';
import { useSimpleModalOverlay } from '@/hooks/useModalOverlay';

interface HuashengConfig {
    host: string;
    port: number;
    account: string;
    password: string;
    enabled: boolean;
    heartbeatInterval: number;
    reconnectInterval: number;
    maxReconnectAttempts: number;
    commandTimeout: number;
}

interface AccountInfo {
    accountId: string;
    cash: number;
    marketValue: number;
    totalValue: number;
    availableCash: number;
    profit: number;
    profitPercent: number;
    connected: boolean;
    lastUpdate: string;
}

interface TradingConfigModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (config: { huasheng: HuashengConfig }) => Promise<void>;
}

type TabType = 'info' | 'config';

export const TradingConfigModal: React.FC<TradingConfigModalProps> = ({
    isOpen,
    onClose,
    onSave
}) => {
    const [activeTab, setActiveTab] = useState<TabType>('info');
    const [config, setConfig] = useState<HuashengConfig>({
        host: '127.0.0.1',
        port: 8080,
        account: '',
        password: '',
        enabled: true,
        heartbeatInterval: 10000,
        reconnectInterval: 5000,
        maxReconnectAttempts: 10,
        commandTimeout: 30000
    });
    const [accountInfo, setAccountInfo] = useState<AccountInfo | null>(null);
    const [loading, setLoading] = useState(false);
    const [saving, setSaving] = useState(false);
    const [showAdvanced, setShowAdvanced] = useState(false);
    const [infoLoading, setInfoLoading] = useState(false);

    // 使用统一的遮罩层点击处理
    const { handleOverlayClick } = useSimpleModalOverlay(onClose);

    // 加载当前配置
    useEffect(() => {
        if (isOpen) {
            loadCurrentConfig();
        }
    }, [isOpen]);

    // 当切换到信息标签页时加载账户信息
    useEffect(() => {
        if (isOpen && activeTab === 'info') {
            loadAccountInfo();
        }
    }, [isOpen, activeTab]);

    const loadCurrentConfig = async () => {
        setLoading(true);
        try {
            const response = await window.electronAPI.config.get({ section: 'trading' });
            if (response.success && response.data && response.data.huasheng) {
                setConfig(response.data.huasheng);
            }
        } catch (error) {
            console.error('加载交易配置失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const loadAccountInfo = async () => {
        setInfoLoading(true);
        try {
            // 调用华盛API获取真实账户信息
            const response = await window.electronAPI.huasheng.getAccountInfo();
            if (response.success && response.data) {
                setAccountInfo(response.data);
            } else {
                console.error('获取账户信息失败:', response.message);
                setAccountInfo(null);
            }
        } catch (error) {
            console.error('加载账户信息失败:', error);
            setAccountInfo(null);
        } finally {
            setInfoLoading(false);
        }
    };

    const handleSave = async () => {
        setSaving(true);
        try {
            await onSave({ huasheng: config });
            onClose();
        } catch (error) {
            console.error('保存配置失败:', error);
            alert('保存配置失败: ' + (error instanceof Error ? error.message : '未知错误'));
        } finally {
            setSaving(false);
        }
    };

    const handleInputChange = (field: keyof HuashengConfig, value: any) => {
        setConfig(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleTabClick = (tab: TabType) => {
        setActiveTab(tab);
    };

    const formatCurrency = (value: number) => {
        return new Intl.NumberFormat('zh-CN', {
            style: 'currency',
            currency: 'HKD',
            minimumFractionDigits: 2
        }).format(value);
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={handleOverlayClick}>
            <div className="modal-content task-config-modal" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>💰 交易系统</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>

                {/* 标签页导航 */}
                <div className="modal-tabs">
                    <button 
                        className={`tab-button ${activeTab === 'info' ? 'active' : ''}`}
                        onClick={() => handleTabClick('info')}
                    >
                        账户信息
                    </button>
                    <button 
                        className={`tab-button ${activeTab === 'config' ? 'active' : ''}`}
                        onClick={() => handleTabClick('config')}
                    >
                        交易配置
                    </button>
                </div>

                <div className="modal-body">
                    {activeTab === 'info' && (
                        <div className="tab-content">
                            {infoLoading ? (
                                <div style={{ 
                                    textAlign: 'center', 
                                    padding: '40px', 
                                    color: '#495057'
                                }}>
                                    加载账户信息中...
                                </div>
                            ) : accountInfo ? (
                                <div>
                                    {/* 连接状态 */}
                                    <div style={{
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'space-between',
                                        marginBottom: '20px',
                                        padding: '15px',
                                        background: accountInfo.connected ? '#d4edda' : '#f8d7da',
                                        border: `1px solid ${accountInfo.connected ? '#c3e6cb' : '#f5c6cb'}`,
                                        borderRadius: '6px'
                                    }}>
                                        <div style={{ display: 'flex', alignItems: 'center' }}>
                                            <span style={{ 
                                                fontSize: '18px', 
                                                marginRight: '10px' 
                                            }}>
                                                {accountInfo.connected ? '🟢' : '🔴'}
                                            </span>
                                            <div>
                                                <div style={{ fontWeight: 'bold', color: accountInfo.connected ? '#155724' : '#721c24' }}>
                                                    {accountInfo.connected ? '已连接' : '连接断开'}
                                                </div>
                                                <div style={{ fontSize: '12px', color: '#6c757d' }}>
                                                    账户: {accountInfo.accountId}
                                                </div>
                                            </div>
                                        </div>
                                        <button 
                                            onClick={loadAccountInfo}
                                            className="btn btn-primary"
                                            style={{ fontSize: '12px', padding: '4px 8px' }}
                                        >
                                            刷新
                                        </button>
                                    </div>

                                    {/* 资金信息 */}
                                    <div style={{
                                        background: '#f8f9fa',
                                        border: '1px solid #e9ecef',
                                        borderRadius: '6px',
                                        padding: '20px',
                                        marginBottom: '20px'
                                    }}>
                                        <h4 style={{ margin: '0 0 15px 0', color: '#495057' }}>资金概览</h4>
                                        
                                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                                            <div className="form-group">
                                                <label>总资产</label>
                                                <div style={{ 
                                                    fontSize: '18px', 
                                                    fontWeight: 'bold', 
                                                    color: '#2c3e50',
                                                    fontFamily: 'monospace'
                                                }}>
                                                    {formatCurrency(accountInfo.totalValue)}
                                                </div>
                                            </div>
                                            <div className="form-group">
                                                <label>现金余额</label>
                                                <div style={{ 
                                                    fontSize: '18px', 
                                                    fontWeight: 'bold', 
                                                    color: '#495057',
                                                    fontFamily: 'monospace'
                                                }}>
                                                    {formatCurrency(accountInfo.cash)}
                                                </div>
                                            </div>
                                        </div>

                                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px', marginBottom: '15px' }}>
                                            <div className="form-group">
                                                <label>持仓市值</label>
                                                <div style={{ 
                                                    fontSize: '16px', 
                                                    fontWeight: '500', 
                                                    color: '#495057',
                                                    fontFamily: 'monospace'
                                                }}>
                                                    {formatCurrency(accountInfo.marketValue)}
                                                </div>
                                            </div>
                                            <div className="form-group">
                                                <label>可用资金</label>
                                                <div style={{ 
                                                    fontSize: '16px', 
                                                    fontWeight: '500', 
                                                    color: '#495057',
                                                    fontFamily: 'monospace'
                                                }}>
                                                    {formatCurrency(accountInfo.availableCash)}
                                                </div>
                                            </div>
                                        </div>

                                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                                            <div className="form-group">
                                                <label>浮动盈亏</label>
                                                <div style={{ 
                                                    fontSize: '16px', 
                                                    fontWeight: 'bold', 
                                                    color: accountInfo.profit >= 0 ? '#28a745' : '#dc3545',
                                                    fontFamily: 'monospace'
                                                }}>
                                                    {accountInfo.profit >= 0 ? '+' : ''}{formatCurrency(accountInfo.profit)}
                                                </div>
                                            </div>
                                            <div className="form-group">
                                                <label>盈亏比例</label>
                                                <div style={{ 
                                                    fontSize: '16px', 
                                                    fontWeight: 'bold', 
                                                    color: accountInfo.profitPercent >= 0 ? '#28a745' : '#dc3545',
                                                    fontFamily: 'monospace'
                                                }}>
                                                    {accountInfo.profitPercent >= 0 ? '+' : ''}{accountInfo.profitPercent.toFixed(2)}%
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* 更新时间 */}
                                    <div style={{ 
                                        textAlign: 'center', 
                                        color: '#6c757d', 
                                        fontSize: '12px' 
                                    }}>
                                        最后更新: {accountInfo.lastUpdate}
                                    </div>
                                </div>
                            ) : (
                                <div style={{
                                    textAlign: 'center',
                                    color: '#6c757d',
                                    padding: '40px',
                                    background: 'white',
                                    border: '2px dashed #dee2e6',
                                    borderRadius: '6px'
                                }}>
                                    <div style={{ fontSize: '48px', marginBottom: '10px' }}>💤</div>
                                    <div style={{ fontWeight: '500' }}>无法获取账户信息</div>
                                    <div style={{ fontSize: '12px', marginTop: '5px' }}>
                                        请检查华盛通连接状态或重新配置
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {activeTab === 'config' && (
                        <div className="tab-content">
                            {loading ? (
                                <div style={{ 
                                    textAlign: 'center', 
                                    padding: '40px', 
                                    color: '#495057'
                                }}>
                                    加载配置中...
                                </div>
                            ) : (
                                <div>
                                    {/* 配置表单 */}
                                    <div style={{
                                        background: '#f8f9fa',
                                        border: '1px solid #e9ecef',
                                        borderRadius: '6px',
                                        padding: '20px'
                                    }}>
                                        {/* 华盛通标题 */}
                                        <div style={{ 
                                            display: 'flex', 
                                            alignItems: 'center', 
                                            marginBottom: '20px',
                                            paddingBottom: '10px',
                                            borderBottom: '1px solid #e9ecef'
                                        }}>
                                            <h3 style={{ margin: 0, color: '#495057' }}>
                                                华盛通交易配置
                                            </h3>
                                            <label style={{ 
                                                display: 'flex',
                                                alignItems: 'center',
                                                marginLeft: 'auto',
                                                cursor: 'pointer',
                                                userSelect: 'none'
                                            }}>
                                                <input
                                                    type="checkbox"
                                                    checked={config.enabled}
                                                    onChange={(e) => handleInputChange('enabled', e.target.checked)}
                                                    style={{ marginRight: '8px' }}
                                                />
                                                <span style={{ 
                                                    fontWeight: config.enabled ? 'bold' : 'normal',
                                                    color: config.enabled ? '#28a745' : '#6c757d'
                                                }}>
                                                    {config.enabled ? '已启用' : '已禁用'}
                                                </span>
                                            </label>
                                        </div>

                                        {/* 服务器连接配置 */}
                                        <div style={{ marginBottom: '20px' }}>
                                            <h4 style={{ 
                                                margin: '0 0 15px 0', 
                                                fontSize: '16px',
                                                color: '#495057'
                                            }}>
                                                服务器连接
                                            </h4>

                                            {/* 服务器地址 */}
                                            <div className="form-group">
                                                <label>
                                                    服务器地址
                                                    <span className="required-asterisk">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    value={config.host}
                                                    onChange={(e) => handleInputChange('host', e.target.value)}
                                                    placeholder="***********"
                                                    disabled={!config.enabled}
                                                />
                                                <small className="field-help">
                                                    华盛通交易服务器的IP地址
                                                </small>
                                            </div>

                                            {/* 端口号 */}
                                            <div className="form-group">
                                                <label>
                                                    端口号
                                                    <span className="required-asterisk">*</span>
                                                </label>
                                                <input
                                                    type="number"
                                                    value={config.port}
                                                    onChange={(e) => handleInputChange('port', parseInt(e.target.value, 10))}
                                                    placeholder="8080"
                                                    min="1"
                                                    max="65535"
                                                    disabled={!config.enabled}
                                                />
                                                <small className="field-help">
                                                    华盛通交易服务的端口号
                                                </small>
                                            </div>
                                        </div>

                                        {/* 账户信息 */}
                                        <div style={{ marginBottom: '20px' }}>
                                            <h4 style={{ 
                                                margin: '0 0 15px 0', 
                                                fontSize: '16px',
                                                color: '#495057'
                                            }}>
                                                账户信息
                                            </h4>

                                            {/* 账户 */}
                                            <div className="form-group">
                                                <label>
                                                    交易账户
                                                    <span className="required-asterisk">*</span>
                                                </label>
                                                <input
                                                    type="text"
                                                    value={config.account}
                                                    onChange={(e) => handleInputChange('account', e.target.value)}
                                                    placeholder="请输入华盛通账户"
                                                    disabled={!config.enabled}
                                                />
                                            </div>

                                            {/* 密码 */}
                                            <div className="form-group">
                                                <label>
                                                    交易密码
                                                    <span className="required-asterisk">*</span>
                                                </label>
                                                <input
                                                    type="password"
                                                    value={config.password}
                                                    onChange={(e) => handleInputChange('password', e.target.value)}
                                                    placeholder="请输入交易密码"
                                                    disabled={!config.enabled}
                                                    style={{ fontFamily: 'monospace' }}
                                                />
                                                <small className="field-help" style={{ color: '#dc3545' }}>
                                                    ⚠️ 密码将以明文形式存储，请确保配置文件安全
                                                </small>
                                            </div>
                                        </div>

                                        {/* 高级设置 */}
                                        <div style={{ borderTop: '1px solid #e9ecef', paddingTop: '15px' }}>
                                            <button
                                                type="button"
                                                onClick={() => setShowAdvanced(!showAdvanced)}
                                                className="btn"
                                                style={{
                                                    background: 'none',
                                                    border: 'none',
                                                    padding: '0',
                                                    color: '#007bff',
                                                    cursor: 'pointer',
                                                    fontSize: '14px',
                                                    display: 'flex',
                                                    alignItems: 'center'
                                                }}
                                            >
                                                <span style={{ marginRight: '5px' }}>
                                                    {showAdvanced ? '▼' : '▶'}
                                                </span>
                                                高级设置
                                            </button>

                                            {showAdvanced && (
                                                <div style={{ marginTop: '15px' }}>
                                                    <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '15px' }}>
                                                        {/* 心跳间隔 */}
                                                        <div className="form-group">
                                                            <label style={{ fontSize: '13px' }}>
                                                                心跳间隔 (ms)
                                                            </label>
                                                            <input
                                                                type="number"
                                                                value={config.heartbeatInterval}
                                                                onChange={(e) => handleInputChange('heartbeatInterval', parseInt(e.target.value, 10))}
                                                                min="1000"
                                                                max="60000"
                                                                disabled={!config.enabled}
                                                                style={{ fontSize: '13px', padding: '6px 8px' }}
                                                            />
                                                        </div>

                                                        {/* 重连间隔 */}
                                                        <div className="form-group">
                                                            <label style={{ fontSize: '13px' }}>
                                                                重连间隔 (ms)
                                                            </label>
                                                            <input
                                                                type="number"
                                                                value={config.reconnectInterval}
                                                                onChange={(e) => handleInputChange('reconnectInterval', parseInt(e.target.value, 10))}
                                                                min="1000"
                                                                max="30000"
                                                                disabled={!config.enabled}
                                                                style={{ fontSize: '13px', padding: '6px 8px' }}
                                                            />
                                                        </div>

                                                        {/* 最大重连次数 */}
                                                        <div className="form-group">
                                                            <label style={{ fontSize: '13px' }}>
                                                                最大重连次数
                                                            </label>
                                                            <input
                                                                type="number"
                                                                value={config.maxReconnectAttempts}
                                                                onChange={(e) => handleInputChange('maxReconnectAttempts', parseInt(e.target.value, 10))}
                                                                min="1"
                                                                max="50"
                                                                disabled={!config.enabled}
                                                                style={{ fontSize: '13px', padding: '6px 8px' }}
                                                            />
                                                        </div>

                                                        {/* 命令超时 */}
                                                        <div className="form-group">
                                                            <label style={{ fontSize: '13px' }}>
                                                                命令超时 (ms)
                                                            </label>
                                                            <input
                                                                type="number"
                                                                value={config.commandTimeout}
                                                                onChange={(e) => handleInputChange('commandTimeout', parseInt(e.target.value, 10))}
                                                                min="5000"
                                                                max="120000"
                                                                disabled={!config.enabled}
                                                                style={{ fontSize: '13px', padding: '6px 8px' }}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>

                <div className="modal-footer">
                    {activeTab === 'config' ? (
                        <>
                            <button
                                className="btn btn-secondary"
                                onClick={onClose}
                                disabled={saving}
                            >
                                取消
                            </button>
                            <button
                                className="btn btn-primary"
                                onClick={handleSave}
                                disabled={saving || (config.enabled && (!config.host || !config.account || !config.password))}
                            >
                                {saving ? '保存中...' : '保存配置'}
                            </button>
                        </>
                    ) : (
                        <button
                            className="btn btn-secondary"
                            onClick={onClose}
                        >
                            关闭
                        </button>
                    )}
                </div>
            </div>
        </div>
    );
};