import React, { useState, useEffect } from 'react';
import { useSimpleModalOverlay } from '@/hooks/useModalOverlay';

interface MarketConfig {
    provider: string;
    host: string;
    port: number;
    encryptKey: string;
    useHttps: boolean;
}

interface MarketConfigModalProps {
    isOpen: boolean;
    onClose: () => void;
    onSave: (config: MarketConfig) => Promise<void>;
}

export const MarketConfigModal: React.FC<MarketConfigModalProps> = ({
    isOpen,
    onClose,
    onSave
}) => {
    const [config, setConfig] = useState<MarketConfig>({
        provider: 'futu',
        host: '127.0.0.1',
        port: 33333,
        encryptKey: '',
        useHttps: false
    });
    const [loading, setLoading] = useState(false);
    const [saving, setSaving] = useState(false);

    // 使用统一的遮罩层点击处理
    const { handleOverlayClick } = useSimpleModalOverlay(onClose);

    // 加载当前配置
    useEffect(() => {
        if (isOpen) {
            loadCurrentConfig();
        }
    }, [isOpen]);

    const loadCurrentConfig = async () => {
        setLoading(true);
        try {
            const response = await window.electronAPI.config.get({ section: 'market' });
            if (response.success && response.data) {
                setConfig(response.data);
            }
        } catch (error) {
            console.error('加载行情配置失败:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleSave = async () => {
        setSaving(true);
        try {
            await onSave(config);
            onClose();
        } catch (error) {
            console.error('保存配置失败:', error);
            alert('保存配置失败: ' + (error instanceof Error ? error.message : '未知错误'));
        } finally {
            setSaving(false);
        }
    };

    const handleInputChange = (field: keyof MarketConfig, value: any) => {
        setConfig(prev => ({
            ...prev,
            [field]: value
        }));
    };

    if (!isOpen) return null;

    return (
        <div className="modal-overlay" onClick={handleOverlayClick}>
            <div className="modal-content" onClick={e => e.stopPropagation()}>
                <div className="modal-header">
                    <h2>📈 行情数据配置</h2>
                    <button className="modal-close" onClick={onClose}>×</button>
                </div>

                <div className="modal-body">
                    {loading ? (
                        <div style={{ 
                            textAlign: 'center', 
                            padding: '40px', 
                            color: '#495057'
                        }}>
                            加载配置中...
                        </div>
                    ) : (
                        <div className="tab-content">
                            {/* 配置表单 */}
                            <div style={{
                                background: '#f8f9fa',
                                border: '1px solid #e9ecef',
                                borderRadius: '6px',
                                padding: '20px'
                            }}>
                                {/* 服务提供商 */}
                                <div className="form-group">
                                    <label>
                                        服务提供商
                                    </label>
                                    <select
                                        value={config.provider}
                                        onChange={(e) => handleInputChange('provider', e.target.value)}
                                        disabled
                                    >
                                        <option value="futu">富途牛牛</option>
                                    </select>
                                    <small className="field-help">
                                        当前仅支持富途牛牛行情数据
                                    </small>
                                </div>

                                {/* 服务器地址 */}
                                <div className="form-group">
                                    <label>
                                        服务器地址
                                        <span className="required-asterisk">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        value={config.host}
                                        onChange={(e) => handleInputChange('host', e.target.value)}
                                        placeholder="127.0.0.1"
                                    />
                                    <small className="field-help">
                                        富途OpenD服务器的IP地址，通常为本机地址
                                    </small>
                                </div>

                                {/* 端口号 */}
                                <div className="form-group">
                                    <label>
                                        端口号
                                        <span className="required-asterisk">*</span>
                                    </label>
                                    <input
                                        type="number"
                                        value={config.port}
                                        onChange={(e) => handleInputChange('port', parseInt(e.target.value, 10))}
                                        placeholder="33333"
                                        min="1"
                                        max="65535"
                                    />
                                    <small className="field-help">
                                        富途OpenD的WebSocket端口，默认为33333
                                    </small>
                                </div>

                                {/* 加密密钥 */}
                                <div className="form-group">
                                    <label>
                                        加密密钥
                                        <span className="required-asterisk">*</span>
                                    </label>
                                    <input
                                        type="text"
                                        value={config.encryptKey}
                                        onChange={(e) => handleInputChange('encryptKey', e.target.value)}
                                        placeholder="请输入16位加密密钥"
                                        maxLength={16}
                                        style={{ fontFamily: 'monospace' }}
                                    />
                                    <small className="field-help">
                                        富途OpenD的WebSocket加密密钥，16位字符串
                                    </small>
                                </div>

                                {/* HTTPS选项 */}
                                <div className="form-group">
                                    <label style={{ 
                                        display: 'flex',
                                        alignItems: 'center',
                                        cursor: 'pointer',
                                        userSelect: 'none'
                                    }}>
                                        <input
                                            type="checkbox"
                                            checked={config.useHttps}
                                            onChange={(e) => handleInputChange('useHttps', e.target.checked)}
                                            style={{ marginRight: '8px' }}
                                        />
                                        启用HTTPS连接
                                    </label>
                                    <small className="field-help" style={{ marginLeft: '24px' }}>
                                        是否使用HTTPS协议连接，通常保持关闭状态
                                    </small>
                                </div>
                            </div>
                        </div>
                    )}
                </div>

                <div className="modal-footer">
                    <button
                        className="btn btn-secondary"
                        onClick={onClose}
                        disabled={saving}
                    >
                        取消
                    </button>
                    <button
                        className="btn btn-primary"
                        onClick={handleSave}
                        disabled={saving || !config.host || !config.encryptKey}
                    >
                        {saving ? '保存中...' : '保存配置'}
                    </button>
                </div>
            </div>
        </div>
    );
};