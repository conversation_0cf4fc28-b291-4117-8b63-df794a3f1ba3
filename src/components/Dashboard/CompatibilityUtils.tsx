import React, { useEffect, useState } from 'react';

// 浏览器特性检测
export const featureDetection = {
    // CSS Grid支持
    supportsGrid: () => {
        return CSS.supports('display', 'grid');
    },
    
    // CSS Variables支持
    supportsCSSVariables: () => {
        return CSS.supports('--custom-property', 'value');
    },
    
    // Flexbox支持
    supportsFlexbox: () => {
        return CSS.supports('display', 'flex');
    },
    
    // IntersectionObserver支持
    supportsIntersectionObserver: () => {
        return 'IntersectionObserver' in window;
    },
    
    // Web Worker支持
    supportsWebWorker: () => {
        return 'Worker' in window;
    },
    
    // Local Storage支持
    supportsLocalStorage: () => {
        try {
            const test = '__localStorage_test__';
            localStorage.setItem(test, test);
            localStorage.removeItem(test);
            return true;
        } catch (e) {
            return false;
        }
    },
    
    // Touch支持
    supportsTouch: () => {
        return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
    }
};

// 渐进增强样式
export const progressiveStyles = `
    /* 基础样式 - 所有浏览器 */
    .task-grid {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        padding: 16px;
    }
    
    .task-card {
        flex: 1 1 300px;
        max-width: 100%;
    }
    
    /* Grid增强 - 支持Grid的浏览器 */
    @supports (display: grid) {
        .task-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            grid-gap: 20px;
        }
        
        .task-card {
            max-width: none;
        }
    }
    
    /* CSS Variables增强 */
    @supports (--css: variables) {
        :root {
            --grid-gap: 20px;
            --card-padding: 16px;
            --primary-color: #007bff;
            --success-color: #28a745;
            --danger-color: #dc3545;
            --warning-color: #ffc107;
        }
        
        .task-grid {
            gap: var(--grid-gap);
            padding: var(--card-padding);
        }
    }
    
    /* Backdrop Filter增强 */
    @supports (backdrop-filter: blur(10px)) {
        .modal-overlay {
            backdrop-filter: blur(10px);
            background-color: rgba(0, 0, 0, 0.3);
        }
    }
    
    /* 不支持backdrop-filter的回退 */
    @supports not (backdrop-filter: blur(10px)) {
        .modal-overlay {
            background-color: rgba(0, 0, 0, 0.5);
        }
    }
    
    /* Sticky定位增强 */
    @supports (position: sticky) {
        .task-header {
            position: sticky;
            top: 0;
            z-index: 10;
            background: white;
        }
    }
    
    /* 不支持sticky的回退 */
    @supports not (position: sticky) {
        .task-header {
            position: relative;
        }
    }
`;

// 浏览器兼容性Hook
export const useBrowserCompatibility = () => {
    const [compatibility, setCompatibility] = useState({
        supportsGrid: true,
        supportsCSSVariables: true,
        supportsFlexbox: true,
        supportsIntersectionObserver: true,
        supportsWebWorker: true,
        supportsLocalStorage: true,
        supportsTouch: false,
        browser: {
            name: 'unknown',
            version: 'unknown',
            isModern: true
        }
    });

    useEffect(() => {
        // 检测浏览器特性
        const features = {
            supportsGrid: featureDetection.supportsGrid(),
            supportsCSSVariables: featureDetection.supportsCSSVariables(),
            supportsFlexbox: featureDetection.supportsFlexbox(),
            supportsIntersectionObserver: featureDetection.supportsIntersectionObserver(),
            supportsWebWorker: featureDetection.supportsWebWorker(),
            supportsLocalStorage: featureDetection.supportsLocalStorage(),
            supportsTouch: featureDetection.supportsTouch()
        };

        // 检测浏览器信息
        const userAgent = navigator.userAgent;
        let browserInfo = {
            name: 'unknown',
            version: 'unknown',
            isModern: true
        };

        if (userAgent.includes('Chrome')) {
            const version = userAgent.match(/Chrome\/(\d+)/)?.[1];
            browserInfo = {
                name: 'Chrome',
                version: version || 'unknown',
                isModern: parseInt(version || '0') >= 80
            };
        } else if (userAgent.includes('Firefox')) {
            const version = userAgent.match(/Firefox\/(\d+)/)?.[1];
            browserInfo = {
                name: 'Firefox',
                version: version || 'unknown',
                isModern: parseInt(version || '0') >= 75
            };
        } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            const version = userAgent.match(/Version\/(\d+)/)?.[1];
            browserInfo = {
                name: 'Safari',
                version: version || 'unknown',
                isModern: parseInt(version || '0') >= 13
            };
        } else if (userAgent.includes('Edge')) {
            const version = userAgent.match(/Edge\/(\d+)/)?.[1];
            browserInfo = {
                name: 'Edge',
                version: version || 'unknown',
                isModern: parseInt(version || '0') >= 80
            };
        }

        setCompatibility({
            ...features,
            browser: browserInfo
        });
    }, []);

    return compatibility;
};

// 降级组件包装器
interface DegradableComponentProps {
    modern: React.ReactNode;
    fallback: React.ReactNode;
    condition?: boolean;
}

export const DegradableComponent: React.FC<DegradableComponentProps> = ({
    modern,
    fallback,
    condition
}) => {
    const compatibility = useBrowserCompatibility();
    
    const shouldUseFallback = condition !== undefined 
        ? !condition 
        : !compatibility.browser.isModern;

    return <>{shouldUseFallback ? fallback : modern}</>;
};

// Polyfill加载器
export const loadPolyfills = async () => {
    const polyfills = [];

    // IntersectionObserver polyfill
    if (!('IntersectionObserver' in window)) {
        polyfills.push(
            import('intersection-observer')
        );
    }

    // ResizeObserver polyfill
    if (!('ResizeObserver' in window)) {
        polyfills.push(
            import('resize-observer-polyfill').then(module => {
                (window as any).ResizeObserver = module.default;
            })
        );
    }

    // Promise polyfill
    if (!window.Promise) {
        polyfills.push(
            import('promise-polyfill').then(module => {
                window.Promise = module.default;
            })
        );
    }

    // Fetch polyfill
    if (!window.fetch) {
        polyfills.push(
            import('whatwg-fetch')
        );
    }

    await Promise.all(polyfills);
};

// 性能降级Hook
export const usePerformanceFallback = () => {
    const [shouldDegrade, setShouldDegrade] = useState(false);
    const [degradationLevel, setDegradationLevel] = useState<'none' | 'low' | 'medium' | 'high'>('none');

    useEffect(() => {
        // 检测设备性能
        const checkPerformance = () => {
            const navigator = window.navigator as any;
            
            // 设备内存检测
            const deviceMemory = navigator.deviceMemory || 8;
            const hardwareConcurrency = navigator.hardwareConcurrency || 4;
            
            // 网络连接检测
            const connection = navigator.connection;
            const effectiveType = connection?.effectiveType || '4g';
            const saveData = connection?.saveData || false;
            
            // 判断降级级别
            if (deviceMemory < 2 || hardwareConcurrency < 2 || effectiveType === 'slow-2g' || saveData) {
                setDegradationLevel('high');
                setShouldDegrade(true);
            } else if (deviceMemory < 4 || effectiveType === '2g') {
                setDegradationLevel('medium');
                setShouldDegrade(true);
            } else if (deviceMemory < 8 || effectiveType === '3g') {
                setDegradationLevel('low');
                setShouldDegrade(true);
            } else {
                setDegradationLevel('none');
                setShouldDegrade(false);
            }
        };

        checkPerformance();

        // 监听网络变化
        if (navigator.connection) {
            navigator.connection.addEventListener('change', checkPerformance);
            return () => navigator.connection.removeEventListener('change', checkPerformance);
        }
    }, []);

    return {
        shouldDegrade,
        degradationLevel,
        // 根据降级级别提供不同的配置
        config: {
            disableAnimations: degradationLevel !== 'none',
            useSimplifiedUI: degradationLevel === 'high',
            reducedDataFetching: degradationLevel !== 'none',
            enableVirtualization: degradationLevel !== 'none',
            reducedImageQuality: degradationLevel === 'high',
            disableBackgroundSync: degradationLevel !== 'none'
        }
    };
};

// 降级样式注入
export const injectFallbackStyles = () => {
    const styleSheet = document.createElement('style');
    styleSheet.textContent = `
        /* 低性能设备样式 */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }
        
        /* 低带宽样式 */
        @media (prefers-reduced-data: reduce) {
            img {
                content-visibility: auto;
            }
            
            .decorative-image {
                display: none;
            }
        }
        
        /* 高对比度模式 */
        @media (prefers-contrast: high) {
            .task-card {
                border: 2px solid currentColor !important;
            }
            
            button {
                border: 2px solid currentColor !important;
            }
        }
        
        /* 暗色模式 */
        @media (prefers-color-scheme: dark) {
            :root {
                --bg-color: #1a1a1a;
                --text-color: #ffffff;
                --border-color: #333;
            }
        }
        
        /* 触摸设备优化 */
        @media (hover: none) and (pointer: coarse) {
            button,
            a,
            .clickable {
                min-height: 44px;
                min-width: 44px;
            }
        }
    `;

    if (!document.head.querySelector('style[data-fallback-styles]')) {
        styleSheet.setAttribute('data-fallback-styles', 'true');
        document.head.appendChild(styleSheet);
    }
};

// 初始化兼容性处理
injectFallbackStyles();