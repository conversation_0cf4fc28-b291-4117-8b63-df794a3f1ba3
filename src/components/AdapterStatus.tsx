// 适配器状态显示组件
import React from 'react';

interface AdapterStatusProps {
    futuConnected: boolean;
    huashengConnected: boolean;
}

const AdapterStatus: React.FC<AdapterStatusProps> = ({ futuConnected, huashengConnected }) => {
    return (
        <div style={{ marginBottom: '20px' }}>
            <h2>📡 适配器状态</h2>
            <div style={{ display: 'flex', gap: '20px' }}>
                <div>
                    <span>富途: </span>
                    <span style={{ color: futuConnected ? 'green' : 'red' }}>
                        {futuConnected ? '✅ 已连接' : '❌ 未连接'}
                    </span>
                </div>
                <div>
                    <span>华盛: </span>
                    <span style={{ color: huashengConnected ? 'green' : 'red' }}>
                        {huashengConnected ? '✅ 已连接' : '❌ 未连接'}
                    </span>
                </div>
            </div>
        </div>
    );
};

export default AdapterStatus;
