// 实时数据流组件
import React from 'react';
import { ElectronRealtimeDataEvent } from '../communication';

interface RealtimeDataStreamProps {
    realtimeData: ElectronRealtimeDataEvent[];
}

const RealtimeDataStream: React.FC<RealtimeDataStreamProps> = ({ realtimeData }) => {
    if (realtimeData.length === 0) {
        return null;
    }

    return (
        <div style={{ marginTop: '20px' }}>
            <h2>📡 实时数据流</h2>
            <div style={{ 
                border: '1px solid #ccc', 
                padding: '10px', 
                maxHeight: '300px', 
                overflowY: 'auto',
                backgroundColor: '#f5f5f5'
            }}>
                {realtimeData.map((event, index) => (
                    <div key={index} style={{ 
                        marginBottom: '5px', 
                        padding: '5px',
                        backgroundColor: 'white',
                        borderRadius: '3px'
                    }}>
                        <strong>{event.adapter}</strong> - 
                        <small> {new Date(event.timestamp * 1000).toLocaleTimeString()}</small>
                        <br />
                        <code>{JSON.stringify(event.data)}</code>
                    </div>
                ))}
            </div>
        </div>
    );
};

export default RealtimeDataStream;
