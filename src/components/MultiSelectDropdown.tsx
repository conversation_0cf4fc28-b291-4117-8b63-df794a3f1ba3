import React, { useState, useRef, useEffect } from 'react';
import { createPortal } from 'react-dom';

interface Option {
    label: string;
    value: string;
}

interface MultiSelectDropdownProps {
    options: Option[];
    value: string[];
    onChange: (selectedValues: string[]) => void;
    placeholder?: string;
    className?: string;
}

export const MultiSelectDropdown: React.FC<MultiSelectDropdownProps> = ({
    options,
    value = [],
    onChange,
    placeholder = "请选择...",
    className = ""
}) => {
    const [isOpen, setIsOpen] = useState(false);
    const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0, width: 0, direction: 'bottom' as 'bottom' | 'top' });
    const dropdownRef = useRef<HTMLDivElement>(null);
    const menuRef = useRef<HTMLDivElement>(null);

    // 点击外部关闭下拉框
    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            // 检查点击是否在触发元素内
            if (dropdownRef.current && dropdownRef.current.contains(event.target as Node)) {
                return;
            }
            
            // 检查点击是否在Portal下拉菜单内
            if (menuRef.current && menuRef.current.contains(event.target as Node)) {
                return;
            }
            
            // 点击在外部，关闭下拉框
            setIsOpen(false);
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }
        
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen]);

    // 监听窗口滚动和大小变化，重新计算位置
    useEffect(() => {
        if (!isOpen) return;

        const handleReposition = () => {
            calculateDropdownPosition();
        };

        window.addEventListener('scroll', handleReposition, true);
        window.addEventListener('resize', handleReposition);

        return () => {
            window.removeEventListener('scroll', handleReposition, true);
            window.removeEventListener('resize', handleReposition);
        };
    }, [isOpen]);

    // 计算下拉框位置
    const calculateDropdownPosition = () => {
        if (!dropdownRef.current) return;

        const rect = dropdownRef.current.getBoundingClientRect();
        const dropdownHeight = 200; // 下拉菜单的最大高度
        const spaceBelow = window.innerHeight - rect.bottom;
        const spaceAbove = rect.top;

        let top = rect.bottom + 2; // 默认向下展开
        let direction: 'bottom' | 'top' = 'bottom';

        // 如果下方空间不足，且上方空间更充足，则向上展开
        if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
            top = rect.top - dropdownHeight - 2; // 向上展开
            direction = 'top';
        }

        setDropdownPosition({
            top,
            left: rect.left,
            width: rect.width,
            direction
        });
    };

    // 打开下拉框时计算位置
    const handleToggleDropdown = () => {
        if (!isOpen) {
            calculateDropdownPosition();
        }
        setIsOpen(!isOpen);
    };

    // 切换选项
    const toggleOption = (optionValue: string) => {
        const newValue = value.includes(optionValue)
            ? value.filter(v => v !== optionValue)
            : [...value, optionValue];
        onChange(newValue);
        // 多选模式下点击选项后不关闭下拉框
    };

    // 移除单个标签
    const removeTag = (optionValue: string, event: React.MouseEvent) => {
        event.stopPropagation();
        const newValue = value.filter(v => v !== optionValue);
        onChange(newValue);
    };

    // 获取显示内容
    const renderSelectedContent = () => {
        if (value.length === 0) {
            return <span className="placeholder">{placeholder}</span>;
        }

        return (
            <div className="selected-tags">
                {value.map(val => {
                    const option = options.find(opt => opt.value === val);
                    return (
                        <span key={val} className="tag">
                            <span className="tag-text">{option?.label || val}</span>
                            <button 
                                className="tag-remove"
                                onClick={(e) => removeTag(val, e)}
                                type="button"
                            >
                                ×
                            </button>
                        </span>
                    );
                })}
            </div>
        );
    };

    // 全选/取消全选
    // const toggleSelectAll = () => {
    //     if (value.length === options.length) {
    //         onChange([]);
    //     } else {
    //         onChange(options.map(opt => opt.value));
    //     }
    // };

    // 渲染下拉菜单的Portal
    const renderDropdownPortal = () => {
        if (!isOpen) return null;

        return createPortal(
            <div 
                ref={menuRef}
                className="dropdown-menu-portal"
                style={{
                    position: 'fixed',
                    top: dropdownPosition.top,
                    left: dropdownPosition.left,
                    width: dropdownPosition.width,
                    zIndex: 9999,
                    background: 'white',
                    border: '1px solid #ddd',
                    borderRadius: '6px',
                    boxShadow: '0 2px 10px rgba(0, 0, 0, 0.1)',
                    overflow: 'hidden'
                }}
            >
                <div className="options-list">
                    {options.map((option) => (
                        <div 
                            key={option.value} 
                            className={`menu-option ${value.includes(option.value) ? 'selected' : ''}`}
                            onClick={(e) => {
                                e.stopPropagation();
                                toggleOption(option.value);
                            }}
                        >
                            <span className="option-text">{option.label}</span>
                        </div>
                    ))}
                </div>
            </div>,
            document.body
        );
    };

    return (
        <div className={`simple-multiselect ${className}`} ref={dropdownRef}>
            {/* 选择框 */}
            <div 
                className={`select-input ${isOpen ? 'open' : ''} ${value.length > 0 ? 'has-value' : ''}`}
                onClick={handleToggleDropdown}
            >
                <div className="input-content">
                    {renderSelectedContent()}
                </div>
                <div className="input-actions">
                    {value.length > 0 && (
                        <button 
                            className="clear-all"
                            onClick={(e) => {
                                e.stopPropagation();
                                onChange([]);
                            }}
                            type="button"
                        >
                            ×
                        </button>
                    )}
                    <div className="dropdown-arrow">
                        <svg 
                            className={`arrow ${isOpen ? 'rotated' : ''}`}
                            width="12" 
                            height="12" 
                            viewBox="0 0 12 12" 
                            fill="none"
                        >
                            <path 
                                d="M3 4.5L6 7.5L9 4.5" 
                                stroke="currentColor" 
                                strokeWidth="1.5" 
                                strokeLinecap="round" 
                                strokeLinejoin="round"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            {/* Portal渲染的下拉菜单 */}
            {renderDropdownPortal()}
        </div>
    );
};