/* 基于现有风格的多选下拉框样式 */
.simple-multiselect {
    position: relative;
    width: 100%;
}

/* 主输入框 - 基于现有的 input 样式 */
.select-input {
    display: flex;
    align-items: center;
    min-height: 44px;
    padding: 8px 12px;
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: border-color 0.3s ease;
    font-size: 14px;
}

.select-input:hover {
    border-color: #999;
}

.select-input.open {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.select-input.has-value {
    padding: 8px 12px;
}

/* 输入内容区域 */
.input-content {
    flex: 1;
    display: flex;
    align-items: center;
    min-height: 24px;
}

/* 占位符 */
.placeholder {
    color: #999;
    font-size: 14px;
    user-select: none;
}

/* 已选择标签容器 */
.selected-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    align-items: center;
    width: 100%;
}

/* 标签样式 - 基于现有的主色调 */
.tag {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    padding: 3px 6px;
    background: #007bff;
    color: white;
    border-radius: 3px;
    font-size: 12px;
    white-space: nowrap;
    max-width: 120px;
}

.tag-text {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tag-remove {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 14px;
    height: 14px;
    border: none;
    background: rgba(255, 255, 255, 0.3);
    color: white;
    border-radius: 2px;
    cursor: pointer;
    font-size: 10px;
    font-weight: bold;
    flex-shrink: 0;
}

.tag-remove:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* 输入框右侧操作区 */
.input-actions {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-left: auto;
    flex-shrink: 0;
}

/* 清除所有按钮 */
.clear-all {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 16px;
    height: 16px;
    border: none;
    background: #dc3545;
    color: white;
    border-radius: 2px;
    cursor: pointer;
    font-size: 12px;
    font-weight: bold;
}

.clear-all:hover {
    background: #c82333;
}

/* 下拉箭头 */
.dropdown-arrow {
    display: flex;
    align-items: center;
    color: #666;
}

.arrow {
    transition: transform 0.3s ease;
}

.arrow.rotated {
    transform: rotate(180deg);
}

/* 下拉菜单 */
.dropdown-menu {
    position: absolute;
    top: calc(100% + 2px);
    left: 0;
    right: 0;
    z-index: 1000;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 向上展开的下拉菜单 */
.dropdown-menu-top {
    top: auto;
    bottom: calc(100% + 2px);
}

/* 选项容器 */
.options-list {
    max-height: 200px;
    overflow-y: auto;
}

/* 菜单选项 */
.menu-option {
    padding: 10px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-bottom: 1px solid #f1f1f1;
    font-size: 14px;
    color: #333;
    position: relative;
}

.menu-option:last-child {
    border-bottom: none;
}

.menu-option:hover {
    background-color: #f8f9fa;
}

/* 选中状态 - 更协调的设计 */
.menu-option.selected {
    background-color: #f8f9fa;
    color: #007bff;
    font-weight: 500;
    border-left: 3px solid #007bff;
    padding-left: 9px; /* 减少左边距以补偿边框 */
}

.menu-option.selected:hover {
    background-color: #e9ecef;
    color: #0056b3;
    border-left-color: #0056b3;
}

/* 选中状态的勾选图标 */
.menu-option.selected::after {
    content: '✓';
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #007bff;
    font-weight: bold;
    font-size: 12px;
}

/* 选项文本 */
.option-text {
    font-size: 14px;
    line-height: 1.4;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}