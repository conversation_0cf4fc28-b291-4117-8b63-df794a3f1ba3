// 数据查询面板组件
import React from 'react';

interface DataQueryPanelProps {
    futuConnected: boolean;
    huashengConnected: boolean;
    loading: boolean;
    onGetFutuQuote: () => void;
    onGetHuashengFunds: () => void;
    onSubscribeFutuRealtime: () => void;
    onPlaceFutuOrder: () => void;
}

const DataQueryPanel: React.FC<DataQueryPanelProps> = ({
    futuConnected,
    huashengConnected,
    loading,
    onGetFutuQuote,
    onGetHuashengFunds,
    onSubscribeFutuRealtime,
    onPlaceFutuOrder
}) => {
    return (
        <div style={{ marginBottom: '20px' }}>
            <h2>📊 数据查询</h2>
            <div style={{ display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                <button onClick={onGetFutuQuote} disabled={loading || !futuConnected}>
                    获取富途报价 (HK.00700)
                </button>
                <button onClick={onGetHuashengFunds} disabled={loading || !huashengConnected}>
                    获取华盛资金
                </button>
                <button onClick={onSubscribeFutuRealtime} disabled={loading || !futuConnected}>
                    订阅富途实时数据
                </button>
                <button onClick={onPlaceFutuOrder} disabled={loading || !futuConnected}>
                    富途下单演示
                </button>
            </div>
        </div>
    );
};

export default DataQueryPanel;
