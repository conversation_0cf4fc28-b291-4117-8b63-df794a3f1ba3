// XX交易终端 - 直接显示量化交易终端
import { Dashboard } from './pages';
import "./App.css";
import "./styles/Dashboard.css";
import "./styles/electron.css";
import { useEffect } from 'react';

function App() {
    useEffect(() => {
        // 添加全局右键菜单支持
        const handleContextMenu = (e: MouseEvent) => {
            const target = e.target as HTMLElement;
            
            // 检查是否是输入框或文本区域
            if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
                e.preventDefault();
                window.electronAPI?.contextMenu?.showContextMenu();
            }
        };

        // 注意：键盘快捷键现在由 Electron 原生菜单处理
        // 这里只保留右键菜单支持

        document.addEventListener('contextmenu', handleContextMenu);

        return () => {
            document.removeEventListener('contextmenu', handleContextMenu);
        };
    }, []);

    return (
        <div style={{ minHeight: '100vh', backgroundColor: '#f8f9fa' }}>
            <Dashboard />
        </div>
    );
}

export default App;
