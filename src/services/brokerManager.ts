import { Broker } from '../types';

// 默认经纪商列表
const DEFAULT_BROKERS: Omit<Broker, 'id' | 'createdAt' | 'isDefault'>[] = [
    { name: '高盛', code: 'goldman_sachs' },
    { name: '摩根士丹利', code: 'morgan_stanley' },
    { name: '瑞银', code: 'ubs' },
    { name: '汇丰', code: 'hsbc' },
    { name: '中信', code: 'citic' },
    { name: '海通', code: 'haitong' }
];

class BrokerManager {
    private storageKey = 'trading_brokers';
    private brokers: Broker[] = [];

    constructor() {
        this.loadBrokers();
    }

    // 加载经纪商列表
    private loadBrokers(): void {
        try {
            const stored = localStorage.getItem(this.storageKey);
            if (stored) {
                const brokerData = JSON.parse(stored);
                this.brokers = brokerData.map((broker: any) => ({
                    ...broker,
                    createdAt: new Date(broker.createdAt)
                }));
            } else {
                // 首次初始化，使用默认经纪商
                this.initializeDefaultBrokers();
            }
        } catch (error) {
            console.error('加载经纪商数据失败:', error);
            this.initializeDefaultBrokers();
        }
    }

    // 初始化默认经纪商
    private initializeDefaultBrokers(): void {
        this.brokers = DEFAULT_BROKERS.map(broker => ({
            ...broker,
            id: this.generateId(),
            createdAt: new Date()
        }));
        this.saveBrokers();
    }

    // 保存经纪商列表到本地存储
    private saveBrokers(): void {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.brokers));
        } catch (error) {
            console.error('保存经纪商数据失败:', error);
        }
    }

    // 生成唯一ID
    private generateId(): string {
        return `broker_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    // 根据名称生成代码
    private generateCodeFromName(name: string): string {
        // 简单的代码生成逻辑：取名称的拼音首字母或英文，加上时间戳
        let baseCode = '';
        
        // 如果包含英文，提取英文字符
        const englishMatch = name.match(/[a-zA-Z]+/g);
        if (englishMatch) {
            baseCode = englishMatch.join('').toLowerCase();
        } else {
            // 如果是中文，生成简单标识
            baseCode = 'broker';
        }
        
        // 确保代码唯一性，添加时间戳后缀
        let finalCode = baseCode;
        let counter = 1;
        
        while (this.brokers.some(broker => broker.code === finalCode)) {
            finalCode = `${baseCode}_${counter}`;
            counter++;
        }
        
        return finalCode;
    }

    // 获取所有经纪商
    getAllBrokers(): Broker[] {
        return [...this.brokers];
    }

    // 获取经纪商选项 (用于下拉选择)
    getBrokerOptions(): { label: string; value: string }[] {
        return this.brokers.map(broker => ({
            label: broker.name,
            value: broker.code
        }));
    }

    // 添加新经纪商
    addBroker(name: string, code: string): { success: boolean; message: string; broker?: Broker } {
        // 验证输入
        if (!name.trim()) {
            return { success: false, message: '经纪商名称不能为空' };
        }

        // 如果代码为空，自动生成
        let finalCode = code.trim();
        if (!finalCode) {
            // 根据名称生成代码，转为拼音或简单的标识
            finalCode = this.generateCodeFromName(name.trim());
        }

        // 检查代码是否重复
        if (this.brokers.some(broker => broker.code === finalCode)) {
            return { success: false, message: '经纪商代码已存在，请手动输入不同的代码' };
        }

        // 检查名称是否重复
        if (this.brokers.some(broker => broker.name === name.trim())) {
            return { success: false, message: '经纪商名称已存在' };
        }

        // 创建新经纪商
        const newBroker: Broker = {
            id: this.generateId(),
            name: name.trim(),
            code: finalCode,
            createdAt: new Date()
        };

        this.brokers.push(newBroker);
        this.saveBrokers();

        return { success: true, message: '经纪商添加成功', broker: newBroker };
    }

    // 删除经纪商
    deleteBroker(id: string): { success: boolean; message: string } {
        const brokerIndex = this.brokers.findIndex(broker => broker.id === id);
        
        if (brokerIndex === -1) {
            return { success: false, message: '经纪商不存在' };
        }

        this.brokers.splice(brokerIndex, 1);
        this.saveBrokers();

        return { success: true, message: '经纪商删除成功' };
    }

    // 更新经纪商
    updateBroker(id: string, name: string, code: string): { success: boolean; message: string; broker?: Broker } {
        const brokerIndex = this.brokers.findIndex(broker => broker.id === id);
        
        if (brokerIndex === -1) {
            return { success: false, message: '经纪商不存在' };
        }

        // 验证输入
        if (!name.trim()) {
            return { success: false, message: '经纪商名称不能为空' };
        }

        // 如果代码为空，使用原有代码或生成新代码
        let finalCode = code.trim();
        if (!finalCode) {
            finalCode = this.brokers[brokerIndex].code; // 保持原有代码
        }

        // 检查代码是否与其他经纪商重复（排除自己）
        if (this.brokers.some((broker, index) => index !== brokerIndex && broker.code === finalCode)) {
            return { success: false, message: '经纪商代码已存在' };
        }

        // 检查名称是否与其他经纪商重复（排除自己）
        if (this.brokers.some((broker, index) => index !== brokerIndex && broker.name === name.trim())) {
            return { success: false, message: '经纪商名称已存在' };
        }

        // 更新经纪商
        this.brokers[brokerIndex] = {
            ...this.brokers[brokerIndex],
            name: name.trim(),
            code: finalCode
        };

        this.saveBrokers();

        return { success: true, message: '经纪商更新成功', broker: this.brokers[brokerIndex] };
    }

    // 根据代码获取经纪商
    getBrokerByCode(code: string): Broker | undefined {
        return this.brokers.find(broker => broker.code === code);
    }

    // 根据ID获取经纪商
    getBrokerById(id: string): Broker | undefined {
        return this.brokers.find(broker => broker.id === id);
    }
}

// 单例实例
export const brokerManager = new BrokerManager();