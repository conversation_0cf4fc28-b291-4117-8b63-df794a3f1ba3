import { useCallback, useRef } from 'react';

/**
 * 统一的弹框遮罩层点击处理 Hook
 * 
 * 解决问题：
 * - 防止在弹框内拖拽选择文字时，鼠标在遮罩层松开导致弹框关闭
 * - 只有在遮罩层上完整点击（mousedown + mouseup 都在遮罩层）才关闭弹框
 * 
 * 使用方式：
 * ```tsx
 * const { handleOverlayClick } = useModalOverlay(onClose);
 * 
 * return (
 *   <div className="modal-overlay" onClick={handleOverlayClick}>
 *     <div className="modal-content" onClick={e => e.stopPropagation()}>
 *       // 弹框内容
 *     </div>
 *   </div>
 * );
 * ```
 */
export const useModalOverlay = (onClose: () => void) => {
    const mouseDownTargetRef = useRef<EventTarget | null>(null);

    const handleOverlayMouseDown = useCallback((e: React.MouseEvent) => {
        // 记录 mousedown 事件的目标元素
        mouseDownTargetRef.current = e.target;
    }, []);

    const handleOverlayClick = useCallback((e: React.MouseEvent) => {
        // 只有当 mousedown 和 click 都发生在遮罩层本身时才关闭弹框
        if (
            e.target === e.currentTarget && 
            mouseDownTargetRef.current === e.currentTarget
        ) {
            onClose();
        }
        // 重置记录
        mouseDownTargetRef.current = null;
    }, [onClose]);

    return {
        handleOverlayClick,
        handleOverlayMouseDown
    };
};

/**
 * 简化版本的遮罩层点击处理
 * 使用 currentTarget 检查，适用于大多数场景
 */
export const useSimpleModalOverlay = (onClose: () => void) => {
    const handleOverlayClick = useCallback((e: React.MouseEvent) => {
        // 只有点击遮罩层本身才关闭弹框
        if (e.target === e.currentTarget) {
            onClose();
        }
    }, [onClose]);

    return { handleOverlayClick };
};

/**
 * 带条件检查的遮罩层点击处理
 * 支持在特定条件下阻止关闭（如有子弹框打开时）
 */
export const useConditionalModalOverlay = (
    onClose: () => void, 
    shouldPreventClose?: () => boolean
) => {
    const { handleOverlayClick: baseHandleClick } = useSimpleModalOverlay(onClose);

    const handleOverlayClick = useCallback((e: React.MouseEvent) => {
        // 检查是否应该阻止关闭
        if (shouldPreventClose && shouldPreventClose()) {
            return;
        }
        baseHandleClick(e);
    }, [baseHandleClick, shouldPreventClose]);

    return { handleOverlayClick };
};
