// 交易演示逻辑 Hook - Electron 版本
import { useState, useEffect } from 'react';
import { electronTradingClient, ConnectionStatus } from '@/communication';

export const useTradingDemo = () => {
    const [electronConnected, setElectronConnected] = useState(false);
    const [loading, setLoading] = useState(false);
    const [systemStatus, setSystemStatus] = useState<any>(null);
    const [realtimeData, setRealtimeData] = useState<any[]>([]);

    useEffect(() => {
        // 设置实时数据监听
        const setupRealtimeListeners = async () => {
            // 监听 Electron 实时数据
            electronTradingClient.onRealtimeData((event) => {
                console.log('Electron 实时数据:', event);
                setRealtimeData(prev => [...prev.slice(-9), event]); // 保留最新10条
            });

            electronTradingClient.onConnectionStatus((data) => {
                console.log('连接状态变化:', data);
                setElectronConnected(data.status === ConnectionStatus.Connected);
            });
        };

        setupRealtimeListeners();

        // 清理函数
        return () => {
            electronTradingClient.destroy();
        };
    }, []);

    // 获取系统状态
    const getSystemStatus = async () => {
        setLoading(true);
        try {
            const result = await electronTradingClient.getTradingSystemStatus();
            if (result.success) {
                setSystemStatus(result.data);
                setElectronConnected(result.data?.initialized || false);
                console.log('系统状态:', result.data);
            } else {
                console.error('获取系统状态失败:', result.message);
            }
        } catch (error) {
            console.error('获取系统状态异常:', error);
        } finally {
            setLoading(false);
        }
    };

    // 初始化系统
    const initializeSystem = async () => {
        setLoading(true);
        try {
            const result = await electronTradingClient.initializeTradingSystem();
            if (result.success) {
                setElectronConnected(true);
                console.log('系统初始化成功');
                await getSystemStatus(); // 刷新状态
            } else {
                console.error('系统初始化失败:', result.message);
            }
        } catch (error) {
            console.error('系统初始化异常:', error);
        } finally {
            setLoading(false);
        }
    };

    // 获取任务列表
    const getTaskList = async () => {
        if (!electronConnected) return;
        
        setLoading(true);
        try {
            const result = await electronTradingClient.getTaskList();
            if (result.success) {
                console.log('任务列表:', result.data);
                return result.data;
            }
        } catch (error) {
            console.error('获取任务列表失败:', error);
        } finally {
            setLoading(false);
        }
    };

    // 演示创建任务
    const createDemoTask = async () => {
        if (!electronConnected) return;
        
        try {
            const demoTaskConfig = {
                name: '演示任务',
                stockCode: 'HK.00700',
                stockName: '腾讯控股',
                market: 'HK' as any,
                strategyConfig: {
                    strategyType: 'strategy_a_big_order_monitor' as any,
                    params: {
                        monitorThreshold: 1000,
                        durationSeconds: 60,
                        targetBrokers: ['goldman_sachs'],
                        orderSize: 100
                    },
                    requiredDataTypes: []
                },
                riskConfig: {
                    triggerLogic: 'any' as any,
                    conditions: [],
                    liquidationStrategy: {
                        type: 'market' as any,
                        params: {}
                    }
                },
                autoStart: false
            };
            
            const result = await electronTradingClient.createTask(demoTaskConfig);
            console.log('创建演示任务结果:', result);
            return result;
        } catch (error) {
            console.error('创建演示任务失败:', error);
        }
    };

    return {
        // 状态
        electronConnected,
        loading,
        systemStatus,
        realtimeData,
        
        // 操作函数
        getSystemStatus,
        initializeSystem,
        getTaskList,
        createDemoTask
    };
};
