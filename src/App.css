/* App.css - 组件特定样式 */
.button-group {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    margin-top: 12px;
}

.button-group .action-btn {
    flex: 1;
    min-width: auto;
    padding: 8px 12px;
    font-size: 0.85rem;
}

.status-indicator {
    padding: 8px;
    background: #f8f9fa;
    border-radius: 4px;
    text-align: center;
    font-size: 0.9rem;
    font-weight: 500;
}

.action-btn.connected {
    background: #27ae60 !important;
}

.action-btn.connected:hover:not(:disabled) {
    background: #229954 !important;
}

/* 状态相关样式 */
.status-text.checking {
    color: #f39c12;
}

.status-actions {
    margin-top: 12px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.status-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 4px;
    background: #3498db;
    color: white;
    font-size: 0.8rem;
    cursor: pointer;
    transition: background-color 0.2s;
}

.status-btn:hover:not(:disabled) {
    background: #2980b9;
}

.status-btn:disabled {
    background: #95a5a6;
    cursor: not-allowed;
}

.status-btn.start {
    background: #27ae60;
}

.status-btn.start:hover:not(:disabled) {
    background: #229954;
}

.status-btn.stop {
    background: #e74c3c;
}

.status-btn.stop:hover:not(:disabled) {
    background: #c0392b;
}
