/* TailwindCSS v4 基础样式 */
@import "tailwindcss";

/* 自定义颜色配置 */
@theme {
  --color-primary-100: #e0f2fe;
  --color-primary-300: #7dd3fc;
  --color-primary-500: #0ea5e9;
  --color-primary-600: #0284c7;
  --color-primary-700: #0369a1;
  
  --color-success-50: #d4edda;
  --color-success-100: #c3e6cb;
  --color-success-200: #c3e6cb;
  --color-success-500: #28a745;
  --color-success-600: #1e7e34;
  --color-success-700: #155724;
  
  --color-warning-50: #fff3cd;
  --color-warning-100: #ffeaa7;
  --color-warning-200: #ffeaa7;
  --color-warning-500: #ffc107;
  --color-warning-700: #856404;
  --color-warning-900: #212529;
  
  --color-error-50: #f8d7da;
  --color-error-100: #f5c6cb;
  --color-error-200: #f5c6cb;
  --color-error-500: #dc3545;
  --color-error-600: #c82333;
  --color-error-700: #721c24;
  
  --color-liquidated-50: rgba(0, 123, 255, 0.1);
  --color-liquidated-100: rgba(0, 123, 255, 0.2);
  --color-liquidated-200: rgba(0, 123, 255, 0.3);
  --color-liquidated-500: #007bff;
  --color-liquidated-600: #0056b3;
  --color-liquidated-700: #004085;
  
  --font-mono: "SF Mono", "Monaco", "Consolas", "Roboto Mono", monospace;
  --font-size-2xs: 0.625rem;
  --min-height-280: 280px;
  --min-height-320: 320px;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 自定义工具类 */
@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.3s ease-out forwards;
  }
  
  .animate-slide-up {
    animation: slideUp 0.4s ease-out forwards;
  }
  
  .animation-delay-100 {
    animation-delay: 100ms;
  }
  
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-300 {
    animation-delay: 300ms;
  }
  
  .text-2xs {
    font-size: var(--font-size-2xs);
  }
  
  .min-h-280 {
    min-height: var(--min-height-280);
  }
  
  .min-h-320 {
    min-height: var(--min-height-320);
  }
}

/* 修复滚动条样式 */
@layer base {
  button {
    outline: none;
  }
  
  body {
    font-smoothing: antialiased;
    -webkit-font-smoothing: antialiased;
  }
  
  /* 修复输入框文字颜色问题 */
  input, textarea, select {
    color: #333;
    background-color: white;
  }
  
  input::placeholder, textarea::placeholder {
    color: #999;
    opacity: 1;
  }
  
  input:disabled, textarea:disabled, select:disabled {
    background-color: #f5f5f5;
    color: #666;
  }
  
  /* 修复单选按钮和复选框样式 */
  input[type="radio"], input[type="checkbox"] {
    background-color: transparent;
    width: auto !important;
    height: auto !important;
    max-width: unset !important;
    margin: 0;
    -webkit-appearance: auto;
    -moz-appearance: auto;
    appearance: auto;
    flex-shrink: 0;
  }
  
  * {
    scrollbar-width: thin;
    scrollbar-color: #d1d5db transparent;
  }
  
  *::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }
  
  *::-webkit-scrollbar-track {
    background: transparent;
  }
  
  *::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 9999px;
  }
  
  *::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
  }
}