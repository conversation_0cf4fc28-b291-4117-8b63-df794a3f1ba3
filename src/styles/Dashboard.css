/* Dashboard 样式 - 清理后版本 */
.dashboard {
    height: 100vh;
    background-color: #f8f9fa;
    padding: 10px 20px;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 全局控制区样式 */
.global-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: white;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex-wrap: nowrap;
    gap: 20px;
    min-height: 80px;
    flex-shrink: 0;
}

.controls-left {
    display: flex;
    align-items: center;
    gap: 20px;
    flex-wrap: nowrap;
    flex: 1;
    min-width: 0;
}

.control-group {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
}

.global-stats {
    display: flex;
    gap: 30px;
    flex-wrap: nowrap;
    flex: 2;
    justify-content: center;
    min-width: 0;
}

.connection-status {
    display: flex;
    gap: 15px;
    flex-wrap: nowrap;
    min-width: 0;
}

.connection-item {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0;
    padding: 6px 12px;
    background: #f8f9fa;
    border-radius: 20px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.connection-item:hover {
    background: #e9ecef;
    border-color: #dee2e6;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.connection-indicator {
    font-size: 12px;
    width: 12px;
    height: 12px;
    display: inline-block;
    border-radius: 50%;
    position: relative;
    flex-shrink: 0;
}

.connection-indicator::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.connection-indicator.connected::before {
    background: #28a745;
    animation: pulse-connected 2s infinite;
}

.connection-indicator.connecting::before {
    background: #ffc107;
    animation: pulse-connecting 1s infinite;
}

.connection-indicator.disconnected::before {
    background: #dc3545;
}

.connection-indicator.unknown::before {
    background: #6c757d;
}

.connection-label {
    font-size: 13px;
    color: #495057;
    font-weight: 500;
    letter-spacing: 0.3px;
}

@keyframes pulse-connected {
    0%,
    100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.1);
    }
}

@keyframes pulse-connecting {
    0%,
    100% {
        opacity: 0.4;
    }
    50% {
        opacity: 1;
    }
}

/* 统计数据样式 */
.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    white-space: nowrap;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #2c3e50;
    text-align: center;
    white-space: nowrap;
}

.stat-value.currency {
    display: inline-block;
    width: 180px;
    padding: 4px 8px;
    background: rgba(0, 123, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(0, 123, 255, 0.1);
    font-family: "SF Mono", "Monaco", "Consolas", "Roboto Mono", monospace;
    font-size: 1.1rem;
    letter-spacing: 0.02em;
}

.stat-value.running {
    color: #28a745;
}

.stat-value.profit {
    color: #28a745;
    border-color: rgba(40, 167, 69, 0.2);
    background: rgba(40, 167, 69, 0.05);
}

.stat-value.loss {
    color: #dc3545;
    border-color: rgba(220, 53, 69, 0.2);
    background: rgba(220, 53, 69, 0.05);
}

/* Dashboard主内容区 */
.dashboard-content {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    flex: 1;
    overflow: hidden;
    min-height: 0;
    display: flex;
    flex-direction: column;
}

/* TaskList容器 - 分离标题和内容区域 */
.task-list-container {
    display: flex;
    flex-direction: column;
    height: 100%;
    min-height: 0;
}

/* 固定的标题区域 */
.task-list-header {
    flex-shrink: 0;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e9ecef;
}

/* 标题行对齐优化 */
.task-list-header .flex {
    align-items: center;
}

.task-list-header h2 {
    line-height: 1.2;
}

/* 可滚动的内容区域 */
.task-list-content {
    flex: 1;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0;
    padding-right: 5px; /* 为滚动条留出空间 */
}

/* 滚动条样式优化 */
.task-list-content::-webkit-scrollbar {
    width: 6px;
}

.task-list-content::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.task-list-content::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.task-list-content::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .global-controls {
        flex-wrap: wrap;
        justify-content: center;
    }

    .controls-left {
        order: 1;
        flex: 1 1 100%;
        justify-content: center;
    }

    .global-stats {
        order: 2;
        flex: 1 1 100%;
        justify-content: center;
        gap: 20px;
    }

    .connection-status {
        order: 3;
        flex: 1 1 100%;
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .global-controls {
        flex-direction: column;
        align-items: stretch;
        text-align: center;
        gap: 15px;
    }

    .controls-left {
        flex-direction: column;
        gap: 15px;
    }

    .global-stats {
        flex-direction: column;
        gap: 15px;
    }

    .connection-status {
        flex-direction: column;
        gap: 8px;
    }

    .connection-item {
        justify-content: center;
        padding: 8px 16px;
    }
}

/* TaskConfigModal样式继续保持 */
.task-config-modal {
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-tabs {
    display: flex;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
    flex-shrink: 0;
}

.tab-button {
    flex: 1;
    padding: 15px 20px;
    border: none;
    background: transparent;
    cursor: pointer;
    font-size: 14px;
    color: #6c757d;
    transition: all 0.3s ease;
    border-bottom: 3px solid transparent;
}

.tab-button:hover {
    background: #e9ecef;
    color: #495057;
}

.tab-button.active {
    color: #007bff;
    border-bottom-color: #007bff;
    background: white;
}

.tab-button.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.tab-button.disabled:hover {
    background: transparent;
    color: #6c757d;
}

.tab-content {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    min-height: 0;
}

/* 其他必要的模态框和表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.required-asterisk {
    color: #dc3545;
    font-weight: bold;
}

.form-group input:not([type="radio"]):not([type="checkbox"]),
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    color: #333;
    background-color: white;
    transition: border-color 0.3s ease;
}

.form-group input:not([type="radio"]):not([type="checkbox"]):focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group input:not([type="radio"]):not([type="checkbox"])::placeholder,
.form-group textarea::placeholder {
    color: #999;
    opacity: 1;
}

.form-group input:not([type="radio"]):not([type="checkbox"]):disabled,
.form-group select:disabled,
.form-group textarea:disabled {
    background-color: #f5f5f5;
    color: #666;
    cursor: not-allowed;
}

.field-help {
    display: block;
    margin-top: 5px;
    color: #6c757d;
    font-size: 12px;
    line-height: 1.4;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    white-space: nowrap;
    flex-shrink: 0;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #007bff;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background: #1e7e34;
}

.btn-warning {
    background: #ffc107;
    color: #212529;
}

.btn-warning:hover:not(:disabled) {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background: #c82333;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background: #5a6268;
}

/* Modal样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(2px);
}

.modal-content {
    background: white;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.modal-header {
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #f8f9fa;
    border-radius: 10px 10px 0 0;
    flex-shrink: 0;
}

.modal-header h2 {
    margin: 0;
    color: #2c3e50;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #dc3545;
}

.modal-body {
    padding: 0;
    flex: 1;
    min-height: 0;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-footer {
    padding: 20px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background: #f8f9fa;
    border-radius: 0 0 10px 10px;
    flex-shrink: 0;
}

/* TaskDetailsModal 专用样式 */
.modal-large {
    max-width: 1200px;
    width: 95%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
}

.task-details-modal .modal-body {
    padding: 0;
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.details-layout {
    display: grid;
    grid-template-columns: 350px 1fr;
    gap: 0;
    flex: 1;
    overflow: hidden;
    min-height: 0; /* 允许flex子项缩小到内容尺寸以下 */
}

.details-sidebar {
    background: #f8f9fa;
    border-right: 1px solid #e9ecef;
    padding: 20px;
    overflow-y: auto;
    overflow-x: hidden;
    min-height: 0; /* 允许缩小到内容尺寸以下，启用滚动 */
    /* 优化滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f8f9fa;
}

.details-sidebar::-webkit-scrollbar {
    width: 6px;
}

.details-sidebar::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.details-sidebar::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
}

.details-sidebar::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8;
}

.details-main {
    padding: 20px;
    overflow: hidden;
    background: white;
    min-height: 0;
    display: flex;
    flex-direction: column;
    /* 优化滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 white;
}

/* 实时数据区域 - 根据内容高度显示 */
.realtime-data-tabs {
    display: flex;
    flex-direction: column;
    flex: 1;
    min-height: 0;
    gap: 15px;
}

/* 数据标签组 */
.data-tab-group {
    display: flex;
    flex-direction: column;
}

/* 移除强制拉伸设置 */
.data-tab-group:first-child {
    flex-shrink: 0;
}

.data-tab-group:last-child {
    flex: 1;
    min-height: 0;
}

.details-main::-webkit-scrollbar {
    width: 6px;
}

.details-main::-webkit-scrollbar-track {
    background: white;
}

.details-main::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 3px;
}

.details-main::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8;
}

.details-section {
    margin-bottom: 25px;
}

.details-section:last-child {
    margin-bottom: 0;
}

.details-section h3 {
    color: #495057;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #e9ecef;
}

.info-grid {
    display: grid;
    gap: 12px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

/* 确保基本信息样式不被覆盖 */
.details-section .info-item label {
    font-size: 13px;
    color: #666 !important;
    font-weight: 600;
    margin: 0;
}

.details-section .info-item span {
    font-size: 13px;
    color: #333 !important;
    font-weight: 400;
}

/* 保持原有的通用样式作为后备 */
.info-item label {
    font-size: 13px;
    color: #666;
    font-weight: 600;
    margin: 0;
}

.info-item span {
    font-size: 13px;
    color: #333;
    font-weight: 400;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.status-running {
    background: #d4edda;
    color: #155724;
}

.status-paused {
    background: #fff3cd;
    color: #856404;
}

.status-error {
    background: #f8d7da;
    color: #721c24;
}

.status-stopped {
    background: #e2e3e5;
    color: #383d41;
}

/* 策略配置和风控配置的统一样式 */
.strategy-config,
.risk-config {
    display: grid;
    gap: 12px;
}

.strategy-config .config-item,
.risk-config .config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.strategy-config .config-item label,
.risk-config .config-item label {
    font-size: 13px;
    color: #666 !important;
    font-weight: 600;
    margin: 0;
}

.strategy-config .config-item span,
.risk-config .config-item span {
    font-size: 13px;
    color: #333 !important;
    font-weight: 400;
}

/* 参数配置区域的简洁样式 */
.strategy-config .config-params,
.risk-config .config-params {
    margin-top: 8px;
    padding-left: 0;
}

.strategy-config .config-params label,
.risk-config .config-params label {
    font-size: 13px;
    color: #666 !important;
    font-weight: 600;
    margin: 0 0 8px 0;
    display: block;
}

/* 参数列表的层次缩进样式 */
.strategy-config .params-list,
.risk-config .params-list {
    padding-left: 12px;
    border-left: 2px solid #e9ecef;
    background: transparent;
    border-radius: 0;
    margin-top: 4px;
}

.strategy-config .param-item,
.risk-config .param-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 3px 0;
    font-size: 12px;
    border-bottom: none;
}

.strategy-config .param-key,
.risk-config .param-key {
    color: #666 !important;
    font-weight: 500;
    min-width: 90px;
    flex-shrink: 0;
    font-size: 12px;
}

.strategy-config .param-value,
.risk-config .param-value {
    color: #333 !important;
    font-weight: 400;
    flex: 1;
    text-align: right;
    font-size: 12px;
}

/* 通用配置项样式（作为后备） */
.details-section .config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px 0;
}

.details-section .config-item label {
    font-size: 13px;
    color: #666 !important;
    font-weight: 600;
    margin: 0;
}

.details-section .config-item span {
    font-size: 13px;
    color: #333 !important;
    font-weight: 400;
}

.realtime-data-tabs {
    display: grid;
    gap: 20px;
}

.data-tab-group {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
}

/* 逐笔成交和经纪商队列并排布局 */
.tick-broker-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    padding: 0;
    background: transparent;
    border: none;
    flex: 1;
    min-height: 0;
}

.tick-feed-tab,
.broker-queue-tab {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 15px;
    display: flex;
    flex-direction: column;
}

.data-tab {
    background: white;
    border-radius: 4px;
    padding: 10px;
}

.logs-section {
    margin-top: 20px;
}

.log-tabs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.log-tabs-header h3 {
    margin: 0;
    color: #495057;
    font-size: 16px;
}

/* 条件块显示样式 */
.condition-block {
    margin-bottom: 12px;
    border-left: 3px solid #e9ecef;
    padding-left: 10px;
}

.condition-header {
    font-weight: 600;
    margin-bottom: 8px !important;
    color: #495057 !important;
}

.condition-params {
    margin-left: 15px;
}

.param-detail {
    margin-bottom: 4px !important;
    color: #6c757d !important;
}

.param-detail .param-key {
    color: #6c757d !important;
    font-size: 13px;
}

.param-detail .param-value {
    color: #495057 !important;
    font-size: 13px;
}

/* 实时数据区域标题 */
.realtime-data-section h3 {
    margin: 0 0 15px 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
}

/* 独立任务日志区域样式 */
.logs-section-standalone {
    margin-top: 30px;
    border-top: 2px solid #e9ecef;
    padding-top: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    padding: 20px;
}

.logs-section-standalone .log-tabs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.logs-section-standalone .log-tabs-header h3 {
    margin: 0;
    color: #495057;
    font-size: 16px;
    font-weight: 600;
}

.logs-section-standalone .log-content {
    background: white;
    border-radius: 4px;
    padding: 15px;
    border: 1px solid #dee2e6;
    min-height: 300px;
    max-height: 400px;
    overflow-y: auto;
}

/* Modal Footer 布局 */
.modal-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

/* Footer 左侧日志状态 */
.footer-log-status {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 6px 12px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.2s;
    max-width: 400px;
    overflow: hidden;
}

.footer-log-status:hover {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.1);
}

.log-status-icon {
    font-size: 14px;
    flex-shrink: 0;
}

.log-status-text {
    font-size: 13px;
    color: #6c757d;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    flex: 1;
}

.log-status-count {
    font-size: 12px;
    color: #adb5bd;
    flex-shrink: 0;
}

/* Footer 右侧操作按钮 */
.footer-actions {
    display: flex;
    gap: 10px;
}

/* 完整日志模态框样式 */
.modal-medium {
    max-width: 800px;
    width: 90vw;
}

.modal-medium .log-content {
    background: white;
    border-radius: 4px;
    padding: 15px;
    border: 1px solid #dee2e6;
    min-height: 400px;
    max-height: 500px;
    overflow-y: auto;
}

.log-tabs {
    display: flex;
    gap: 5px;
}

.log-tabs .tab-button {
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: 1px solid #e9ecef;
    background: white;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: none;
}

.log-tabs .tab-button:hover {
    background: #f8f9fa;
    color: #495057;
}

.log-tabs .tab-button.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.log-content {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    max-height: 300px;
    overflow-y: auto;
}

.profit {
    color: #28a745 !important;
}

.loss {
    color: #dc3545 !important;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .details-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
        max-height: calc(90vh - 140px);
    }

    .details-sidebar {
        border-right: none;
        border-bottom: 1px solid #e9ecef;
        max-height: 200px; /* 限制左侧面板在移动端的高度 */
        overflow-y: auto;
    }

    .details-main {
        overflow-y: auto;
        flex: 1;
    }

    /* 移动端下逐笔成交和经纪商队列堆叠显示 */
    .tick-broker-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }
}

/* 数据展示组件样式 */

/* OrderBook 样式 */
.order-book {
    width: 100%;
    min-width: 450px; /* 恢复为6列布局的最小宽度 */
}

.order-book-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 10px;
}

.order-book-header h4 {
    margin: 0;
    color: #495057;
    font-size: 14px;
    font-weight: 600;
}

.update-time {
    font-size: 12px;
    color: #6c757d;
}

.order-book-content {
    width: 100%;
    overflow-x: auto; /* 允许水平滚动以防内容过宽 */
}

.order-book-table {
    width: 100%;
    min-width: 420px; /* 恢复为6列布局的最小宽度 */
    font-size: 12px;
}

.table-header {
    display: grid;
    grid-template-columns: 1fr 70px 70px 70px 70px 1fr;
    gap: 4px;
    padding: 8px 0;
    background: #f8f9fa;
    border-radius: 4px;
    font-weight: 600;
    color: #495057 !important;
    text-align: center;
    font-size: 11px;
}

.table-body {
    display: grid;
    gap: 1px;
}

.table-row {
    display: grid;
    grid-template-columns: 1fr 70px 70px 70px 70px 1fr;
    gap: 4px;
    padding: 3px 0;
    border-bottom: 1px solid #f0f0f0;
    align-items: center;
    font-size: 11px;
}

.table-row:last-child {
    border-bottom: none;
}

.col-brokers {
    font-size: 9px;
    color: #6c757d !important;
    text-align: center;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.2;
}

.col-volume {
    text-align: center;
    font-weight: 500;
    color: #495057 !important;
    font-size: 11px;
}

.col-price {
    text-align: center;
    font-weight: 600;
    font-size: 11px;
}

.bid-price {
    color: #28a745;
}

.ask-price {
    color: #dc3545;
}

.bid-volume {
    color: #28a745;
}

.ask-volume {
    color: #dc3545;
}

/* TickFeed 样式 */
.tick-feed {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.tick-feed-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 8px;
}

.tick-feed-header h4 {
    margin: 0;
    color: #495057 !important;
    font-size: 13px;
    font-weight: 600;
}

.tick-count {
    font-size: 11px;
    color: #6c757d !important;
}

.tick-feed-content {
    overflow-y: auto;
    min-height: 0;
}

.tick-table {
    width: 100%;
}

.tick-header {
    display: grid;
    grid-template-columns: 50px 60px 50px 40px 1fr;
    gap: 4px;
    padding: 4px 0;
    background: #f8f9fa;
    border-radius: 3px;
    font-size: 10px;
    font-weight: 600;
    color: #495057 !important;
    text-align: center;
}

.tick-body {
    display: grid;
    gap: 1px;
}

.tick-row {
    display: grid;
    grid-template-columns: 50px 60px 50px 40px 1fr;
    gap: 4px;
    padding: 3px 0;
    border-bottom: 1px solid #f0f0f0;
    font-size: 10px;
    align-items: center;
}

.tick-row .col-time {
    color: #6c757d !important;
    font-size: 9px;
    text-align: center;
}

.tick-row .col-price {
    font-weight: 600;
    text-align: right;
    font-size: 11px;
    color: #495057 !important;
}

.tick-row.tick-buy .col-price {
    color: #28a745 !important;
}

.tick-row.tick-sell .col-price {
    color: #dc3545 !important;
}

.tick-row .col-volume {
    text-align: right;
    color: #495057 !important;
    font-size: 10px;
}

.tick-row .col-direction {
    text-align: center;
    font-weight: 500;
    font-size: 10px;
    color: #495057 !important;
}

.tick-row.tick-buy .col-direction {
    color: #28a745 !important;
}

.tick-row.tick-sell .col-direction {
    color: #dc3545 !important;
}

.tick-row .col-broker {
    color: #6c757d !important;
    font-size: 9px;
    text-align: left;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.empty-ticks {
    text-align: center;
    padding: 20px;
    color: #6c757d !important;
    font-style: italic;
    font-size: 12px;
}

/* BrokerQueue 样式 */
.broker-queue {
    width: 100%;
    display: flex;
    flex-direction: column;
}

.broker-queue-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
    margin-bottom: 8px;
}

.broker-queue-header h4 {
    margin: 0;
    color: #495057 !important;
    font-size: 13px;
    font-weight: 600;
}

.queue-count {
    font-size: 11px;
    color: #6c757d !important;
}

.broker-queue-content {
    overflow-y: auto;
    min-height: 0;
}

.empty-queue {
    text-align: center;
    padding: 20px;
    color: #6c757d !important;
    font-style: italic;
    font-size: 12px;
}

.price-level {
    margin-bottom: 10px;
    border-radius: 4px;
    overflow: hidden;
}

.price-level:last-child {
    margin-bottom: 0;
}

.price-level-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 4px 8px;
    font-size: 11px;
    font-weight: 600;
}

.buy-side .price-level-header {
    background: #d4edda;
    color: #155724 !important;
}

.sell-side .price-level-header {
    background: #f8d7da;
    color: #721c24 !important;
}

.price-level-info {
    color: inherit !important;
}

.total-volume {
    color: inherit !important;
    font-size: 10px;
}

.brokers-list {
    background: #f8f9fa;
    padding: 4px;
}

.broker-item {
    display: grid;
    grid-template-columns: 1fr 60px;
    gap: 4px;
    padding: 3px 6px;
    margin-bottom: 2px;
    background: white;
    border-radius: 2px;
    font-size: 10px;
    align-items: center;
}

.broker-item:last-child {
    margin-bottom: 0;
}

.broker-info {
    display: flex;
    flex-direction: column;
    gap: 1px;
}

.broker-name {
    color: #495057 !important;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 10px;
}

.broker-id {
    color: #6c757d !important;
    font-size: 8px;
}

.broker-stats {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 1px;
}

.broker-stats .volume {
    color: #495057 !important;
    font-weight: 600;
    font-size: 9px;
}

.broker-stats .orders {
    color: #6c757d !important;
    font-size: 8px;
}

/* LogSystem 样式 */
.log-system {
    width: 100%;
}

.log-system-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.log-system-header h4 {
    margin: 0;
    color: #495057 !important;
    font-size: 16px;
    font-weight: 600;
}

.log-count {
    font-size: 13px;
    color: #6c757d !important;
    background: white;
    padding: 4px 8px;
    border-radius: 12px;
    border: 1px solid #dee2e6;
}

.log-system-content {
    max-height: 400px;
    overflow-y: auto;
}

.log-list {
    display: grid;
    gap: 8px;
    padding: 15px;
}

.log-entry {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 12px;
    transition: all 0.2s ease;
}

.log-entry:hover {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border-color: #007bff;
}

.log-entry.log-info {
    border-left: 3px solid #17a2b8;
}

.log-entry.log-warning {
    border-left: 3px solid #ffc107;
    background: #fffcf5;
}

.log-entry.log-error {
    border-left: 3px solid #dc3545;
    background: #fef6f6;
}

.log-entry.log-debug {
    border-left: 3px solid #6c757d;
    background: #f8f9fa;
}

.log-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    flex-wrap: wrap;
}

.log-level {
    font-size: 14px;
    display: inline-block;
    min-width: 20px;
}

.log-timestamp {
    font-size: 11px;
    color: #6c757d !important;
    font-family: "SF Mono", "Monaco", "Consolas", "Roboto Mono", monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 3px;
    white-space: nowrap;
}

.log-category {
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 8px;
    text-align: center;
    white-space: nowrap;
    color: #495057 !important;
    background: #e9ecef;
    border: 1px solid #dee2e6;
}

.log-task-id {
    font-size: 10px;
    color: #868e96 !important;
    background: #f1f3f4;
    padding: 2px 6px;
    border-radius: 6px;
    font-family: "SF Mono", "Monaco", "Consolas", "Roboto Mono", monospace;
}

.log-message {
    color: #495057 !important;
    line-height: 1.5;
    word-break: break-word;
    font-size: 13px;
    margin-bottom: 8px;
}

.log-details {
    margin-top: 8px;
}

.log-details details {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    padding: 8px;
}

.log-details summary {
    font-size: 11px;
    color: #007bff !important;
    cursor: pointer;
    font-weight: 500;
    user-select: none;
}

.log-details summary:hover {
    color: #0056b3 !important;
}

.log-details pre {
    margin: 8px 0 0 0;
    padding: 8px;
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 3px;
    font-size: 11px;
    color: #495057 !important;
    overflow-x: auto;
    white-space: pre-wrap;
    word-break: break-word;
}

.empty-logs {
    text-align: center;
    padding: 40px 20px;
    color: #6c757d !important;
    font-style: italic;
    font-size: 14px;
}

.empty-logs p {
    margin: 0;
    color: #6c757d !important;
}

/* 兼容旧的log-item样式 */
.log-item {
    display: grid;
    grid-template-columns: 80px auto 1fr;
    gap: 8px;
    padding: 8px;
    border-radius: 4px;
    align-items: flex-start;
    font-size: 12px;
    border-left: 3px solid transparent;
}

.log-item.info {
    background: #f8f9fa;
    border-left-color: #17a2b8;
}

.log-item.warning {
    background: #fff3cd;
    border-left-color: #ffc107;
}

.log-item.error {
    background: #f8d7da;
    border-left-color: #dc3545;
}

.log-time {
    color: #6c757d !important;
    font-size: 11px;
    white-space: nowrap;
}

.log-category.strategy {
    background: #e7f3ff;
    color: #0056b3 !important;
}

.log-category.trading {
    background: #e8f5e8;
    color: #155724 !important;
}

.log-category.system {
    background: #fff3cd;
    color: #856404 !important;
}

/* 响应式任务网格 - 修复滚动问题 */
.responsive-task-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 0;
    /* 确保网格容器本身不限制高度 */
}
