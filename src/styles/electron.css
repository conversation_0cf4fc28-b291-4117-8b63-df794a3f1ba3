/* Electron 窗口拖拽样式 */

/* 默认情况下，整个窗口顶部区域可拖拽 */
.electron-drag {
    -webkit-app-region: drag;
}

/* 按钮等交互元素不可拖拽 */
.electron-no-drag,
button,
input,
select,
textarea,
a,
[role="button"],
[tabindex] {
    -webkit-app-region: no-drag;
}

/* 标题栏区域样式 */
.title-bar {
    -webkit-app-region: drag;
    height: 32px;
    background: transparent;
    display: flex;
    align-items: center;
    padding: 0 16px;
    user-select: none;
}

/* macOS 特殊处理 */
@media (prefers-color-scheme: dark) {
    .title-bar {
        background: rgba(0, 0, 0, 0.05);
    }
}

/* 确保主要内容区域不可拖拽 */
.main-content {
    -webkit-app-region: no-drag;
}

/* 顶部控制栏可拖拽，但按钮除外 */
.top-controls {
    -webkit-app-region: drag;
    padding: 8px 16px;
}

.top-controls button,
.top-controls select,
.top-controls input {
    -webkit-app-region: no-drag;
}