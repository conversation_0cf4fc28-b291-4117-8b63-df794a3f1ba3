// 设计系统配置 - 基于项目"弹出框风格"
export const designSystem = {
  // 色彩系统
  colors: {
    // 主色调 - 基于项目现有色彩
    primary: {
      50: '#f0f9ff',
      100: '#e0f2fe',
      500: '#0ea5e9',
      600: '#0284c7',
      700: '#0369a1',
    },
    
    // 灰色系统 - 与现有#495057, #666等保持一致
    gray: {
      50: '#f8f9fa',    // 主容器背景
      100: '#e9ecef',   // 边框色
      200: '#dee2e6',   // 次级边框
      300: '#ced4da',
      400: '#adb5bd',
      500: '#6c757d',   // 次要文字
      600: '#495057',   // 标题文字
      700: '#343a40',
      800: '#212529',   // 主要文字
      900: '#1a1a1a',
    },
    
    // 状态色彩
    success: {
      50: '#d4edda',
      500: '#28a745',
      700: '#155724',
    },
    warning: {
      50: '#fff3cd',
      500: '#ffc107',
      700: '#856404',
    },
    error: {
      50: '#f8d7da',
      500: '#dc3545',
      700: '#721c24',
    },
    liquidated: {
      50: 'rgba(0, 123, 255, 0.1)',
      500: '#007bff',
      700: '#004085',
    }
  },
  
  // 间距系统
  spacing: {
    // 基于现有的15px, 20px等间距
    xs: '5px',
    sm: '10px',
    md: '15px',    // 表单字段标准间距
    lg: '20px',    // 容器内边距
    xl: '30px',    // 区域间隔
    '2xl': '40px',
  },
  
  // 字体系统
  typography: {
    // 字体大小
    sizes: {
      xs: '0.75rem',   // 12px - 帮助文字
      sm: '0.8125rem', // 13px - 描述文字
      base: '0.875rem', // 14px - 正文
      lg: '1rem',      // 16px - 标题
      xl: '1.125rem',  // 18px - 大标题
    },
    
    // 字重
    weights: {
      normal: '400',
      medium: '500',   // 标签和重要文字
      semibold: '600', // 标题
      bold: '700',
    },
    
    // 行高
    lineHeights: {
      tight: '1.2',
      normal: '1.4',   // 描述文字标准行高
      relaxed: '1.6',
    }
  },
  
  // 圆角系统
  borderRadius: {
    sm: '3px',     // 按钮
    md: '4px',     // 卡片
    lg: '6px',     // 主容器
    xl: '8px',
    '2xl': '12px',
  },
  
  // 阴影系统
  shadows: {
    sm: '0 1px 3px rgba(0,0,0,0.1)',
    md: '0 2px 4px rgba(0, 0, 0, 0.04)',      // 卡片基础阴影
    lg: '0 4px 12px rgba(0, 0, 0, 0.08)',     // 悬停阴影
    xl: '0 8px 25px rgba(0, 0, 0, 0.12)',     // 激活阴影
  },
  
  // 动画配置
  animations: {
    // 持续时间
    durations: {
      fast: '150ms',
      normal: '200ms',
      slow: '300ms',
    },
    
    // 缓动函数
    easings: {
      linear: 'linear',
      out: 'cubic-bezier(0.4, 0, 0.2, 1)',
      in: 'cubic-bezier(0.4, 0, 1, 1)',
      inOut: 'cubic-bezier(0.4, 0, 0.2, 1)',
    }
  },
  
  // 响应式断点（桌面端专用）
  breakpoints: {
    desktop: '1024px',  // 标准桌面
    wide: '1440px',     // 宽屏
    ultra: '1920px',    // 超宽屏
  }
} as const;

export type ColorKey = keyof typeof designSystem.colors;
export type SpacingKey = keyof typeof designSystem.spacing;