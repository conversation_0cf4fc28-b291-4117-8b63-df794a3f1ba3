// 主题管理系统
import { designSystem } from './designSystem';

// 主题配置接口
export interface ThemeConfig {
  mode: 'light' | 'dark';
  density: 'compact' | 'standard' | 'comfortable';
  animations: boolean;
}

// 默认主题配置
export const defaultTheme: ThemeConfig = {
  mode: 'light',
  density: 'standard',
  animations: true,
};

// 密度变体配置
export const densityVariants = {
  compact: {
    cardPadding: 'p-3',
    cardMinHeight: 'min-h-[240px]',
    gap: 'gap-3',
    fontSize: 'text-sm',
    buttonSize: 'px-2 py-1 text-xs',
  },
  standard: {
    cardPadding: 'p-4',
    cardMinHeight: 'min-h-[280px]',
    gap: 'gap-4',
    fontSize: 'text-base',
    buttonSize: 'px-3 py-1.5 text-sm',
  },
  comfortable: {
    cardPadding: 'p-5',
    cardMinHeight: 'min-h-[320px]',
    gap: 'gap-5',
    fontSize: 'text-base',
    buttonSize: 'px-4 py-2 text-sm',
  },
};

// 动画配置
export const animationVariants = {
  enabled: {
    card: 'transition-all duration-200 ease-out hover:shadow-card-hover hover:-translate-y-0.5',
    button: 'transition-all duration-150 ease-out hover:scale-105',
    status: 'transition-colors duration-200 ease-out',
  },
  disabled: {
    card: '',
    button: '',
    status: '',
  },
};

// 获取主题样式
export const getThemeClasses = (config: Partial<ThemeConfig> = {}) => {
  const theme = { ...defaultTheme, ...config };
  
  const density = densityVariants[theme.density];
  const animations = animationVariants[theme.animations ? 'enabled' : 'disabled'];
  
  return {
    // 卡片样式
    card: [
      'bg-white',
      'border border-gray-200 rounded-lg',
      density.cardPadding,
      density.cardMinHeight,
      'flex flex-col',
      animations.card
    ].join(' '),
    
    // 网格布局
    grid: [
      'grid',
      density.gap,
      'grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4'
    ].join(' '),
    
    // 按钮样式
    button: [
      density.buttonSize,
      'font-medium rounded',
      'focus:outline-none focus:ring-2 focus:ring-offset-1',
      'disabled:opacity-50 disabled:cursor-not-allowed',
      animations.button
    ].join(' '),
    
    // 文字样式
    title: `${density.fontSize} font-semibold text-gray-800`,
    subtitle: 'text-sm text-gray-600',
    caption: 'text-xs text-gray-500',
  };
};

// CSS变量生成器（用于动态主题切换）
export const generateCSSVariables = (config: ThemeConfig) => {
  const variables: Record<string, string> = {};
  
  // 根据配置生成CSS变量
  if (config.mode === 'light') {
    variables['--color-background'] = designSystem.colors.gray[50];
    variables['--color-surface'] = '#ffffff';
    variables['--color-text-primary'] = designSystem.colors.gray[800];
    variables['--color-text-secondary'] = designSystem.colors.gray[500];
  }
  
  // 密度相关变量
  const density = densityVariants[config.density];
  variables['--card-padding'] = density.cardPadding.split('-')[1];
  variables['--card-gap'] = density.gap.split('-')[1];
  
  return variables;
};

// 主题上下文Hook（为将来的主题切换准备）
export const useTheme = () => {
  // 暂时返回默认主题，后续可扩展为React Context
  return {
    theme: defaultTheme,
    classes: getThemeClasses(defaultTheme),
    setTheme: (theme: Partial<ThemeConfig>) => {
      // 实现主题切换逻辑
      console.log('Theme change requested:', theme);
    }
  };
};

// 导出常用的预设主题
export const presetThemes = {
  default: defaultTheme,
  compact: { ...defaultTheme, density: 'compact' as const },
  comfortable: { ...defaultTheme, density: 'comfortable' as const },
} as const;