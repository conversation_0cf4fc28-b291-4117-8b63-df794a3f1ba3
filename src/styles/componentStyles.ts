// 任务卡片样式库 - 基于CSS-in-JS的组件样式
import React from 'react';

// ==================== 类型定义 ====================

export type CardSize = 'standard' | 'compact' | 'wide';
export type CardElevation = 'none' | 'low' | 'medium' | 'high';
export type TaskStatus = 'running' | 'paused' | 'stopped' | 'error' | 'liquidated';
export type ButtonVariant = 'primary' | 'success' | 'warning' | 'danger' | 'secondary' | 'info';
export type FilterStatus = 'all' | TaskStatus;
export type TextVariant = 'title' | 'subtitle' | 'caption' | 'body' | 'profit' | 'loss' | 'neutral';
export type LayoutVariant = 'taskGrid' | 'cardHeader' | 'cardBody' | 'cardFooter' | 'infoRow' | 
  'buttonGroup' | 'container' | 'pageHeader' | 'pageContent' | 'filterContainer';
export type AnimationVariant = 'fadeIn' | 'slideUp';
export type AnimationDelay = 'short' | 'medium' | 'long';
export type GridColumns = 1 | 2 | 3 | 4 | 5 | 6;
export type StyleClasses = string;

// ==================== 接口定义 ====================

export interface CardVariantConfig {
  size?: CardSize;
  elevation?: CardElevation;
  status?: TaskStatus;
  animated?: boolean;
}

export interface ButtonConfig {
  variant?: ButtonVariant;
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
}

export interface AnimationConfig {
  variant: AnimationVariant;
  delay?: AnimationDelay;
  duration?: number;
}

export interface GridConfig {
  responsive?: boolean;
  columns?: GridColumns;
  gap?: 'sm' | 'md' | 'lg';
}

// ==================== 样式常量 ====================

const colors = {
  primary: {
    100: '#e0f2fe',
    300: '#7dd3fc', 
    500: '#0ea5e9',
    600: '#0284c7',
    700: '#0369a1',
  },
  gray: {
    50: '#f8f9fa',
    100: '#e9ecef',
    200: '#dee2e6',
    300: '#ced4da',
    400: '#adb5bd',
    500: '#6c757d',
    600: '#495057',
    700: '#343a40',
    800: '#212529',
  },
  success: {
    50: '#d4edda',
    200: '#c3e6cb',
    500: '#28a745',
    700: '#155724',
  },
  warning: {
    50: '#fff3cd',
    200: '#ffeaa7',
    500: '#ffc107',
    700: '#856404',
    900: '#212529',
  },
  error: {
    50: '#f8d7da',
    500: '#dc3545',
    700: '#721c24',
  },
  liquidated: {
    50: 'rgba(0, 123, 255, 0.1)',
    500: '#007bff',
    700: '#004085',
  }
};

const shadows = {
  card: '0 2px 4px rgba(0, 0, 0, 0.04)',
  cardHover: '0 4px 12px rgba(0, 0, 0, 0.08)',
  cardActive: '0 8px 25px rgba(0, 0, 0, 0.12)',
};

// ==================== 样式生成函数 ====================

// 生成卡片样式
export const getCardClasses = (config: CardVariantConfig = {}): StyleClasses => {
  const {
    size = 'standard',
    elevation = 'medium',
    status,
    animated = true
  } = config;

  const baseClasses = [
    'bg-white',
    'border',
    'border-gray-200',
    'rounded-lg',
    'overflow-visible',
    'relative',
    'flex',
    'flex-col',
    'transition-all',
    'duration-200'
  ];

  // 尺寸变体
  const sizeClasses: Record<CardSize, string[]> = {
    compact: ['p-3', 'min-h-[240px]'],
    standard: ['p-4', 'min-h-[280px]'],
    wide: ['p-5', 'min-h-[320px]']
  };

  // 阴影变体
  const elevationClasses: Record<CardElevation, string[]> = {
    none: [],
    low: ['shadow-sm'],
    medium: ['shadow-md'],
    high: ['shadow-lg']
  };

  // 动画效果
  const animationClasses = animated ? [
    'hover:shadow-xl',
    'hover:-translate-y-1',
    'hover:scale-[1.02]'
  ] : [];

  // 状态边框增强
  const statusClasses: Record<TaskStatus, string[]> = {
    running: ['border-l-4', 'border-l-success-500'],
    paused: ['border-l-4', 'border-l-warning-500'],
    stopped: ['border-l-4', 'border-l-gray-400'],
    error: ['border-l-4', 'border-l-error-500'],
    liquidated: ['border-l-4', 'border-l-liquidated-500']
  };

  return [
    ...baseClasses,
    ...sizeClasses[size],
    ...elevationClasses[elevation],
    ...animationClasses,
    ...(status ? statusClasses[status] : [])
  ].join(' ');
};

// 增强的状态指示器样式（支持动画和更多配置）
export const getStatusIndicatorClasses = (status: TaskStatus, config: {
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'badge' | 'pill' | 'dot';
} = {}): StyleClasses => {
  const { animated = true, size = 'md', variant = 'badge' } = config;
  
  const baseClasses = [
    'inline-flex items-center gap-1 rounded font-medium transition-all duration-300',
    'focus:outline-none focus:ring-2 focus:ring-offset-1'
  ];
  
  // 尺寸配置
  const sizeClasses: Record<'sm' | 'md' | 'lg', string[]> = {
    sm: ['px-1.5 py-0.5 text-2xs'],
    md: ['px-2 py-0.5 text-xs'],
    lg: ['px-3 py-1 text-sm']
  };
  
  // 变体配置
  const variantClasses: Record<'badge' | 'pill' | 'dot', string[]> = {
    badge: ['rounded'],
    pill: ['rounded-full'],
    dot: ['rounded-full', 'w-3 h-3', 'p-0']
  };
  
  // 状态样式映射（增强版）
  const statusVariants: Record<TaskStatus, {
    classes: string[];
    pulseColor?: string;
    icon?: string;
  }> = {
    running: {
      classes: ['bg-green-100 text-green-700 border border-green-200'],
      pulseColor: '#10b981',
      icon: '▶️'
    },
    paused: {
      classes: ['bg-yellow-100 text-yellow-700 border border-yellow-200'],
      pulseColor: '#f59e0b',
      icon: '⏸️'
    },
    stopped: {
      classes: ['bg-gray-100 text-gray-600 border border-gray-200'],
      icon: '⏹️'
    },
    error: {
      classes: ['bg-red-100 text-red-700 border border-red-200'],
      pulseColor: '#ef4444',
      icon: '❌'
    },
    liquidated: {
      classes: ['bg-blue-100 text-blue-700 border border-blue-200'],
      icon: '💧'
    }
  };
  
  const statusConfig = statusVariants[status] || statusVariants.stopped;
  
  // 动画类
  const animationClasses = [];
  if (animated && statusConfig.pulseColor) {
    if (status === 'running') {
      animationClasses.push('animate-pulse');
    } else if (status === 'error') {
      animationClasses.push('animate-bounce');
    }
  }
  
  return [
    ...baseClasses,
    ...sizeClasses[size],
    ...variantClasses[variant],
    ...statusConfig.classes,
    ...animationClasses
  ].join(' ');
};

// 状态指示器组件样式生成器（内联样式版本）
export const getStatusIndicatorStyle = (status: TaskStatus, config: {
  animated?: boolean;
  size?: 'sm' | 'md' | 'lg';
} = {}) => {
  const { animated = true, size = 'md' } = config;
  
  // 基础样式
  const baseStyle: React.CSSProperties = {
    display: 'inline-flex',
    alignItems: 'center',
    gap: '4px',
    borderRadius: '4px',
    fontWeight: '500',
    transition: 'all 0.3s ease',
    border: '1px solid'
  };
  
  // 尺寸样式
  const sizeStyles: Record<'sm' | 'md' | 'lg', React.CSSProperties> = {
    sm: { padding: '2px 6px', fontSize: '10px' },
    md: { padding: '4px 8px', fontSize: '12px' },
    lg: { padding: '6px 12px', fontSize: '14px' }
  };
  
  // 状态样式映射
  const statusStyles: Record<TaskStatus, React.CSSProperties & { animation?: string }> = {
    running: {
      backgroundColor: '#d4f4dd',
      color: '#166534',
      borderColor: '#16a34a',
      animation: animated ? 'statusPulse 2s infinite' : 'none'
    },
    paused: {
      backgroundColor: '#fef3c7',
      color: '#92400e',
      borderColor: '#f59e0b'
    },
    stopped: {
      backgroundColor: '#f3f4f6',
      color: '#4b5563',
      borderColor: '#9ca3af'
    },
    error: {
      backgroundColor: '#fecaca',
      color: '#991b1b',
      borderColor: '#ef4444',
      animation: animated ? 'errorBlink 1s infinite alternate' : 'none'
    },
    liquidated: {
      backgroundColor: '#dbeafe',
      color: '#1e40af',
      borderColor: '#3b82f6'
    }
  };
  
  return {
    ...baseStyle,
    ...sizeStyles[size],
    ...statusStyles[status]
  };
};

// 状态图标映射
export const getStatusIcon = (status: TaskStatus): string => {
  const iconMap: Record<TaskStatus, string> = {
    running: '▶️',
    paused: '⏸️',
    stopped: '⏹️',
    error: '❌',
    liquidated: '💧'
  };
  
  return iconMap[status] || '⏸️';
};

// 状态文本映射
export const getStatusText = (status: TaskStatus): string => {
  const textMap: Record<TaskStatus, string> = {
    running: '运行中',
    paused: '已暂停',
    stopped: '已停止',
    error: '错误',
    liquidated: '已清仓'
  };
  
  return textMap[status] || status;
};

// 状态颜色映射（用于边框等）
export const getStatusColor = (status: TaskStatus): string => {
  const colorMap: Record<TaskStatus, string> = {
    running: '#16a34a',
    paused: '#f59e0b',
    stopped: '#6b7280',
    error: '#ef4444',
    liquidated: '#3b82f6'
  };
  
  return colorMap[status] || '#6b7280';
};
export const getButtonClasses = (config: ButtonConfig | ButtonVariant = 'primary'): StyleClasses => {
  // 兼容旧API：如果传入字符串，转换为配置对象
  const buttonConfig: ButtonConfig = typeof config === 'string' 
    ? { variant: config }
    : config;
    
  const { 
    variant = 'primary', 
    size = 'md', 
    disabled = false, 
    loading = false 
  } = buttonConfig;
  
  const baseClasses = [
    'font-medium rounded transition-all duration-200 ease-out',
    'focus:outline-none focus:ring-2 focus:ring-offset-1',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    'inline-flex items-center justify-center gap-2'
  ];
  
  // 尺寸变体
  const sizeClasses: Record<NonNullable<ButtonConfig['size']>, string[]> = {
    sm: ['px-2 py-1 text-xs'],
    md: ['px-3 py-1.5 text-sm'],
    lg: ['px-4 py-2 text-base']
  };
  
  // 样式变体
  const variants: Record<ButtonVariant, string[]> = {
    primary: ['bg-primary-500 text-white hover:bg-primary-600 focus:ring-primary-300'],
    success: ['bg-success-500 text-white hover:bg-green-600 focus:ring-green-300'],
    warning: ['bg-warning-500 text-warning-900 hover:bg-yellow-500 focus:ring-yellow-300'],
    danger: ['bg-error-500 text-white hover:bg-red-600 focus:ring-red-300'],
    secondary: ['bg-gray-100 text-gray-700 hover:bg-gray-200 focus:ring-gray-300'],
    info: ['bg-blue-500 text-white hover:bg-blue-600 focus:ring-blue-300']
  };
  
  // 状态修饰符
  const stateClasses = [];
  if (disabled) stateClasses.push('opacity-50 cursor-not-allowed');
  if (loading) stateClasses.push('pointer-events-none');

  return [
    ...baseClasses,
    ...sizeClasses[size],
    ...variants[variant],
    ...stateClasses
  ].join(' ');
};

// 文字样式工具类
export const textClasses: Record<TextVariant, string> = {
  title: 'text-gray-800 font-semibold',
  subtitle: 'text-gray-600 text-sm',
  caption: 'text-gray-500 text-xs',
  body: 'text-gray-700 text-sm',
  profit: 'text-success-500 font-semibold',
  loss: 'text-error-500 font-semibold',
  neutral: 'text-gray-600'
};

// 文字样式获取函数
export const getTextClasses = (variant: TextVariant): StyleClasses => {
  return textClasses[variant] || textClasses.body;
};

// 布局工具类（增强版响应式支持）
export const layoutClasses: Record<LayoutVariant, string> = {
  // 响应式任务网格
  taskGrid: 'responsive-task-grid',
  
  // Flex布局（增强版）
  cardHeader: 'flex justify-between items-start mb-4',
  cardBody: 'flex-1 flex flex-col gap-3',
  cardFooter: 'flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 pt-3 border-t border-gray-100 mt-auto',
  
  // 信息行
  infoRow: 'flex justify-between items-center text-sm',
  
  // 按钮组（响应式）
  buttonGroup: 'flex flex-wrap gap-2',
  
  // 容器布局（响应式）
  container: 'max-w-7xl mx-auto px-4 sm:px-6 lg:px-8',
  
  // 页面布局（响应式）
  pageHeader: 'flex flex-col gap-4 mb-6 sm:flex-row sm:items-center sm:justify-between',
  pageContent: 'space-y-6',
  
  // 筛选器布局（响应式）
  filterContainer: 'flex flex-wrap gap-2 justify-start sm:justify-end',
};

// 布局样式获取函数
export const getLayoutClasses = (variant: LayoutVariant): StyleClasses => {
  return layoutClasses[variant] || '';
};

// 动画类名映射
const animationVariants: Record<AnimationVariant, string> = {
  fadeIn: 'animate-fade-in',
  slideUp: 'animate-slide-up'
};

const animationDelays: Record<AnimationDelay, string> = {
  short: 'animation-delay-100',
  medium: 'animation-delay-200',
  long: 'animation-delay-300'
};

// 动画样式获取函数
export const getAnimationClasses = (config: AnimationConfig | AnimationVariant): StyleClasses => {
  if (typeof config === 'string') {
    return animationVariants[config] || '';
  }
  
  const { variant, delay } = config;
  const classes = [animationVariants[variant]];
  
  if (delay) {
    classes.push(animationDelays[delay]);
  }
  
  return classes.filter(Boolean).join(' ');
};

// 向后兼容的动画类名对象
export const animationClasses = {
  fadeIn: 'animate-fade-in',
  slideUp: 'animate-slide-up',
  
  // 延迟动画
  delayShort: 'animation-delay-100',
  delayMedium: 'animation-delay-200',
  delayLong: 'animation-delay-300'
};

// 响应式网格配置
export const getGridClasses = (config: GridConfig | boolean = true): StyleClasses => {
  // 向后兼容：如果传入boolean，转换为配置对象
  const gridConfig: GridConfig = typeof config === 'boolean' 
    ? { responsive: config }
    : config;
    
  const { responsive = true, columns = 3, gap = 'md' } = gridConfig;
  
  // 间距映射
  const gapClasses = {
    sm: 'gap-2 md:gap-3',
    md: 'gap-4 md:gap-5', 
    lg: 'gap-6 md:gap-8'
  };
  
  const baseGrid = `grid ${gapClasses[gap]}`;
  
  if (responsive) {
    // 增强的响应式：支持6个断点
    return `${baseGrid} grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4`;
  }
  
  return `${baseGrid} grid-cols-${columns}`;
};

// 响应式布局工具类（增强版）
export const responsiveLayoutClasses = {
  // 容器布局
  container: {
    base: 'w-full mx-auto px-4',
    sm: 'sm:px-6',
    lg: 'lg:px-8',
    xl: 'xl:max-w-7xl'
  },
  
  // 任务网格布局
  taskGrid: {
    base: 'grid gap-4',
    sm: 'sm:gap-4',
    md: 'md:grid-cols-2 md:gap-5',
    lg: 'lg:grid-cols-2',
    xl: 'xl:grid-cols-3',
    '2xl': '2xl:grid-cols-4'
  },
  
  // 页面头部布局
  pageHeader: {
    base: 'flex flex-col gap-4 mb-6',
    sm: 'sm:flex-row sm:items-center sm:justify-between'
  },
  
  // 筛选器布局
  filterContainer: {
    base: 'flex flex-wrap gap-2',
    sm: 'sm:justify-start',
    md: 'md:justify-end'
  },
  
  // 卡片布局
  cardLayout: {
    base: 'min-h-[280px] p-4',
    sm: 'sm:min-h-[300px]',
    md: 'md:min-h-[320px]',
    lg: 'lg:p-5'
  }
};

// 响应式布局类生成器
export const getResponsiveClasses = (layout: keyof typeof responsiveLayoutClasses): string => {
  const layoutConfig = responsiveLayoutClasses[layout];
  return Object.values(layoutConfig).join(' ');
};

// 断点工具类
export const breakpoints = {
  xs: '0px',      // 手机竖屏 (0px+)
  sm: '576px',    // 手机横屏 (576px+) 
  md: '768px',    // 平板 (768px+)
  lg: '992px',    // 笔记本 (992px+)
  xl: '1200px',   // 桌面 (1200px+)
  '2xl': '1400px' // 大屏桌面 (1400px+)
};

// 响应式辅助函数
export const getBreakpointValue = (breakpoint: keyof typeof breakpoints): string => {
  return breakpoints[breakpoint];
};

// 媒体查询生成器
export const mediaQuery = (breakpoint: keyof typeof breakpoints): string => {
  return `@media (min-width: ${breakpoints[breakpoint]})`;
};

// 响应式网格系统（CSS-in-JS版本）
export const getResponsiveGridStyle = (config: {
  columns?: { xs?: number; sm?: number; md?: number; lg?: number; xl?: number; '2xl'?: number };
  gap?: { xs?: string; sm?: string; md?: string; lg?: string; xl?: string; '2xl'?: string };
  padding?: { xs?: string; sm?: string; md?: string; lg?: string; xl?: string; '2xl'?: string };
} = {}): React.CSSProperties => {
  const {
    columns = { xs: 1, sm: 1, md: 2, lg: 2, xl: 3, '2xl': 4 },
    gap = { xs: '12px', sm: '16px', md: '16px', lg: '20px', xl: '20px', '2xl': '24px' },
    padding = { xs: '12px', sm: '16px', md: '16px', lg: '20px', xl: '20px', '2xl': '24px' }
  } = config;
  
  return {
    display: 'grid',
    gridTemplateColumns: `repeat(${columns.xs || 1}, 1fr)`,
    gap: gap.xs || '12px',
    padding: padding.xs || '12px',
    
    // 使用CSS变量来支持响应式（需要配合CSS媒体查询）
    '--grid-columns-sm': columns.sm || 1,
    '--grid-columns-md': columns.md || 2,
    '--grid-columns-lg': columns.lg || 2,
    '--grid-columns-xl': columns.xl || 3,
    '--grid-columns-2xl': columns['2xl'] || 4,
    
    '--grid-gap-sm': gap.sm || '16px',
    '--grid-gap-md': gap.md || '16px', 
    '--grid-gap-lg': gap.lg || '20px',
    '--grid-gap-xl': gap.xl || '20px',
    '--grid-gap-2xl': gap['2xl'] || '24px',
    
    '--grid-padding-sm': padding.sm || '16px',
    '--grid-padding-md': padding.md || '16px',
    '--grid-padding-lg': padding.lg || '20px',
    '--grid-padding-xl': padding.xl || '20px',
    '--grid-padding-2xl': padding['2xl'] || '24px'
  } as React.CSSProperties;
};

// 添加响应式网格的CSS规则
const responsiveStyleSheet = document.createElement('style');
responsiveStyleSheet.textContent = `
  .responsive-task-grid {
    display: grid;
    gap: var(--grid-gap, 12px);
    padding: var(--grid-padding, 12px);
    grid-template-columns: repeat(var(--grid-columns, 1), 1fr);
  }
  
  @media (min-width: 576px) {
    .responsive-task-grid {
      --grid-columns: var(--grid-columns-sm, 1);
      --grid-gap: var(--grid-gap-sm, 16px);
      --grid-padding: var(--grid-padding-sm, 16px);
    }
  }
  
  @media (min-width: 768px) {
    .responsive-task-grid {
      --grid-columns: var(--grid-columns-md, 2);
      --grid-gap: var(--grid-gap-md, 16px);
      --grid-padding: var(--grid-padding-md, 16px);
    }
  }
  
  @media (min-width: 992px) {
    .responsive-task-grid {
      --grid-columns: var(--grid-columns-lg, 2);
      --grid-gap: var(--grid-gap-lg, 20px);
      --grid-padding: var(--grid-padding-lg, 20px);
    }
  }
  
  @media (min-width: 1200px) {
    .responsive-task-grid {
      --grid-columns: var(--grid-columns-xl, 3);
      --grid-gap: var(--grid-gap-xl, 20px);
      --grid-padding: var(--grid-padding-xl, 20px);
    }
  }
  
  @media (min-width: 1400px) {
    .responsive-task-grid {
      --grid-columns: var(--grid-columns-2xl, 4);
      --grid-gap: var(--grid-gap-2xl, 24px);
      --grid-padding: var(--grid-padding-2xl, 24px);
    }
  }
`;

if (!document.head.querySelector('style[data-responsive-grid-styles]')) {
  responsiveStyleSheet.setAttribute('data-responsive-grid-styles', 'true');
  document.head.appendChild(responsiveStyleSheet);
}

// 类型安全的样式组合函数
export const combineClasses = (...classes: (string | undefined | null | false)[]): StyleClasses => {
  return classes.filter(Boolean).join(' ');
};

// 条件样式应用函数
export const conditionalClasses = (condition: boolean, trueClasses: string, falseClasses = ''): StyleClasses => {
  return condition ? trueClasses : falseClasses;
};

// 类型验证和错误处理函数
export const validateTaskStatus = (status: unknown): TaskStatus => {
  const validStatuses: TaskStatus[] = ['running', 'paused', 'stopped', 'error', 'liquidated'];
  if (typeof status === 'string' && validStatuses.includes(status as TaskStatus)) {
    return status as TaskStatus;
  }
  console.warn(`Invalid task status: ${status}, falling back to 'stopped'`);
  return 'stopped';
};

export const validateButtonVariant = (variant: unknown): ButtonVariant => {
  const validVariants: ButtonVariant[] = ['primary', 'success', 'warning', 'danger', 'secondary', 'info'];
  if (typeof variant === 'string' && validVariants.includes(variant as ButtonVariant)) {
    return variant as ButtonVariant;
  }
  console.warn(`Invalid button variant: ${variant}, falling back to 'primary'`);
  return 'primary';
};

// 安全的样式生成器包装函数
export const safeGetCardClasses = (config: Partial<CardVariantConfig> = {}): StyleClasses => {
  try {
    const safeConfig: CardVariantConfig = {
      ...config,
      status: config.status ? validateTaskStatus(config.status) : undefined
    };
    return getCardClasses(safeConfig);
  } catch (error) {
    console.error('Error generating card classes:', error);
    return 'bg-white border border-gray-200 rounded-lg p-4'; // 回退默认样式
  }
};

export const safeGetStatusIndicatorClasses = (status: unknown): StyleClasses => {
  try {
    const validStatus = validateTaskStatus(status);
    return getStatusIndicatorClasses(validStatus);
  } catch (error) {
    console.error('Error generating status indicator classes:', error);
    return 'inline-flex items-center gap-1 px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-600';
  }
};

export const safeGetButtonClasses = (config: unknown): StyleClasses => {
  try {
    if (typeof config === 'string') {
      const validVariant = validateButtonVariant(config);
      return getButtonClasses(validVariant);
    }
    if (typeof config === 'object' && config !== null) {
      const buttonConfig = config as Partial<ButtonConfig>;
      const safeConfig: ButtonConfig = {
        ...buttonConfig,
        variant: buttonConfig.variant ? validateButtonVariant(buttonConfig.variant) : 'primary'
      };
      return getButtonClasses(safeConfig);
    }
    return getButtonClasses('primary');
  } catch (error) {
    console.error('Error generating button classes:', error);
    return 'px-3 py-1.5 text-sm font-medium rounded bg-gray-100 text-gray-700'; // 回退默认样式
  }
};