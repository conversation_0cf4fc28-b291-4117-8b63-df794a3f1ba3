#!/usr/bin/env python3
"""
打包后应用调试脚本
用于测试和监控打包后的 Tauri 应用的运行状态
"""

import os
import sys
import subprocess
import time
import threading
import signal
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/tmp/packaged_app_debug.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class PackagedAppDebugger:
    def __init__(self, app_path):
        self.app_path = app_path
        self.processes = {}
        self.running = True
        
    def log_system_info(self):
        """记录系统信息"""
        logger.info("=== 系统信息 ===")
        logger.info(f"当前时间: {datetime.now()}")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"操作系统: {os.uname()}")
        logger.info(f"当前用户: {os.getenv('USER', 'unknown')}")
        logger.info(f"当前工作目录: {os.getcwd()}")
        logger.info(f"应用路径: {self.app_path}")
        
    def check_app_structure(self):
        """检查应用结构"""
        logger.info("=== 检查应用结构 ===")
        
        if not os.path.exists(self.app_path):
            logger.error(f"应用不存在: {self.app_path}")
            return False
            
        # 检查主要组件
        main_executable = os.path.join(self.app_path, "Contents", "MacOS", "XX交易终端")
        python_services = [
            os.path.join(self.app_path, "Contents", "MacOS", "counter-service"),
            os.path.join(self.app_path, "Contents", "MacOS", "futu-adapter"),
            os.path.join(self.app_path, "Contents", "MacOS", "huasheng-adapter")
        ]
        
        logger.info(f"主执行文件: {main_executable} - 存在: {os.path.exists(main_executable)}")
        
        for service in python_services:
            exists = os.path.exists(service)
            executable = os.access(service, os.X_OK) if exists else False
            logger.info(f"Python服务: {os.path.basename(service)} - 存在: {exists}, 可执行: {executable}")
            
            if exists:
                # 检查文件类型
                try:
                    result = subprocess.run(['file', service], capture_output=True, text=True)
                    logger.info(f"  文件类型: {result.stdout.strip()}")
                except Exception as e:
                    logger.error(f"  无法检查文件类型: {e}")
                    
        return True
        
    def start_with_monitoring(self):
        """启动应用并监控"""
        logger.info("=== 启动应用监控 ===")
        
        try:
            # 使用 open 命令启动应用，这是 macOS 的标准方式
            cmd = ['open', '-W', self.app_path]  # -W 参数让命令等待应用退出
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            # 启动应用进程
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            logger.info(f"应用已启动，进程ID: {process.pid}")
            
            # 监控进程输出
            self.monitor_process_output(process)
            
            # 等待进程结束
            return_code = process.wait()
            logger.info(f"应用退出，返回码: {return_code}")
            
            return return_code
            
        except Exception as e:
            logger.error(f"启动应用失败: {e}")
            return -1
            
    def monitor_process_output(self, process):
        """监控进程输出"""
        def read_stream(stream, stream_name):
            try:
                for line in iter(stream.readline, ''):
                    if line.strip():
                        logger.info(f"[{stream_name}] {line.strip()}")
            except Exception as e:
                logger.error(f"读取{stream_name}流时出错: {e}")
                
        # 创建线程监控输出流
        stdout_thread = threading.Thread(
            target=read_stream, 
            args=(process.stdout, "STDOUT")
        )
        stderr_thread = threading.Thread(
            target=read_stream, 
            args=(process.stderr, "STDERR")
        )
        
        stdout_thread.daemon = True
        stderr_thread.daemon = True
        
        stdout_thread.start()
        stderr_thread.start()
        
    def check_running_processes(self):
        """检查相关进程"""
        logger.info("=== 检查相关进程 ===")
        
        try:
            # 查找相关进程
            result = subprocess.run(
                ['ps', 'aux'], 
                capture_output=True, 
                text=True
            )
            
            lines = result.stdout.split('\n')
            relevant_processes = []
            
            for line in lines:
                if any(keyword in line.lower() for keyword in [
                    'XX交易终端', 'counter-service', 'futu-adapter', 'huasheng-adapter'
                ]):
                    relevant_processes.append(line)
                    logger.info(f"发现相关进程: {line}")
                    
            if not relevant_processes:
                logger.info("没有发现相关运行进程")
                
        except Exception as e:
            logger.error(f"检查进程失败: {e}")
            
    def check_console_logs(self):
        """检查系统控制台日志"""
        logger.info("=== 检查控制台日志 ===")
        
        try:
            # 查看最近的系统日志，过滤应用相关信息
            cmd = [
                'log', 'show', '--last', '5m', '--predicate', 
                'process CONTAINS "富途" OR process CONTAINS "counter" OR process CONTAINS "futu" OR process CONTAINS "huasheng"'
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
            
            if result.stdout.strip():
                logger.info("系统日志中的相关信息:")
                for line in result.stdout.split('\n')[-20:]:  # 只显示最后20行
                    if line.strip():
                        logger.info(f"  {line}")
            else:
                logger.info("系统日志中未发现相关信息")
                
        except subprocess.TimeoutExpired:
            logger.warning("查询系统日志超时")
        except Exception as e:
            logger.error(f"查询系统日志失败: {e}")
            
    def test_python_services_individually(self):
        """单独测试Python服务"""
        logger.info("=== 单独测试Python服务 ===")
        
        services = [
            "counter-service",
            "futu-adapter", 
            "huasheng-adapter"
        ]
        
        for service_name in services:
            service_path = os.path.join(self.app_path, "Contents", "MacOS", service_name)
            
            if not os.path.exists(service_path):
                logger.error(f"服务不存在: {service_path}")
                continue
                
            logger.info(f"测试服务: {service_name}")
            
            try:
                # 尝试直接运行服务
                result = subprocess.run(
                    [service_path, '--help'],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                
                logger.info(f"  返回码: {result.returncode}")
                if result.stdout:
                    logger.info(f"  输出: {result.stdout[:200]}...")
                if result.stderr:
                    logger.info(f"  错误: {result.stderr[:200]}...")
                    
            except subprocess.TimeoutExpired:
                logger.warning(f"  {service_name} 测试超时")
            except Exception as e:
                logger.error(f"  {service_name} 测试失败: {e}")
                
    def run_full_diagnosis(self):
        """运行完整诊断"""
        logger.info("开始打包后应用完整诊断")
        logger.info("=" * 60)
        
        # 1. 记录系统信息
        self.log_system_info()
        
        # 2. 检查应用结构
        if not self.check_app_structure():
            logger.error("应用结构检查失败，停止诊断")
            return False
            
        # 3. 检查当前运行的相关进程
        self.check_running_processes()
        
        # 4. 单独测试Python服务
        self.test_python_services_individually()
        
        # 5. 检查系统日志
        self.check_console_logs()
        
        # 6. 启动应用并监控
        logger.info("准备启动应用...")
        return_code = self.start_with_monitoring()
        
        # 7. 启动后再次检查进程和日志
        logger.info("应用退出后检查:")
        self.check_running_processes()
        self.check_console_logs()
        
        return return_code == 0

def main():
    app_path = "/Users/<USER>/Desktop/stock/src-tauri/target/release/bundle/macos/XX交易终端.app"
    
    debugger = PackagedAppDebugger(app_path)
    
    # 设置信号处理
    def signal_handler(sig, frame):
        logger.info("收到中断信号，正在停止...")
        debugger.running = False
        sys.exit(0)
        
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 运行完整诊断
    success = debugger.run_full_diagnosis()
    
    if success:
        logger.info("诊断完成，应用运行正常")
        return 0
    else:
        logger.error("诊断发现问题")
        return 1

if __name__ == "__main__":
    sys.exit(main())