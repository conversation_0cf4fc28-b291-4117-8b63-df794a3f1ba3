#!/bin/bash
# 自动化构建测试脚本
# 用于彻底清理、重建并验证 Tauri 应用构建过程

set -e  # 脚本遇到错误时退出

# 颜色输出函数
print_header() { echo -e "\033[1;34m=== $1 ===\033[0m"; }
print_success() { echo -e "\033[1;32m✅ $1\033[0m"; }
print_error() { echo -e "\033[1;31m❌ $1\033[0m"; }
print_warning() { echo -e "\033[1;33m⚠️ $1\033[0m"; }
print_info() { echo -e "\033[1;36m📋 $1\033[0m"; }

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

LOG_DIR="$PROJECT_ROOT/logs"
TIMESTAMP=$(date "+%Y%m%d_%H%M%S")
BUILD_LOG="$LOG_DIR/build_test_$TIMESTAMP.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 记录日志的函数
log_and_print() {
    echo "$1" | tee -a "$BUILD_LOG"
}

# 开始测试
print_header "开始自动化构建测试 - $(date)"
log_and_print "构建测试开始时间: $(date)"
log_and_print "项目目录: $PROJECT_ROOT"

# 第一步：完全清理
print_header "第一步：完全清理所有缓存和构建产物"
{
    print_info "清理 Rust 构建缓存..."
    if [ -d "src-tauri/target" ]; then
        rm -rf src-tauri/target/
        log_and_print "已删除 src-tauri/target/"
    fi
    
    print_info "清理二进制文件..."
    if [ -d "src-tauri/bin" ]; then
        rm -rf src-tauri/bin/
        log_and_print "已删除 src-tauri/bin/"
    fi
    
    print_info "清理前端构建产物..."
    if [ -d "dist" ]; then
        rm -rf dist/
        log_and_print "已删除 dist/"
    fi
    
    print_info "清理 Python 构建临时文件..."
    if [ -d "build" ]; then
        rm -rf build/
        log_and_print "已删除 build/"
    fi
    
    if [ -d "__pycache__" ]; then
        find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
        log_and_print "已清理 __pycache__ 目录"
    fi
    
    if [ -d ".pytest_cache" ]; then
        rm -rf .pytest_cache/
        log_and_print "已删除 .pytest_cache/"
    fi
    
    print_success "清理完成"
}

# 第二步：重新构建 Python 二进制文件
print_header "第二步：重新构建 Python 二进制文件"
{
    print_info "执行 Python 二进制构建..."
    if python3 scripts/build-python-binaries.py >> "$BUILD_LOG" 2>&1; then
        print_success "Python 二进制文件构建成功"
        
        print_info "验证生成的二进制文件..."
        if [ -d "src-tauri/bin" ]; then
            log_and_print "生成的二进制文件列表:"
            ls -la src-tauri/bin/ | tee -a "$BUILD_LOG"
            
            # 检查每个二进制文件是否可执行
            for binary in src-tauri/bin/*; do
                if [ -f "$binary" ] && [ -x "$binary" ]; then
                    log_and_print "✅ $binary 存在且可执行"
                else
                    log_and_print "❌ $binary 不存在或不可执行"
                fi
            done
        else
            print_error "二进制文件目录不存在"
            exit 1
        fi
    else
        print_error "Python 二进制文件构建失败"
        exit 1
    fi
}

# 第三步：验证脚本内容
print_header "第三步：验证生成的脚本内容"
{
    print_info "检查 counter-service 脚本内容..."
    
    # 使用 strings 命令查看二进制文件中的字符串，寻找路径信息
    if command -v strings >/dev/null 2>&1; then
        strings src-tauri/bin/counter-service | grep -E "(\/.*python|sys\.path|__file__|PYTHONPATH)" > "$LOG_DIR/counter-service-strings_$TIMESTAMP.log" 2>/dev/null || true
        
        if [ -s "$LOG_DIR/counter-service-strings_$TIMESTAMP.log" ]; then
            log_and_print "counter-service 中找到的路径信息:"
            cat "$LOG_DIR/counter-service-strings_$TIMESTAMP.log" | head -20 | tee -a "$BUILD_LOG"
        else
            log_and_print "⚠️ 未在 counter-service 中找到明显的路径信息"
        fi
    else
        print_warning "未找到 strings 命令，跳过内容验证"
    fi
    
    print_success "脚本内容验证完成"
}

# 第四步：构建前端
print_header "第四步：构建前端"
{
    print_info "安装前端依赖..."
    if yarn install >> "$BUILD_LOG" 2>&1; then
        print_success "前端依赖安装成功"
    else
        print_error "前端依赖安装失败"
        exit 1
    fi
    
    print_info "构建前端..."
    if yarn build >> "$BUILD_LOG" 2>&1; then
        print_success "前端构建成功"
        
        if [ -d "dist" ]; then
            log_and_print "前端构建产物:"
            ls -la dist/ | tee -a "$BUILD_LOG"
        fi
    else
        print_error "前端构建失败"
        exit 1
    fi
}

# 第五步：Tauri 构建
print_header "第五步：执行 Tauri 构建"
{
    print_info "开始 Tauri 构建..."
    
    # 记录构建开始时间
    BUILD_START=$(date +%s)
    log_and_print "Tauri 构建开始时间: $(date)"
    
    if yarn tauri build >> "$BUILD_LOG" 2>&1; then
        BUILD_END=$(date +%s)
        BUILD_DURATION=$((BUILD_END - BUILD_START))
        print_success "Tauri 构建成功，耗时: ${BUILD_DURATION}秒"
        
        # 查找构建产物
        print_info "查找构建产物..."
        APP_BUNDLE=$(find src-tauri/target -name "*.app" -type d 2>/dev/null | head -1)
        if [ -n "$APP_BUNDLE" ]; then
            log_and_print "找到应用包: $APP_BUNDLE"
            
            # 验证应用包结构
            if [ -d "$APP_BUNDLE/Contents/MacOS" ]; then
                log_and_print "应用包结构正确"
                
                # 列出应用包内容
                log_and_print "应用包内容:"
                ls -la "$APP_BUNDLE/Contents/" | tee -a "$BUILD_LOG"
                
                if [ -d "$APP_BUNDLE/Contents/MacOS" ]; then
                    log_and_print "MacOS 目录内容:"
                    ls -la "$APP_BUNDLE/Contents/MacOS/" | tee -a "$BUILD_LOG"
                fi
                
                if [ -d "$APP_BUNDLE/Contents/Resources" ]; then
                    log_and_print "Resources 目录内容:"
                    ls -la "$APP_BUNDLE/Contents/Resources/" | tee -a "$BUILD_LOG"
                fi
            else
                print_error "应用包结构异常"
                exit 1
            fi
        else
            print_error "未找到构建产物"
            exit 1
        fi
    else
        print_error "Tauri 构建失败"
        exit 1
    fi
}

# 第六步：验证打包后的脚本
print_header "第六步：验证打包后的二进制文件"
{
    if [ -n "$APP_BUNDLE" ]; then
        print_info "检查应用包中的二进制文件..."
        
        # 检查 sidecar 二进制文件
        SIDECAR_DIR="$APP_BUNDLE/Contents/MacOS"
        for binary in counter-service futu-adapter huasheng-adapter; do
            BINARY_PATH="$SIDECAR_DIR/$binary"
            if [ -f "$BINARY_PATH" ] && [ -x "$BINARY_PATH" ]; then
                log_and_print "✅ $binary 存在于应用包中且可执行"
                
                # 检查文件大小
                SIZE=$(stat -f%z "$BINARY_PATH" 2>/dev/null || stat -c%s "$BINARY_PATH" 2>/dev/null || echo "unknown")
                log_and_print "   文件大小: $SIZE bytes"
                
                # 使用 strings 检查内容（如果可用）
                if command -v strings >/dev/null 2>&1; then
                    strings "$BINARY_PATH" | grep -E "(\/.*python|sys\.path|__file__|PYTHONPATH)" > "$LOG_DIR/${binary}-packaged-strings_$TIMESTAMP.log" 2>/dev/null || true
                    
                    if [ -s "$LOG_DIR/${binary}-packaged-strings_$TIMESTAMP.log" ]; then
                        log_and_print "   打包后 $binary 中找到的路径信息:"
                        cat "$LOG_DIR/${binary}-packaged-strings_$TIMESTAMP.log" | head -10 | tee -a "$BUILD_LOG"
                    else
                        log_and_print "   ⚠️ 打包后 $binary 中未找到明显的路径信息"
                    fi
                fi
            else
                print_error "$binary 不存在于应用包中或不可执行"
                log_and_print "检查 $SIDECAR_DIR 目录内容:"
                ls -la "$SIDECAR_DIR/" | tee -a "$BUILD_LOG"
            fi
        done
        
        print_success "打包后的二进制文件验证完成"
    fi
}

# 生成构建报告
print_header "构建测试报告"
{
    print_success "构建测试完成！"
    log_and_print "构建测试完成时间: $(date)"
    
    echo ""
    print_info "测试结果汇总:"
    log_and_print "✅ 清理阶段: 完成"
    log_and_print "✅ Python 二进制构建: 完成"
    log_and_print "✅ 脚本内容验证: 完成"
    log_and_print "✅ 前端构建: 完成"
    log_and_print "✅ Tauri 构建: 完成"
    log_and_print "✅ 打包验证: 完成"
    
    echo ""
    print_info "关键文件位置:"
    if [ -n "$APP_BUNDLE" ]; then
        log_and_print "应用包: $APP_BUNDLE"
    fi
    log_and_print "构建日志: $BUILD_LOG"
    log_and_print "字符串分析日志目录: $LOG_DIR"
    
    echo ""
    print_info "下一步: 使用 auto-app-test.sh 测试应用功能"
}