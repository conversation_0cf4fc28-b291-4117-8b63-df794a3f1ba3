#!/bin/bash
# 主控制脚本 - 全自动化测试和调试流程
# 整合构建测试、应用测试和日志分析的完整流程

set -e

# 颜色输出函数
print_header() { echo -e "\033[1;34m=== $1 ===\033[0m"; }
print_success() { echo -e "\033[1;32m✅ $1\033[0m"; }
print_error() { echo -e "\033[1;31m❌ $1\033[0m"; }
print_warning() { echo -e "\033[1;33m⚠️ $1\033[0m"; }
print_info() { echo -e "\033[1;36m📋 $1\033[0m"; }

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

LOG_DIR="$PROJECT_ROOT/logs"
TIMESTAMP=$(date "+%Y%m%d_%H%M%S")
MASTER_LOG="$LOG_DIR/master_test_$TIMESTAMP.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 记录日志的函数
log_and_print() {
    echo "$1" | tee -a "$MASTER_LOG"
}

# 显示使用说明
show_usage() {
    cat << EOF
🚀 Tauri 应用自动化测试和调试流程

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -b, --build-only        仅执行构建测试
    -t, --test-only         仅执行应用测试 (需要已构建的应用)
    -a, --analyze-only      仅执行日志分析
    -f, --full-flow         执行完整流程 (默认)
    -c, --clean             执行完整清理后再开始
    -s, --skip-build        跳过构建步骤，直接测试现有应用
    --no-interaction        非交互模式，自动执行所有步骤
    --log-level <level>     日志级别 (debug, info, warn, error)

示例:
    $0                      # 执行完整流程
    $0 --build-only         # 仅执行构建测试
    $0 --clean --full-flow  # 完整清理后执行完整流程
    $0 --test-only          # 仅测试已构建的应用
    $0 --analyze-only       # 仅分析现有日志

注意:
    - 首次运行建议使用 --clean --full-flow
    - 如果构建失败，先使用 --build-only 排查问题
    - 应用测试失败时，使用 --analyze-only 分析日志
EOF
}

# 解析命令行参数
OPERATION="full"
CLEAN_FIRST=false
SKIP_BUILD=false
NO_INTERACTION=false
LOG_LEVEL="info"

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        -b|--build-only)
            OPERATION="build"
            shift
            ;;
        -t|--test-only)
            OPERATION="test"
            shift
            ;;
        -a|--analyze-only)
            OPERATION="analyze"
            shift
            ;;
        -f|--full-flow)
            OPERATION="full"
            shift
            ;;
        -c|--clean)
            CLEAN_FIRST=true
            shift
            ;;
        -s|--skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --no-interaction)
            NO_INTERACTION=true
            shift
            ;;
        --log-level)
            LOG_LEVEL="$2"
            shift 2
            ;;
        *)
            print_error "未知选项: $1"
            show_usage
            exit 1
            ;;
    esac
done

# 检查必要的工具
check_prerequisites() {
    local missing_tools=()
    
    command -v yarn >/dev/null 2>&1 || missing_tools+=("yarn")
    command -v python3 >/dev/null 2>&1 || missing_tools+=("python3")
    
    if [ ${#missing_tools[@]} -gt 0 ]; then
        print_error "缺少必要的工具: ${missing_tools[*]}"
        print_info "请安装缺少的工具后再运行此脚本"
        exit 1
    fi
}

# 确认操作
confirm_operation() {
    if [ "$NO_INTERACTION" = true ]; then
        return 0
    fi
    
    echo ""
    print_info "即将执行的操作:"
    case $OPERATION in
        "build") print_info "  📦 构建测试" ;;
        "test") print_info "  🧪 应用测试" ;;
        "analyze") print_info "  📊 日志分析" ;;
        "full") print_info "  🔄 完整流程 (构建 + 测试 + 分析)" ;;
    esac
    
    if [ "$CLEAN_FIRST" = true ]; then
        print_info "  🧹 包含完整清理"
    fi
    
    if [ "$SKIP_BUILD" = true ]; then
        print_info "  ⏭️  跳过构建步骤"
    fi
    
    echo ""
    read -p "是否继续? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "操作已取消"
        exit 0
    fi
}

# 执行构建测试
run_build_test() {
    print_header "执行构建测试"
    log_and_print "开始构建测试: $(date)"
    
    if [ ! -x "scripts/auto-build-test.sh" ]; then
        print_error "构建测试脚本不存在或不可执行: scripts/auto-build-test.sh"
        return 1
    fi
    
    if bash scripts/auto-build-test.sh; then
        print_success "构建测试完成"
        return 0
    else
        print_error "构建测试失败"
        return 1
    fi
}

# 执行应用测试
run_app_test() {
    print_header "执行应用测试"
    log_and_print "开始应用测试: $(date)"
    
    if [ ! -x "scripts/auto-app-test.sh" ]; then
        print_error "应用测试脚本不存在或不可执行: scripts/auto-app-test.sh"
        return 1
    fi
    
    # 检查是否存在构建产物
    local app_bundle=$(find src-tauri/target -name "*.app" -type d 2>/dev/null | head -1)
    if [ -z "$app_bundle" ]; then
        print_error "未找到应用包，请先执行构建测试"
        return 1
    fi
    
    if timeout 300 bash scripts/auto-app-test.sh; then  # 5分钟超时
        print_success "应用测试完成"
        return 0
    else
        local exit_code=$?
        if [ $exit_code -eq 124 ]; then
            print_warning "应用测试超时，但可能已收集到有用信息"
        else
            print_error "应用测试失败"
        fi
        return $exit_code
    fi
}

# 执行日志分析
run_log_analysis() {
    print_header "执行日志分析"
    log_and_print "开始日志分析: $(date)"
    
    if [ ! -x "scripts/log-analyzer.py" ]; then
        print_error "日志分析脚本不存在或不可执行: scripts/log-analyzer.py"
        return 1
    fi
    
    if [ ! -d "$LOG_DIR" ] || [ -z "$(ls -A "$LOG_DIR" 2>/dev/null)" ]; then
        print_warning "日志目录为空，没有日志可供分析"
        return 1
    fi
    
    local analysis_report="$LOG_DIR/analysis_report_$TIMESTAMP.txt"
    
    if python3 scripts/log-analyzer.py --log-dir "$LOG_DIR" --output "$analysis_report" --show-fixes; then
        print_success "日志分析完成"
        log_and_print "分析报告已保存到: $analysis_report"
        return 0
    else
        print_error "日志分析失败"
        return 1
    fi
}

# 清理环境
clean_environment() {
    print_header "清理环境"
    log_and_print "开始环境清理: $(date)"
    
    # 停止可能运行的服务
    pkill -f "XX交易终端" 2>/dev/null || true
    pkill -f "counter-service" 2>/dev/null || true
    pkill -f "futu-adapter" 2>/dev/null || true
    pkill -f "huasheng-adapter" 2>/dev/null || true
    
    sleep 2
    
    # 强制清理
    pkill -9 -f "XX交易终端" 2>/dev/null || true
    pkill -9 -f "counter-service" 2>/dev/null || true
    pkill -9 -f "futu-adapter" 2>/dev/null || true
    pkill -9 -f "huasheng-adapter" 2>/dev/null || true
    
    print_success "环境清理完成"
}

# 生成最终报告
generate_final_report() {
    print_header "生成最终测试报告"
    
    local final_report="$LOG_DIR/final_report_$TIMESTAMP.md"
    
    cat > "$final_report" << EOF
# Tauri 应用自动化测试报告

**测试时间**: $(date)
**项目目录**: $PROJECT_ROOT
**执行的操作**: $OPERATION

## 测试概览

EOF
    
    if [ "$OPERATION" = "build" ] || [ "$OPERATION" = "full" ]; then
        if [ -f "$LOG_DIR/build_test_$TIMESTAMP.log" ]; then
            echo "✅ 构建测试: 已执行" >> "$final_report"
        else
            echo "❌ 构建测试: 未执行或失败" >> "$final_report"
        fi
    fi
    
    if [ "$OPERATION" = "test" ] || [ "$OPERATION" = "full" ]; then
        if [ -f "$LOG_DIR/app_test_$TIMESTAMP.log" ]; then
            echo "✅ 应用测试: 已执行" >> "$final_report"
        else
            echo "❌ 应用测试: 未执行或失败" >> "$final_report"
        fi
    fi
    
    if [ "$OPERATION" = "analyze" ] || [ "$OPERATION" = "full" ]; then
        if [ -f "$LOG_DIR/analysis_report_$TIMESTAMP.txt" ]; then
            echo "✅ 日志分析: 已执行" >> "$final_report"
        else
            echo "❌ 日志分析: 未执行或失败" >> "$final_report"
        fi
    fi
    
    cat >> "$final_report" << EOF

## 生成的文件

- **主日志**: $MASTER_LOG
- **最终报告**: $final_report

$(ls -la "$LOG_DIR"/*_$TIMESTAMP* 2>/dev/null | sed 's/^/- /' || echo "- 无其他日志文件")

## 下一步建议

1. 查看主日志文件获取详细执行信息
2. 如果有分析报告，重点关注其中的问题和建议
3. 根据发现的问题进行针对性修复
4. 修复后重新运行测试验证效果

---
*此报告由自动化测试脚本生成*
EOF
    
    print_success "最终报告已生成: $final_report"
    log_and_print "最终报告已生成: $final_report"
}

# 主流程
main() {
    print_header "Tauri 应用自动化测试和调试流程"
    log_and_print "测试流程开始: $(date)"
    log_and_print "执行操作: $OPERATION"
    log_and_print "项目目录: $PROJECT_ROOT"
    
    # 检查前置条件
    check_prerequisites
    
    # 确认操作
    confirm_operation
    
    # 清理环境 (如果需要)
    if [ "$CLEAN_FIRST" = true ]; then
        clean_environment
    fi
    
    local overall_success=true
    
    # 根据操作类型执行相应步骤
    case $OPERATION in
        "build")
            if ! run_build_test; then
                overall_success=false
            fi
            ;;
        "test")
            if ! run_app_test; then
                overall_success=false
            fi
            ;;
        "analyze")
            if ! run_log_analysis; then
                overall_success=false
            fi
            ;;
        "full")
            # 构建测试
            if [ "$SKIP_BUILD" != true ]; then
                if ! run_build_test; then
                    overall_success=false
                    print_warning "构建测试失败，但继续执行后续步骤..."
                fi
            fi
            
            # 应用测试
            if ! run_app_test; then
                overall_success=false
                print_warning "应用测试失败，但继续执行日志分析..."
            fi
            
            # 日志分析
            if ! run_log_analysis; then
                print_warning "日志分析失败"
            fi
            ;;
    esac
    
    # 最后清理
    clean_environment
    
    # 生成最终报告
    generate_final_report
    
    # 总结
    print_header "测试流程总结"
    log_and_print "测试流程完成: $(date)"
    
    if [ "$overall_success" = true ]; then
        print_success "所有测试步骤均成功完成！"
        echo ""
        print_info "主要输出文件:"
        print_info "  📋 主日志: $MASTER_LOG"
        if [ -f "$LOG_DIR/analysis_report_$TIMESTAMP.txt" ]; then
            print_info "  📊 分析报告: $LOG_DIR/analysis_report_$TIMESTAMP.txt"
        fi
        print_info "  📄 最终报告: $LOG_DIR/final_report_$TIMESTAMP.md"
    else
        print_error "部分测试步骤失败"
        print_info "请检查日志文件以获取详细信息:"
        print_info "  📋 主日志: $MASTER_LOG"
        print_info "  📄 最终报告: $LOG_DIR/final_report_$TIMESTAMP.md"
        exit 1
    fi
}

# 设置退出时清理
trap clean_environment EXIT

# 运行主流程
main "$@"