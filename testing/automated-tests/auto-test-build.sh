#!/bin/bash

# 全自动化构建测试流程
# 用于诊断和解决 Tauri 打包问题

set -e

echo "🚀 启动全自动化构建测试流程"
echo "================================="

PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
cd "$PROJECT_ROOT"

# 创建日志目录
LOGS_DIR="$HOME/Desktop/tauri_debug_logs_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$LOGS_DIR"

echo "📝 日志目录: $LOGS_DIR"

# 1. 环境检查
echo
echo "1️⃣ 环境检查"
echo "============"
echo "uv 版本:" | tee "$LOGS_DIR/01_env_check.log"
uv --version >> "$LOGS_DIR/01_env_check.log" 2>&1 || echo "uv 未安装" >> "$LOGS_DIR/01_env_check.log"
echo "node 版本:" >> "$LOGS_DIR/01_env_check.log"
node --version >> "$LOGS_DIR/01_env_check.log" 2>&1
echo "yarn 版本:" >> "$LOGS_DIR/01_env_check.log"
yarn --version >> "$LOGS_DIR/01_env_check.log" 2>&1
echo "tauri 版本:" >> "$LOGS_DIR/01_env_check.log"
yarn tauri --version >> "$LOGS_DIR/01_env_check.log" 2>&1

# 2. 完全清理
echo
echo "2️⃣ 完全清理构建产物"
echo "===================="
echo "清理目标:" | tee "$LOGS_DIR/02_cleanup.log"
rm -rf src-tauri/target/ src-tauri/bin/ node_modules/.vite dist >> "$LOGS_DIR/02_cleanup.log" 2>&1
echo "✅ 清理完成" >> "$LOGS_DIR/02_cleanup.log"

# 3. 验证脚本生成
echo
echo "3️⃣ 生成和验证 Python 包装脚本"
echo "==============================="
./scripts/build-python-binaries.sh > "$LOGS_DIR/03_script_generation.log" 2>&1

echo "检查生成的脚本:" | tee -a "$LOGS_DIR/03_script_generation.log"
for script in counter-service futu-adapter huasheng-adapter; do
    if [ -f "src-tauri/bin/$script" ]; then
        echo "✅ $script 存在" | tee -a "$LOGS_DIR/03_script_generation.log"
        if grep -q "*/Contents/MacOS" "src-tauri/bin/$script"; then
            echo "  ✅ 使用正确的路径检测" | tee -a "$LOGS_DIR/03_script_generation.log"
        else
            echo "  ❌ 路径检测不正确" | tee -a "$LOGS_DIR/03_script_generation.log"
        fi
        echo "  脚本前10行:" >> "$LOGS_DIR/03_script_generation.log"
        head -10 "src-tauri/bin/$script" >> "$LOGS_DIR/03_script_generation.log"
    else
        echo "❌ $script 不存在" | tee -a "$LOGS_DIR/03_script_generation.log"
    fi
done

# 4. Tauri 构建
echo
echo "4️⃣ Tauri 构建"
echo "=============="
echo "开始构建..." | tee "$LOGS_DIR/04_tauri_build.log"
yarn tauri build >> "$LOGS_DIR/04_tauri_build.log" 2>&1

# 检查构建结果
APP_PATH="src-tauri/target/release/bundle/macos/XX交易终端.app"
if [ -d "$APP_PATH" ]; then
    echo "✅ 应用构建成功" | tee -a "$LOGS_DIR/04_tauri_build.log"
    
    # 验证打包后的脚本
    echo "验证打包后的脚本:" >> "$LOGS_DIR/04_tauri_build.log"
    PACKAGED_SCRIPT="$APP_PATH/Contents/MacOS/counter-service"
    if [ -f "$PACKAGED_SCRIPT" ]; then
        echo "✅ 打包后的 counter-service 存在" >> "$LOGS_DIR/04_tauri_build.log"
        echo "脚本内容:" >> "$LOGS_DIR/04_tauri_build.log"
        cat "$PACKAGED_SCRIPT" >> "$LOGS_DIR/04_tauri_build.log"
        
        if grep -q "*/Contents/MacOS" "$PACKAGED_SCRIPT"; then
            echo "✅ 打包后的脚本使用正确路径检测" | tee -a "$LOGS_DIR/04_tauri_build.log"
        else
            echo "❌ 打包后的脚本还是旧版本" | tee -a "$LOGS_DIR/04_tauri_build.log"
            echo "这是问题的根源！" | tee -a "$LOGS_DIR/04_tauri_build.log"
        fi
    else
        echo "❌ 打包后的 counter-service 不存在" >> "$LOGS_DIR/04_tauri_build.log"
    fi
    
    # 检查 Python 资源
    echo "检查 Python 资源:" >> "$LOGS_DIR/04_tauri_build.log"
    PYTHON_SCRIPT="$APP_PATH/Contents/Resources/_up_/src-python/test_services/counter_service.py"
    if [ -f "$PYTHON_SCRIPT" ]; then
        echo "✅ Python 脚本正确打包" >> "$LOGS_DIR/04_tauri_build.log"
    else
        echo "❌ Python 脚本未正确打包" >> "$LOGS_DIR/04_tauri_build.log"
        echo "Resources 目录内容:" >> "$LOGS_DIR/04_tauri_build.log"
        find "$APP_PATH/Contents/Resources" -type f -name "*.py" >> "$LOGS_DIR/04_tauri_build.log"
    fi
else
    echo "❌ 应用构建失败" | tee -a "$LOGS_DIR/04_tauri_build.log"
    exit 1
fi

# 5. 应用功能测试
echo
echo "5️⃣ 应用功能测试"
echo "================"

# 创建应用测试脚本
cat > "$LOGS_DIR/test_app.py" << 'EOF'
#!/usr/bin/env python3
import time
import subprocess
import os
import signal
import sys
from pathlib import Path

def test_packaged_app():
    app_path = sys.argv[1] if len(sys.argv) > 1 else None
    if not app_path or not os.path.exists(app_path):
        print("❌ 应用路径无效")
        return False
    
    print(f"📱 测试应用: {app_path}")
    
    # 启动应用
    print("启动应用...")
    try:
        # 在后台启动应用
        proc = subprocess.Popen(['open', app_path], 
                              stdout=subprocess.PIPE, 
                              stderr=subprocess.PIPE)
        
        # 等待应用启动
        print("等待应用启动...")
        time.sleep(5)
        
        # 检查应用是否在运行
        result = subprocess.run(['pgrep', '-f', 'XX交易终端'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 应用进程正在运行")
            pids = result.stdout.strip().split('\n')
            print(f"进程 PID: {pids}")
            
            # 等待更多时间让服务启动
            print("等待服务启动...")
            time.sleep(10)
            
            # 检查 Python 子进程
            python_result = subprocess.run(['pgrep', '-f', 'python.*counter_service'], 
                                         capture_output=True, text=True)
            if python_result.returncode == 0:
                print("✅ Python 服务进程正在运行")
                print(f"Python PID: {python_result.stdout.strip()}")
            else:
                print("❌ Python 服务进程未运行")
            
            # 清理 - 终止应用
            print("清理进程...")
            for pid in pids:
                try:
                    os.kill(int(pid), signal.SIGTERM)
                except:
                    pass
            
            return True
        else:
            print("❌ 应用未运行")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程出错: {e}")
        return False

if __name__ == "__main__":
    success = test_packaged_app()
    sys.exit(0 if success else 1)
EOF

chmod +x "$LOGS_DIR/test_app.py"

# 运行应用测试
echo "运行应用测试..." | tee "$LOGS_DIR/05_app_test.log"
python3 "$LOGS_DIR/test_app.py" "$APP_PATH" >> "$LOGS_DIR/05_app_test.log" 2>&1

# 6. 手动脚本修复测试
echo
echo "6️⃣ 手动修复脚本测试"
echo "==================="

# 如果打包后的脚本是旧版本，手动修复它
if ! grep -q "*/Contents/MacOS" "$PACKAGED_SCRIPT"; then
    echo "检测到旧版本脚本，进行手动修复..." | tee "$LOGS_DIR/06_manual_fix.log"
    
    # 备份旧脚本
    cp "$PACKAGED_SCRIPT" "$PACKAGED_SCRIPT.backup"
    
    # 复制正确的脚本
    if [ -f "src-tauri/bin/counter-service" ]; then
        cp "src-tauri/bin/counter-service" "$PACKAGED_SCRIPT"
        echo "✅ 脚本已手动修复" | tee -a "$LOGS_DIR/06_manual_fix.log"
        
        # 再次测试
        echo "测试修复后的应用..." >> "$LOGS_DIR/06_manual_fix.log"
        python3 "$LOGS_DIR/test_app.py" "$APP_PATH" >> "$LOGS_DIR/06_manual_fix.log" 2>&1
    else
        echo "❌ 无法找到正确的本地脚本" | tee -a "$LOGS_DIR/06_manual_fix.log"
    fi
else
    echo "脚本已是最新版本，跳过手动修复" | tee "$LOGS_DIR/06_manual_fix.log"
fi

# 7. 生成测试报告
echo
echo "7️⃣ 生成测试报告"
echo "================"

cat > "$LOGS_DIR/test_report.md" << EOF
# Tauri 应用构建测试报告

生成时间: $(date)

## 测试环境
- 项目路径: $PROJECT_ROOT  
- 日志目录: $LOGS_DIR

## 测试结果摘要

### 1. 环境检查
$(cat "$LOGS_DIR/01_env_check.log")

### 2. 脚本生成验证
$(grep -E "(✅|❌)" "$LOGS_DIR/03_script_generation.log" || echo "无验证结果")

### 3. 构建验证  
$(grep -E "(✅|❌)" "$LOGS_DIR/04_tauri_build.log" || echo "无验证结果")

### 4. 应用测试
$(cat "$LOGS_DIR/05_app_test.log")

### 5. 手动修复测试
$(cat "$LOGS_DIR/06_manual_fix.log")

## 问题诊断

$(if ! grep -q "*/Contents/MacOS" "$PACKAGED_SCRIPT" 2>/dev/null; then
echo "❌ **关键问题**: Tauri 打包过程使用了缓存的旧版脚本"
echo "   - 本地脚本是正确的新版本"
echo "   - 但打包后变成了硬编码路径的旧版本"
echo "   - 需要找到 Tauri 缓存脚本的原因"
else
echo "✅ 脚本版本正确"
fi)

## 建议修复方案

1. **立即解决方案**: 手动修复打包后的脚本 (已执行)
2. **根本解决方案**: 找出 Tauri 使用缓存脚本的原因
   - 检查 tauri.conf.json externalBin 配置
   - 检查是否有隐藏的缓存目录
   - 检查构建脚本的时间戳

## 详细日志文件

- 环境检查: $LOGS_DIR/01_env_check.log
- 清理日志: $LOGS_DIR/02_cleanup.log  
- 脚本生成: $LOGS_DIR/03_script_generation.log
- 构建日志: $LOGS_DIR/04_tauri_build.log
- 应用测试: $LOGS_DIR/05_app_test.log
- 修复测试: $LOGS_DIR/06_manual_fix.log

EOF

echo "📋 测试报告已生成: $LOGS_DIR/test_report.md"
echo
echo "🎉 自动化测试完成!"
echo "请查看测试报告了解详细结果和建议修复方案"
echo
echo "快速查看报告:"
echo "cat $LOGS_DIR/test_report.md"