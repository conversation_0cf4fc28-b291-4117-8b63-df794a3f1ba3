#!/bin/bash

# 主控测试脚本
# 统一运行所有诊断和测试流程

echo "🎯 Tauri 应用问题诊断与修复工具集"
echo "================================"
echo
echo "请选择要执行的操作:"
echo
echo "1. 🔍 完整自动化构建测试 (推荐)"
echo "2. 🔧 Tauri 缓存问题诊断"
echo "3. 📺 实时应用日志监控" 
echo "4. ⚡ 快速构建并测试"
echo "5. 🧪 手动修复打包后的脚本"
echo "6. 📋 显示所有工具说明"
echo "0. 退出"
echo

read -p "请输入选择 (0-6): " choice

case $choice in
    1)
        echo "🚀 启动完整自动化构建测试..."
        ./testing/automated-tests/auto-test-build.sh
        ;;
    2)
        echo "🔍 启动 Tauri 缓存诊断..."
        ./testing/automated-tests/diagnose-tauri-cache.sh
        ;;
    3)
        echo "📺 启动实时日志监控..."
        echo "监控将在后台运行，请在另一终端启动应用进行测试"
        ./testing/automated-tests/monitor-app-logs.sh
        ;;
    4)
        echo "⚡ 快速构建..."
        echo "⚠️ 快速构建脚本未迁移，请使用完整测试"
        ;;
    5)
        echo "🧪 手动修复打包后的脚本..."
        if [ -f "/tmp/fix_packaged_script.sh" ]; then
            /tmp/fix_packaged_script.sh
        else
            echo "请先运行缓存诊断生成修复脚本"
        fi
        ;;
    6)
        echo "📋 工具说明"
        echo "==========="
        echo
        echo "🔍 auto-test-build.sh - 完整自动化测试流程"
        echo "   功能: 清理、构建、验证、测试一站式完成"
        echo "   输出: 详细的测试报告和日志文件"
        echo "   用途: 发现和定位所有构建问题"
        echo
        echo "🔧 diagnose-tauri-cache.sh - Tauri 缓存问题诊断"
        echo "   功能: 专门分析 Tauri 为什么使用旧版本脚本"
        echo "   输出: 缓存位置、时间戳对比、修复脚本"
        echo "   用途: 找出构建缓存问题的根本原因"
        echo
        echo "📺 monitor-app-logs.sh - 实时日志监控"
        echo "   功能: 实时捕获应用运行时的所有日志"
        echo "   输出: 系统日志、进程状态、错误信息"
        echo "   用途: 观察应用实际运行状态和错误"
        echo
        echo "⚡ quick-build.sh - 快速构建测试"
        echo "   功能: 跳过清理直接构建和验证"
        echo "   用途: 快速验证脚本修复是否有效"
        echo
        echo "使用建议:"
        echo "1. 首次诊断: 运行选项 1 (完整测试)"
        echo "2. 问题定位: 运行选项 2 (缓存诊断)"
        echo "3. 实时监控: 运行选项 3 (日志监控)"
        echo "4. 快速验证: 运行选项 4 (快速构建)"
        ;;
    0)
        echo "👋 退出"
        exit 0
        ;;
    *)
        echo "❌ 无效选择"
        exit 1
        ;;
esac

echo
echo "🎉 操作完成！"
echo
read -p "是否要查看生成的日志文件？(y/n): " show_logs
if [[ $show_logs =~ ^[Yy]$ ]]; then
    echo "📂 打开日志目录..."
    open ~/Desktop/tauri_*logs*/ 2>/dev/null || echo "没有找到日志目录"
fi