#!/bin/bash

# 快速生产构建脚本
# 专门用于验证包装脚本修复

set -e

echo "🚀 快速生产构建..."

PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
cd "$PROJECT_ROOT"

# 1. 重新生成包装脚本
echo "1️⃣ 重新生成包装脚本..."
rm -f src-tauri/bin/*
./scripts/build-python-binaries.sh

# 2. 验证脚本内容
echo "2️⃣ 验证脚本内容..."
if grep -q "*/Contents/MacOS" src-tauri/bin/counter-service; then
    echo "✅ counter-service 使用正确的路径检测"
else
    echo "❌ counter-service 路径检测不正确"
    exit 1
fi

# 3. 直接构建（跳过清理节省时间）
echo "3️⃣ 开始 Tauri 构建..."
yarn tauri build

# 4. 验证打包结果
echo "4️⃣ 验证打包结果..."
APP_PATH="src-tauri/target/release/bundle/macos/XX交易终端.app"
if [ -f "$APP_PATH/Contents/MacOS/counter-service" ]; then
    if grep -q "*/Contents/MacOS" "$APP_PATH/Contents/MacOS/counter-service"; then
        echo "✅ 打包后的脚本使用正确的路径检测"
    else
        echo "❌ 打包后的脚本路径检测不正确"
        echo "实际内容:"
        head -15 "$APP_PATH/Contents/MacOS/counter-service"
    fi
else
    echo "❌ 打包后的应用不包含 counter-service 脚本"
fi

echo "🎉 快速构建完成！"