#!/bin/bash

# 实时应用日志监控脚本
# 用于捕获和分析 Tauri 应用的运行时日志

APP_NAME="XX交易终端"
LOG_DIR="$HOME/Desktop/tauri_runtime_logs_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$LOG_DIR"

echo "🔍 启动实时日志监控"
echo "==================="
echo "日志目录: $LOG_DIR"

# 函数：启动日志监控
start_monitoring() {
    echo "启动各种日志监控..."
    
    # 1. 监控系统控制台日志（应用相关）
    echo "1️⃣ 启动系统控制台日志监控..."
    log stream --predicate 'process == "XX交易终端"' --style syslog > "$LOG_DIR/console.log" 2>&1 &
    CONSOLE_PID=$!
    
    # 2. 监控 Python 相关进程
    echo "2️⃣ 启动进程监控..."
    while true; do
        echo "=== $(date) ===" >> "$LOG_DIR/processes.log"
        echo "XX交易终端进程:" >> "$LOG_DIR/processes.log"
        pgrep -fl "XX交易终端" >> "$LOG_DIR/processes.log" 2>&1
        echo "Python 进程:" >> "$LOG_DIR/processes.log"
        pgrep -fl "python.*counter" >> "$LOG_DIR/processes.log" 2>&1
        echo "UV 进程:" >> "$LOG_DIR/processes.log"
        pgrep -fl "uv" >> "$LOG_DIR/processes.log" 2>&1
        echo "" >> "$LOG_DIR/processes.log"
        sleep 2
    done &
    PROCESS_PID=$!
    
    # 3. 监控文件系统活动
    echo "3️⃣ 启动文件系统监控..."
    if command -v fswatch >/dev/null 2>&1; then
        fswatch -o /tmp /var/folders -e ".*" | while read num; do
            echo "$(date): File system activity detected" >> "$LOG_DIR/filesystem.log"
        done &
        FS_PID=$!
    fi
    
    echo "监控已启动，PID: console=$CONSOLE_PID, process=$PROCESS_PID"
    
    # 4. 手动测试脚本
    echo "4️⃣ 创建手动测试脚本..."
    cat > "$LOG_DIR/manual_test.sh" << 'EOF'
#!/bin/bash
echo "🧪 手动测试打包后的包装脚本"
echo "============================="

APP_PATH="$(find /Users/<USER>/Desktop/stock/src-tauri/target/release/bundle/macos -name "*.app" | head -1)"

if [ -z "$APP_PATH" ]; then
    echo "❌ 未找到打包后的应用"
    exit 1
fi

echo "📱 应用路径: $APP_PATH"

# 测试包装脚本
SCRIPT_PATH="$APP_PATH/Contents/MacOS/counter-service"
echo "📄 包装脚本路径: $SCRIPT_PATH"

if [ -f "$SCRIPT_PATH" ]; then
    echo "✅ 包装脚本存在"
    echo "脚本内容:"
    cat "$SCRIPT_PATH"
    echo
    echo "🔧 测试脚本执行..."
    # 设置正确的工作目录
    cd "$(dirname "$APP_PATH")"
    
    # 直接运行脚本（5秒后超时）
    timeout 5s "$SCRIPT_PATH" 2>&1 | head -20
    
    echo "脚本测试完成"
else
    echo "❌ 包装脚本不存在"
fi
EOF
    chmod +x "$LOG_DIR/manual_test.sh"
    
    return 0
}

# 函数：停止监控
stop_monitoring() {
    echo "停止日志监控..."
    if [ ! -z "$CONSOLE_PID" ]; then kill $CONSOLE_PID 2>/dev/null; fi
    if [ ! -z "$PROCESS_PID" ]; then kill $PROCESS_PID 2>/dev/null; fi
    if [ ! -z "$FS_PID" ]; then kill $FS_PID 2>/dev/null; fi
    echo "监控已停止"
}

# 函数：生成分析报告
generate_report() {
    echo "📊 生成日志分析报告..."
    
    cat > "$LOG_DIR/analysis_report.md" << EOF
# 实时日志监控分析报告

监控时间: $(date)
日志目录: $LOG_DIR

## 系统控制台日志分析
\`\`\`
$(tail -50 "$LOG_DIR/console.log" 2>/dev/null || echo "无控制台日志")
\`\`\`

## 进程监控分析
\`\`\`
$(tail -20 "$LOG_DIR/processes.log" 2>/dev/null || echo "无进程日志")
\`\`\`

## 手动脚本测试结果
\`\`\`
$("$LOG_DIR/manual_test.sh" 2>&1)
\`\`\`

## 问题分析

### 应用启动状态
$(if pgrep -q "XX交易终端"; then echo "✅ 主应用进程正在运行"; else echo "❌ 主应用进程未运行"; fi)

### Python 服务状态  
$(if pgrep -q "python.*counter"; then echo "✅ Python 服务进程正在运行"; else echo "❌ Python 服务进程未运行"; fi)

### 关键问题识别
$(
    if [ -f "$LOG_DIR/console.log" ]; then
        if grep -q "Failed to start" "$LOG_DIR/console.log"; then
            echo "❌ 发现启动失败错误"
        fi
        if grep -q "Broken pipe" "$LOG_DIR/console.log"; then
            echo "❌ 发现 Broken pipe 错误"
        fi
        if grep -q "Permission denied" "$LOG_DIR/console.log"; then
            echo "❌ 发现权限错误"
        fi
    fi
)

EOF

    echo "报告已生成: $LOG_DIR/analysis_report.md"
}

# 信号处理
trap 'stop_monitoring; generate_report; exit 0' SIGINT SIGTERM

echo "启动监控..."
start_monitoring

echo
echo "🎯 监控已就绪！"
echo "请在另一个终端运行:"
echo "open /Users/<USER>/Desktop/stock/src-tauri/target/release/bundle/macos/XX交易终端.app"
echo
echo "或者运行完整测试:"
echo "./scripts/auto-test-build.sh"
echo
echo "按 Ctrl+C 停止监控并生成报告"

# 等待用户中断
while true; do
    sleep 1
done