#!/bin/bash
# 自动化应用测试脚本
# 用于测试打包后的 Tauri 应用功能，捕获所有日志并分析问题

set -e

# 颜色输出函数
print_header() { echo -e "\033[1;34m=== $1 ===\033[0m"; }
print_success() { echo -e "\033[1;32m✅ $1\033[0m"; }
print_error() { echo -e "\033[1;31m❌ $1\033[0m"; }
print_warning() { echo -e "\033[1;33m⚠️ $1\033[0m"; }
print_info() { echo -e "\033[1;36m📋 $1\033[0m"; }

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

LOG_DIR="$PROJECT_ROOT/logs"
TIMESTAMP=$(date "+%Y%m%d_%H%M%S")
APP_TEST_LOG="$LOG_DIR/app_test_$TIMESTAMP.log"
APP_OUTPUT_LOG="$LOG_DIR/app_output_$TIMESTAMP.log"
PYTHON_SERVICE_LOG="$LOG_DIR/python_service_$TIMESTAMP.log"

# 创建日志目录
mkdir -p "$LOG_DIR"

# 记录日志的函数
log_and_print() {
    echo "$1" | tee -a "$APP_TEST_LOG"
}

# 查找应用包
find_app_bundle() {
    local app_bundle=$(find src-tauri/target -name "*.app" -type d 2>/dev/null | head -1)
    if [ -n "$app_bundle" ]; then
        echo "$app_bundle"
    else
        print_error "未找到应用包，请先运行构建测试"
        exit 1
    fi
}

# 启动应用并捕获日志
start_app_with_logging() {
    local app_path="$1"
    local app_name=$(basename "$app_path" .app)
    local executable="$app_path/Contents/MacOS/$app_name"
    
    if [ ! -x "$executable" ]; then
        executable="$(find "$app_path/Contents/MacOS" -type f -executable | head -1)"
    fi
    
    if [ ! -x "$executable" ]; then
        print_error "未找到可执行文件在: $app_path/Contents/MacOS/"
        return 1
    fi
    
    log_and_print "启动应用: $executable"
    
    # 启动应用并重定向输出到日志文件
    "$executable" > "$APP_OUTPUT_LOG" 2>&1 &
    local app_pid=$!
    
    log_and_print "应用已启动，PID: $app_pid"
    echo $app_pid
}

# 等待应用启动
wait_for_app() {
    local max_wait=30
    local count=0
    
    print_info "等待应用启动..."
    
    while [ $count -lt $max_wait ]; do
        # 检查应用是否在运行
        if pgrep -f "XX交易终端" > /dev/null 2>&1; then
            print_success "应用启动成功"
            return 0
        fi
        
        sleep 1
        count=$((count + 1))
        echo -n "."
    done
    
    echo ""
    print_error "应用启动超时"
    return 1
}

# 测试服务连通性
test_service_connectivity() {
    local service_name="$1"
    local test_command="$2"
    
    print_info "测试 $service_name 服务连通性..."
    
    # 这里我们需要通过某种方式测试服务
    # 由于是桌面应用，我们主要通过日志来判断
    
    sleep 2  # 给服务一些启动时间
    
    # 检查应用输出日志中是否有错误
    if [ -f "$APP_OUTPUT_LOG" ]; then
        local errors=$(grep -i "error\|failed\|broken.*pipe\|connection.*refused" "$APP_OUTPUT_LOG" 2>/dev/null || true)
        if [ -n "$errors" ]; then
            print_error "$service_name 服务出现错误:"
            echo "$errors" | tee -a "$APP_TEST_LOG"
            return 1
        else
            print_success "$service_name 服务看起来正常"
            return 0
        fi
    else
        print_warning "未找到应用输出日志"
        return 1
    fi
}

# 监控系统日志
monitor_system_logs() {
    local duration="$1"
    
    print_info "监控系统日志 $duration 秒..."
    
    # 监控控制台日志（macOS）
    if command -v log >/dev/null 2>&1; then
        log stream --predicate 'process CONTAINS "XX交易终端" OR process CONTAINS "python" OR process CONTAINS "counter-service"' --style compact > "$LOG_DIR/system_log_$TIMESTAMP.log" 2>&1 &
        local log_pid=$!
        
        sleep "$duration"
        
        kill $log_pid 2>/dev/null || true
        
        if [ -f "$LOG_DIR/system_log_$TIMESTAMP.log" ]; then
            local log_lines=$(wc -l < "$LOG_DIR/system_log_$TIMESTAMP.log" 2>/dev/null || echo "0")
            log_and_print "系统日志监控完成，收集到 $log_lines 行日志"
            
            # 分析系统日志中的错误
            local errors=$(grep -i "error\|failed\|broken.*pipe\|connection.*refused\|permission.*denied" "$LOG_DIR/system_log_$TIMESTAMP.log" 2>/dev/null || true)
            if [ -n "$errors" ]; then
                print_warning "系统日志中发现错误:"
                echo "$errors" | head -10 | tee -a "$APP_TEST_LOG"
            fi
        fi
    else
        print_warning "log 命令不可用，跳过系统日志监控"
    fi
}

# 收集进程信息
collect_process_info() {
    print_info "收集进程信息..."
    
    {
        echo "=== 进程列表 ==="
        ps aux | grep -E "(XX交易终端|python|counter-service|futu-adapter|huasheng-adapter)" | grep -v grep
        
        echo ""
        echo "=== 网络连接 ==="
        netstat -an | grep -E "LISTEN|ESTABLISHED" | grep -E ":8000|:8001|:8002|:11111" || echo "未找到相关端口"
        
        echo ""
        echo "=== 打开的文件 ==="
        lsof -c "XX交易终端" 2>/dev/null | head -20 || echo "lsof 信息不可用"
        
    } >> "$LOG_DIR/process_info_$TIMESTAMP.log" 2>&1
    
    log_and_print "进程信息已保存到: $LOG_DIR/process_info_$TIMESTAMP.log"
}

# 执行功能测试
execute_functional_tests() {
    print_info "执行功能测试..."
    
    # 测试1: 检查服务启动
    test_service_connectivity "计数器服务" "counter"
    local counter_result=$?
    
    # 测试2: 检查适配器服务
    test_service_connectivity "富途适配器" "futu"
    local futu_result=$?
    
    test_service_connectivity "华盛适配器" "huasheng"
    local huasheng_result=$?
    
    # 统计结果
    local passed=0
    local total=3
    
    [ $counter_result -eq 0 ] && passed=$((passed + 1))
    [ $futu_result -eq 0 ] && passed=$((passed + 1))
    [ $huasheng_result -eq 0 ] && passed=$((passed + 1))
    
    log_and_print "功能测试结果: $passed/$total 通过"
    
    if [ $passed -eq $total ]; then
        return 0
    else
        return 1
    fi
}

# 清理函数
cleanup() {
    print_info "清理测试环境..."
    
    # 终止应用进程
    if [ -n "$APP_PID" ]; then
        kill "$APP_PID" 2>/dev/null || true
        sleep 2
        kill -9 "$APP_PID" 2>/dev/null || true
    fi
    
    # 清理其他可能的进程
    pkill -f "XX交易终端" 2>/dev/null || true
    pkill -f "counter-service" 2>/dev/null || true
    pkill -f "futu-adapter" 2>/dev/null || true
    pkill -f "huasheng-adapter" 2>/dev/null || true
    
    print_info "清理完成"
}

# 设置退出时清理
trap cleanup EXIT

# 开始测试
print_header "开始自动化应用测试 - $(date)"
log_and_print "应用测试开始时间: $(date)"
log_and_print "项目目录: $PROJECT_ROOT"

# 第一步：查找应用包
print_header "第一步：查找应用包"
APP_BUNDLE=$(find_app_bundle)
log_and_print "找到应用包: $APP_BUNDLE"

# 第二步：启动应用
print_header "第二步：启动应用"
APP_PID=$(start_app_with_logging "$APP_BUNDLE")

if [ -z "$APP_PID" ]; then
    print_error "应用启动失败"
    exit 1
fi

# 第三步：等待应用启动
print_header "第三步：等待应用启动"
if ! wait_for_app; then
    print_error "应用启动失败，检查日志"
    
    if [ -f "$APP_OUTPUT_LOG" ]; then
        print_info "应用输出日志:"
        tail -20 "$APP_OUTPUT_LOG" | tee -a "$APP_TEST_LOG"
    fi
    
    exit 1
fi

# 第四步：收集初始信息
print_header "第四步：收集初始进程信息"
collect_process_info

# 第五步：监控系统日志
print_header "第五步：监控系统日志"
monitor_system_logs 10 &

# 第六步：执行功能测试
print_header "第六步：执行功能测试"
FUNCTIONAL_TESTS_RESULT=0
if ! execute_functional_tests; then
    FUNCTIONAL_TESTS_RESULT=1
fi

# 第七步：等待日志监控完成
print_header "第七步：完成日志收集"
wait  # 等待后台的监控任务完成

# 第八步：分析所有日志
print_header "第八步：分析测试结果"
{
    print_info "应用输出日志分析:"
    if [ -f "$APP_OUTPUT_LOG" ]; then
        local app_lines=$(wc -l < "$APP_OUTPUT_LOG" 2>/dev/null || echo "0")
        log_and_print "应用输出日志行数: $app_lines"
        
        if [ "$app_lines" -gt 0 ]; then
            log_and_print "应用输出日志内容（最后20行）:"
            tail -20 "$APP_OUTPUT_LOG" | tee -a "$APP_TEST_LOG"
            
            # 分析关键错误
            local critical_errors=$(grep -i "broken.*pipe\|connection.*refused\|permission.*denied\|failed.*to.*start" "$APP_OUTPUT_LOG" 2>/dev/null || true)
            if [ -n "$critical_errors" ]; then
                print_error "发现关键错误:"
                echo "$critical_errors" | tee -a "$APP_TEST_LOG"
            fi
        else
            print_warning "应用输出日志为空"
        fi
    else
        print_warning "未找到应用输出日志文件"
    fi
}

# 生成最终报告
print_header "应用测试报告"
{
    print_success "应用测试完成！"
    log_and_print "应用测试完成时间: $(date)"
    
    echo ""
    print_info "测试结果汇总:"
    log_and_print "📱 应用启动: $([ -n "$APP_PID" ] && echo "成功" || echo "失败")"
    log_and_print "🔧 功能测试: $([ $FUNCTIONAL_TESTS_RESULT -eq 0 ] && echo "通过" || echo "失败")"
    
    echo ""
    print_info "日志文件位置:"
    log_and_print "测试日志: $APP_TEST_LOG"
    log_and_print "应用输出: $APP_OUTPUT_LOG"
    log_and_print "系统日志: $LOG_DIR/system_log_$TIMESTAMP.log"
    log_and_print "进程信息: $LOG_DIR/process_info_$TIMESTAMP.log"
    
    echo ""
    if [ $FUNCTIONAL_TESTS_RESULT -eq 0 ]; then
        print_success "测试总体结果: 通过"
    else
        print_error "测试总体结果: 失败"
        print_info "请查看日志文件以获取详细信息"
    fi
}