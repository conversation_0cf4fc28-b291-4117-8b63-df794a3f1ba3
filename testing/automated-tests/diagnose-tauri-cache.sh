#!/bin/bash

# Tauri 缓存诊断脚本  
# 专门找出为什么 Tauri 使用旧版本脚本的原因

set -e

echo "🔍 Tauri 缓存诊断脚本"
echo "==================="

PROJECT_ROOT="$(cd "$(dirname "$0")/.." && pwd)"
cd "$PROJECT_ROOT"

DIAG_LOG="$HOME/Desktop/tauri_cache_diagnosis_$(date +%Y%m%d_%H%M%S).log"
echo "📝 诊断日志: $DIAG_LOG"

# 函数：检查文件时间戳
check_timestamps() {
    echo "🕐 检查文件时间戳" | tee -a "$DIAG_LOG"
    echo "==================" | tee -a "$DIAG_LOG"
    
    echo "src-tauri/bin/ 目录:" >> "$DIAG_LOG"
    if [ -d "src-tauri/bin" ]; then
        ls -la src-tauri/bin/ >> "$DIAG_LOG" 2>&1
        echo "最新修改时间:" >> "$DIAG_LOG"
        find src-tauri/bin -type f -name "*counter*" -exec stat -f "%Sm %N" {} \; >> "$DIAG_LOG" 2>&1
    else
        echo "目录不存在" >> "$DIAG_LOG"
    fi
    
    echo "" >> "$DIAG_LOG"
    echo "tauri.conf.json 修改时间:" >> "$DIAG_LOG"
    stat -f "%Sm %N" src-tauri/tauri.conf.json >> "$DIAG_LOG" 2>&1
    
    echo "" >> "$DIAG_LOG"
    echo "构建脚本修改时间:" >> "$DIAG_LOG"
    stat -f "%Sm %N" scripts/build-python-binaries.sh >> "$DIAG_LOG" 2>&1
}

# 函数：检查 Tauri 配置
check_tauri_config() {
    echo "⚙️ 检查 Tauri 配置" | tee -a "$DIAG_LOG"
    echo "==================" | tee -a "$DIAG_LOG"
    
    echo "tauri.conf.json 中的 externalBin 配置:" >> "$DIAG_LOG"
    grep -A 5 -B 2 "externalBin" src-tauri/tauri.conf.json >> "$DIAG_LOG" 2>&1
    
    echo "" >> "$DIAG_LOG"
    echo "完整的 bundle 配置:" >> "$DIAG_LOG"
    grep -A 20 '"bundle"' src-tauri/tauri.conf.json >> "$DIAG_LOG" 2>&1
}

# 函数：查找所有可能的缓存位置
find_cache_locations() {
    echo "🗂️ 查找缓存位置" | tee -a "$DIAG_LOG" 
    echo "=================" | tee -a "$DIAG_LOG"
    
    # Tauri 目标目录
    echo "src-tauri/target/ 结构:" >> "$DIAG_LOG"
    if [ -d "src-tauri/target" ]; then
        find src-tauri/target -type f -name "*counter*" >> "$DIAG_LOG" 2>&1
    else
        echo "target 目录不存在" >> "$DIAG_LOG"
    fi
    
    # 系统缓存目录
    echo "" >> "$DIAG_LOG"
    echo "系统缓存目录搜索:" >> "$DIAG_LOG"
    
    # 搜索用户库目录
    find ~/Library -name "*tauri*" -type d 2>/dev/null >> "$DIAG_LOG" || true
    find ~/Library -name "*counter-service*" -type f 2>/dev/null >> "$DIAG_LOG" || true
    
    # 搜索临时目录
    find /tmp -name "*tauri*" -type d 2>/dev/null >> "$DIAG_LOG" || true
    find /tmp -name "*counter-service*" -type f 2>/dev/null >> "$DIAG_LOG" || true
}

# 函数：创建构建监控
monitor_build_process() {
    echo "📺 监控构建过程" | tee -a "$DIAG_LOG"
    echo "================" | tee -a "$DIAG_LOG"
    
    # 创建监控脚本
    cat > /tmp/monitor_build.sh << 'EOF'
#!/bin/bash
MONITOR_LOG="$1"

echo "开始监控构建过程..." >> "$MONITOR_LOG"

# 监控文件变化
fswatch -o . | while read num; do
    echo "$(date): 检测到文件变化" >> "$MONITOR_LOG"
    echo "当前 bin 目录内容:" >> "$MONITOR_LOG"
    ls -la src-tauri/bin/ >> "$MONITOR_LOG" 2>&1 || echo "bin 目录不存在" >> "$MONITOR_LOG"
    echo "---" >> "$MONITOR_LOG"
done &

MONITOR_PID=$!
echo $MONITOR_PID
EOF
    
    chmod +x /tmp/monitor_build.sh
    echo "构建监控脚本已创建: /tmp/monitor_build.sh" >> "$DIAG_LOG"
}

# 函数：比较脚本版本
compare_scripts() {
    echo "📋 比较脚本版本" | tee -a "$DIAG_LOG"
    echo "================" | tee -a "$DIAG_LOG"
    
    LOCAL_SCRIPT="src-tauri/bin/counter-service"
    
    if [ -f "$LOCAL_SCRIPT" ]; then
        echo "本地脚本内容:" >> "$DIAG_LOG"
        echo "--- 开始 ---" >> "$DIAG_LOG"
        cat "$LOCAL_SCRIPT" >> "$DIAG_LOG"
        echo "--- 结束 ---" >> "$DIAG_LOG"
        echo "" >> "$DIAG_LOG"
        
        echo "本地脚本特征:" >> "$DIAG_LOG"
        if grep -q "*/Contents/MacOS" "$LOCAL_SCRIPT"; then
            echo "✅ 使用新版本路径检测" >> "$DIAG_LOG"
        else
            echo "❌ 使用旧版本路径检测" >> "$DIAG_LOG"
        fi
        
        if grep -q "macOS 应用包环境" "$LOCAL_SCRIPT"; then
            echo "✅ 包含环境检测逻辑" >> "$DIAG_LOG"
        else
            echo "❌ 缺少环境检测逻辑" >> "$DIAG_LOG"
        fi
    else
        echo "❌ 本地脚本不存在" >> "$DIAG_LOG"
    fi
    
    # 检查打包后的脚本（如果存在）
    PACKAGED_SCRIPT="src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/MacOS/counter-service"
    if [ -f "$PACKAGED_SCRIPT" ]; then
        echo "" >> "$DIAG_LOG"
        echo "打包后脚本内容:" >> "$DIAG_LOG"
        echo "--- 开始 ---" >> "$DIAG_LOG"
        cat "$PACKAGED_SCRIPT" >> "$DIAG_LOG"
        echo "--- 结束 ---" >> "$DIAG_LOG"
        echo "" >> "$DIAG_LOG"
        
        echo "打包后脚本特征:" >> "$DIAG_LOG"
        if grep -q "*/Contents/MacOS" "$PACKAGED_SCRIPT"; then
            echo "✅ 使用新版本路径检测" >> "$DIAG_LOG"
        else
            echo "❌ 使用旧版本路径检测" >> "$DIAG_LOG"
        fi
        
        # 比较差异
        echo "" >> "$DIAG_LOG"
        echo "脚本差异分析:" >> "$DIAG_LOG"
        if cmp -s "$LOCAL_SCRIPT" "$PACKAGED_SCRIPT"; then
            echo "✅ 本地脚本和打包后脚本一致" >> "$DIAG_LOG"
        else
            echo "❌ 本地脚本和打包后脚本不一致" >> "$DIAG_LOG"
            echo "详细差异:" >> "$DIAG_LOG"
            diff "$LOCAL_SCRIPT" "$PACKAGED_SCRIPT" >> "$DIAG_LOG" 2>&1 || true
        fi
    else
        echo "打包后脚本不存在" >> "$DIAG_LOG"
    fi
}

# 函数：生成修复建议
generate_fix_suggestions() {
    echo "" >> "$DIAG_LOG"
    echo "🔧 修复建议" >> "$DIAG_LOG"
    echo "==========" >> "$DIAG_LOG"
    
    # 检查是否是缓存问题
    LOCAL_SCRIPT="src-tauri/bin/counter-service"
    PACKAGED_SCRIPT="src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/MacOS/counter-service"
    
    if [ -f "$LOCAL_SCRIPT" ] && [ -f "$PACKAGED_SCRIPT" ]; then
        if ! cmp -s "$LOCAL_SCRIPT" "$PACKAGED_SCRIPT"; then
            echo "🎯 问题确认：Tauri 使用了缓存的旧脚本" >> "$DIAG_LOG"
            echo "" >> "$DIAG_LOG"
            echo "建议修复方案：" >> "$DIAG_LOG"
            echo "1. 立即解决：" >> "$DIAG_LOG"
            echo "   cp \"$LOCAL_SCRIPT\" \"$PACKAGED_SCRIPT\"" >> "$DIAG_LOG"
            echo "" >> "$DIAG_LOG"
            echo "2. 根本解决：" >> "$DIAG_LOG"
            echo "   - 检查 tauri.conf.json 中的 externalBin 配置时间戳" >> "$DIAG_LOG"
            echo "   - 清理所有可能的缓存目录" >> "$DIAG_LOG"
            echo "   - 使用 --no-bundle 重新构建测试" >> "$DIAG_LOG"
            echo "" >> "$DIAG_LOG"
            echo "3. 验证解决：" >> "$DIAG_LOG"
            echo "   - 比较构建前后的脚本时间戳" >> "$DIAG_LOG"
            echo "   - 监控构建过程中的文件操作" >> "$DIAG_LOG"
        fi
    fi
    
    echo "" >> "$DIAG_LOG"
    echo "立即修复脚本:" >> "$DIAG_LOG"
    cat > /tmp/fix_packaged_script.sh << EOF
#!/bin/bash
# 立即修复打包后的脚本

LOCAL_SCRIPT="$LOCAL_SCRIPT"
PACKAGED_SCRIPT="$PACKAGED_SCRIPT"

if [ -f "\$LOCAL_SCRIPT" ] && [ -f "\$PACKAGED_SCRIPT" ]; then
    echo "备份旧脚本..."
    cp "\$PACKAGED_SCRIPT" "\$PACKAGED_SCRIPT.backup"
    
    echo "复制新脚本..."
    cp "\$LOCAL_SCRIPT" "\$PACKAGED_SCRIPT"
    
    echo "验证修复..."
    if cmp -s "\$LOCAL_SCRIPT" "\$PACKAGED_SCRIPT"; then
        echo "✅ 脚本修复成功"
    else
        echo "❌ 脚本修复失败"
    fi
else
    echo "❌ 无法找到必要的脚本文件"
fi
EOF
    chmod +x /tmp/fix_packaged_script.sh
    echo "修复脚本已创建: /tmp/fix_packaged_script.sh" >> "$DIAG_LOG"
}

# 主执行流程
echo "开始诊断..." | tee -a "$DIAG_LOG"

check_timestamps
check_tauri_config  
find_cache_locations
compare_scripts
generate_fix_suggestions

echo "" | tee -a "$DIAG_LOG"
echo "🎉 诊断完成！" | tee -a "$DIAG_LOG"
echo "详细结果请查看: $DIAG_LOG" | tee -a "$DIAG_LOG"

# 显示摘要
echo ""
echo "📊 诊断摘要："
echo "============"
if [ -f "src-tauri/bin/counter-service" ]; then
    if grep -q "*/Contents/MacOS" "src-tauri/bin/counter-service"; then
        echo "✅ 本地脚本版本正确"
    else
        echo "❌ 本地脚本版本错误"
    fi
else
    echo "❌ 本地脚本不存在"
fi

PACKAGED_SCRIPT="src-tauri/target/release/bundle/macos/XX交易终端.app/Contents/MacOS/counter-service"
if [ -f "$PACKAGED_SCRIPT" ]; then
    if grep -q "*/Contents/MacOS" "$PACKAGED_SCRIPT"; then
        echo "✅ 打包后脚本版本正确"
    else  
        echo "❌ 打包后脚本版本错误 - 这是问题根源！"
        echo "🔧 立即修复: /tmp/fix_packaged_script.sh"
    fi
else
    echo "ℹ️ 打包后脚本不存在（尚未构建）"
fi

echo ""
echo "完整诊断报告: $DIAG_LOG"