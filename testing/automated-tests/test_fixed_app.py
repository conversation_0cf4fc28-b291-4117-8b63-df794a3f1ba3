#!/usr/bin/env python3
"""
修复后应用测试脚本
用于验证修复后的打包应用是否能正常运行
"""

import os
import subprocess
import time
import signal
import sys
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/tmp/fixed_app_test.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class FixedAppTester:
    def __init__(self, app_path):
        self.app_path = app_path
        
    def test_individual_services(self):
        """单独测试Python服务（修复后）"""
        logger.info("=== 测试修复后的Python服务 ===")
        
        services = [
            "counter-service",
            "futu-adapter", 
            "huasheng-adapter"
        ]
        
        for service_name in services:
            service_path = os.path.join(self.app_path, "Contents", "MacOS", service_name)
            
            if not os.path.exists(service_path):
                logger.error(f"服务不存在: {service_path}")
                continue
                
            logger.info(f"测试修复后的服务: {service_name}")
            
            try:
                # 尝试启动服务并快速停止
                process = subprocess.Popen(
                    [service_path],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    env=dict(os.environ)  # 传递当前环境变量
                )
                
                # 等待2秒看是否有输出
                time.sleep(2)
                
                # 发送SIGTERM信号停止服务
                process.terminate()
                stdout, stderr = process.communicate(timeout=5)
                
                logger.info(f"  {service_name} 输出:")
                if stdout:
                    for line in stdout.split('\n')[:10]:  # 只显示前10行
                        if line.strip():
                            logger.info(f"    STDOUT: {line}")
                
                if stderr:
                    for line in stderr.split('\n')[:10]:  # 只显示前10行
                        if line.strip():
                            logger.info(f"    STDERR: {line}")
                            
                logger.info(f"  {service_name} 返回码: {process.returncode}")
                
            except subprocess.TimeoutExpired:
                logger.warning(f"  {service_name} 停止超时，强制终止")
                process.kill()
            except Exception as e:
                logger.error(f"  {service_name} 测试失败: {e}")
                
    def run_app_test(self):
        """运行修复后的应用测试"""
        logger.info("开始测试修复后的打包应用")
        logger.info("=" * 60)
        logger.info(f"测试时间: {datetime.now()}")
        logger.info(f"应用路径: {self.app_path}")
        
        # 测试各个Python服务
        self.test_individual_services()
        
        # 尝试短暂启动完整应用
        logger.info("=== 尝试短暂启动完整应用 ===")
        try:
            cmd = ['open', '-W', '-n', self.app_path]  # -n 参数强制新实例
            logger.info(f"执行命令: {' '.join(cmd)}")
            
            process = subprocess.Popen(cmd)
            logger.info(f"应用进程启动，PID: {process.pid}")
            
            # 等待5秒让应用初始化
            time.sleep(5)
            
            # 检查应用是否还在运行
            if process.poll() is None:
                logger.info("应用正在运行中，测试成功！")
                
                # 优雅关闭应用
                logger.info("正在关闭应用...")
                process.terminate()
                process.wait(timeout=10)
                logger.info("应用已关闭")
            else:
                logger.error(f"应用启动后立即退出，返回码: {process.returncode}")
                
        except Exception as e:
            logger.error(f"应用启动测试失败: {e}")

def main():
    app_path = "/Users/<USER>/Desktop/stock/src-tauri/target/release/bundle/macos/XX交易终端.app"
    
    tester = FixedAppTester(app_path)
    tester.run_app_test()
    
    logger.info("修复后应用测试完成")

if __name__ == "__main__":
    main()