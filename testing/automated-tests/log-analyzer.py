#!/usr/bin/env python3
"""
日志分析工具
用于分析 Tauri 应用的各种日志文件，识别问题模式并提供修复建议
"""

import os
import re
import sys
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass


@dataclass
class LogEntry:
    """日志条目"""
    timestamp: str
    level: str
    source: str
    message: str
    raw_line: str


@dataclass
class Issue:
    """问题条目"""
    category: str
    severity: str  # critical, high, medium, low
    description: str
    solution: str
    occurrences: int
    examples: List[str]


class LogAnalyzer:
    """日志分析器"""
    
    # 错误模式定义
    ERROR_PATTERNS = {
        'broken_pipe': {
            'pattern': r'broken.*pipe|pipe.*broken|EPIPE',
            'category': 'communication',
            'severity': 'critical',
            'description': 'Broken pipe 错误，通常表示进程间通信中断',
            'solution': '检查子进程是否正常启动，验证端口绑定，检查防火墙设置'
        },
        'connection_refused': {
            'pattern': r'connection.*refused|refused.*connection',
            'category': 'network',
            'severity': 'critical',
            'description': '连接被拒绝，服务可能未启动或端口不可用',
            'solution': '确认目标服务已启动，检查端口号是否正确，验证防火墙规则'
        },
        'permission_denied': {
            'pattern': r'permission.*denied|denied.*permission|EACCES',
            'category': 'permission',
            'severity': 'critical',
            'description': '权限被拒绝，可能是文件或网络权限问题',
            'solution': '检查文件权限，确认应用具有必要的网络访问权限'
        },
        'address_in_use': {
            'pattern': r'address.*already.*in.*use|EADDRINUSE',
            'category': 'network',
            'severity': 'high',
            'description': '端口已被占用',
            'solution': '检查端口占用情况，终止占用端口的进程或更换端口'
        },
        'python_import_error': {
            'pattern': r'import.*error|no.*module.*named|modulenotfounderror',
            'category': 'dependency',
            'severity': 'high',
            'description': 'Python 模块导入失败',
            'solution': '检查 Python 环境，确认所需模块已安装，验证 PYTHONPATH 设置'
        },
        'file_not_found': {
            'pattern': r'file.*not.*found|no.*such.*file|ENOENT',
            'category': 'filesystem',
            'severity': 'medium',
            'description': '文件或目录不存在',
            'solution': '检查文件路径是否正确，确认文件存在且可访问'
        },
        'timeout_error': {
            'pattern': r'timeout|timed.*out',
            'category': 'performance',
            'severity': 'medium',
            'description': '操作超时',
            'solution': '增加超时时间，检查网络连接，优化性能瓶颈'
        }
    }
    
    def __init__(self, log_dir: Path):
        self.log_dir = Path(log_dir)
        self.issues: Dict[str, Issue] = {}
        
    def parse_log_line(self, line: str, source: str) -> Optional[LogEntry]:
        """解析日志行"""
        line = line.strip()
        if not line:
            return None
            
        # 尝试解析时间戳
        timestamp_match = re.match(r'(\d{4}-\d{2}-\d{2}[T\s]\d{2}:\d{2}:\d{2})', line)
        timestamp = timestamp_match.group(1) if timestamp_match else datetime.now().isoformat()
        
        # 尝试解析日志级别
        level_match = re.search(r'\b(DEBUG|INFO|WARN|WARNING|ERROR|CRITICAL|FATAL)\b', line, re.IGNORECASE)
        level = level_match.group(1).upper() if level_match else 'UNKNOWN'
        
        return LogEntry(
            timestamp=timestamp,
            level=level,
            source=source,
            message=line,
            raw_line=line
        )
    
    def analyze_log_file(self, log_file: Path) -> List[LogEntry]:
        """分析单个日志文件"""
        if not log_file.exists():
            return []
            
        entries = []
        source = log_file.stem
        
        try:
            with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                for line in f:
                    entry = self.parse_log_line(line, source)
                    if entry:
                        entries.append(entry)
                        self._check_for_issues(entry)
        except Exception as e:
            print(f"警告: 无法读取日志文件 {log_file}: {e}")
            
        return entries
    
    def _check_for_issues(self, entry: LogEntry):
        """检查日志条目中的问题"""
        message_lower = entry.message.lower()
        
        for issue_key, pattern_info in self.ERROR_PATTERNS.items():
            if re.search(pattern_info['pattern'], message_lower, re.IGNORECASE):
                if issue_key in self.issues:
                    self.issues[issue_key].occurrences += 1
                    if len(self.issues[issue_key].examples) < 5:  # 最多保存5个例子
                        self.issues[issue_key].examples.append(entry.raw_line)
                else:
                    self.issues[issue_key] = Issue(
                        category=pattern_info['category'],
                        severity=pattern_info['severity'],
                        description=pattern_info['description'],
                        solution=pattern_info['solution'],
                        occurrences=1,
                        examples=[entry.raw_line]
                    )
    
    def analyze_all_logs(self) -> Dict[str, List[LogEntry]]:
        """分析所有日志文件"""
        all_logs = {}
        
        if not self.log_dir.exists():
            print(f"日志目录不存在: {self.log_dir}")
            return all_logs
        
        log_files = list(self.log_dir.glob("*.log"))
        if not log_files:
            print(f"日志目录中未找到日志文件: {self.log_dir}")
            return all_logs
        
        for log_file in sorted(log_files):
            print(f"分析日志文件: {log_file.name}")
            entries = self.analyze_log_file(log_file)
            if entries:
                all_logs[log_file.name] = entries
        
        return all_logs
    
    def generate_report(self, output_file: Optional[Path] = None) -> str:
        """生成分析报告"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        report_lines = [
            "=" * 80,
            f"📊 日志分析报告 - {timestamp}",
            "=" * 80,
            "",
            f"🗂️  分析目录: {self.log_dir}",
            f"🔍 分析的日志文件数: {len(list(self.log_dir.glob('*.log'))) if self.log_dir.exists() else 0}",
            f"⚠️  发现的问题类型数: {len(self.issues)}",
            ""
        ]
        
        if not self.issues:
            report_lines.extend([
                "🎉 未发现明显的问题模式！",
                "",
                "这表明应用可能运行正常，或者问题不在预定义的错误模式中。",
                "建议手动检查日志文件以获取更多信息。",
                ""
            ])
        else:
            # 按严重程度排序问题
            sorted_issues = sorted(
                self.issues.items(),
                key=lambda x: {'critical': 0, 'high': 1, 'medium': 2, 'low': 3}.get(x[1].severity, 4)
            )
            
            report_lines.append("🚨 发现的问题:")
            report_lines.append("")
            
            for issue_key, issue in sorted_issues:
                severity_emoji = {
                    'critical': '🔴',
                    'high': '🟠',
                    'medium': '🟡',
                    'low': '🟢'
                }.get(issue.severity, '⚪')
                
                report_lines.extend([
                    f"{severity_emoji} **{issue.category.upper()}** - {issue_key}",
                    f"   严重程度: {issue.severity}",
                    f"   出现次数: {issue.occurrences}",
                    f"   描述: {issue.description}",
                    f"   建议解决方案: {issue.solution}",
                    ""
                ])
                
                if issue.examples:
                    report_lines.append("   📝 错误示例:")
                    for i, example in enumerate(issue.examples[:3], 1):  # 只显示前3个例子
                        report_lines.append(f"      {i}. {example[:100]}{'...' if len(example) > 100 else ''}")
                    report_lines.append("")
        
        # 添加诊断建议
        report_lines.extend([
            "-" * 80,
            "🔧 通用诊断建议:",
            "",
            "1. **检查构建过程**:",
            "   - 确保所有 Python 依赖都已正确安装",
            "   - 验证二进制文件是否正确生成",
            "   - 检查 Tauri 配置文件中的 sidecar 设置",
            "",
            "2. **检查运行环境**:",
            "   - 确认必要的端口没有被占用",
            "   - 验证文件权限设置",
            "   - 检查防火墙和安全软件设置",
            "",
            "3. **调试步骤**:",
            "   - 尝试手动运行 Python 服务脚本",
            "   - 使用网络工具检查端口连接",
            "   - 查看系统控制台日志获取更多信息",
            "",
            "4. **如果问题持续**:",
            "   - 在开发模式下测试功能是否正常",
            "   - 比较开发模式和生产模式的配置差异",
            "   - 考虑使用不同的进程通信方式",
            ""
        ])
        
        report_content = "\n".join(report_lines)
        
        if output_file:
            try:
                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write(report_content)
                print(f"📄 分析报告已保存到: {output_file}")
            except Exception as e:
                print(f"❌ 保存报告失败: {e}")
        
        return report_content
    
    def suggest_fixes(self) -> List[str]:
        """根据发现的问题提供具体的修复建议"""
        fixes = []
        
        if 'broken_pipe' in self.issues:
            fixes.extend([
                "针对 Broken Pipe 问题:",
                "1. 在 Rust 代码中添加更好的错误处理和重试机制",
                "2. 确保 Python 服务在 Tauri 应用启动前已完全初始化",
                "3. 检查 tauri.conf.json 中的 sidecar 配置",
                "4. 考虑添加健康检查机制"
            ])
        
        if 'connection_refused' in self.issues:
            fixes.extend([
                "针对连接拒绝问题:",
                "1. 验证服务监听的端口和地址",
                "2. 检查服务启动顺序",
                "3. 添加连接重试逻辑",
                "4. 确认防火墙规则允许本地连接"
            ])
        
        if 'permission_denied' in self.issues:
            fixes.extend([
                "针对权限问题:",
                "1. 检查应用程序签名和权限配置",
                "2. 在 macOS 上检查安全与隐私设置",
                "3. 确认二进制文件具有执行权限",
                "4. 检查网络访问权限"
            ])
        
        return fixes


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="分析 Tauri 应用日志文件")
    parser.add_argument("--log-dir", "-d", 
                       default="logs", 
                       help="日志文件目录 (默认: logs)")
    parser.add_argument("--output", "-o", 
                       help="输出报告文件路径")
    parser.add_argument("--show-fixes", "-f", 
                       action="store_true",
                       help="显示修复建议")
    
    args = parser.parse_args()
    
    # 确定日志目录路径
    if os.path.isabs(args.log_dir):
        log_dir = Path(args.log_dir)
    else:
        # 相对于项目根目录
        project_root = Path(__file__).parent.parent
        log_dir = project_root / args.log_dir
    
    print(f"🔍 开始分析日志目录: {log_dir}")
    
    analyzer = LogAnalyzer(log_dir)
    all_logs = analyzer.analyze_all_logs()
    
    # 生成并显示报告
    output_file = Path(args.output) if args.output else None
    report = analyzer.generate_report(output_file)
    
    print(report)
    
    # 显示修复建议
    if args.show_fixes:
        fixes = analyzer.suggest_fixes()
        if fixes:
            print("\n" + "=" * 80)
            print("🔧 具体修复建议:")
            print("=" * 80)
            for fix in fixes:
                print(fix)
        else:
            print("\n✅ 未找到需要特定修复的问题")
    
    # 返回退出码
    critical_issues = sum(1 for issue in analyzer.issues.values() if issue.severity == 'critical')
    return 1 if critical_issues > 0 else 0


if __name__ == "__main__":
    sys.exit(main())