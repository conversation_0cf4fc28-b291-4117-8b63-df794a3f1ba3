# 打包后应用通信测试使用指南

## 概述

`test_packaged_app.py` 是一个专门用于测试打包后 Tauri 应用与 Python 服务通信功能的脚本。它可以验证应用是否正确打包，Python 服务是否能正常启动和响应。

## 功能特性

### 🔍 应用结构完整性检查
- 验证应用主目录和必要文件是否存在
- 检查 Python 服务二进制文件的可执行性
- 确认资源文件的完整性

### 🔗 服务通信测试
- 测试 counter-service 的启动和关闭
- 验证命令响应机制（ping, get_counter, reset_counter）
- 检查实时数据推送功能
- 验证 JSON 消息的格式正确性

### ⚠️ 错误处理验证
- 测试对无效命令的处理
- 检查对格式错误 JSON 的容错性
- 验证服务在错误情况下的稳定性

### 📊 详细测试报告
- 生成完整的测试通过率报告
- 提供失败原因的详细分析
- 保存带时间戳的详细日志文件

## 使用方法

### 基本用法

```bash
# 使用默认应用路径测试
cd /Users/<USER>/Desktop/stock
./testing/test_packaged_app.py

# 指定自定义应用路径
./testing/test_packaged_app.py /path/to/your/app.app
```

### 默认应用路径

脚本默认使用以下路径：
```
/Users/<USER>/Desktop/stock/src-tauri/target/release/bundle/macos/XX交易终端.app
```

### 前置条件

1. **构建完成的应用**: 确保已成功执行 Tauri 构建
   ```bash
   cd src-tauri
   cargo tauri build
   ```

2. **Python 服务二进制**: 确保 Python 服务已正确打包到应用中
   ```bash
   # 检查服务文件是否存在
   ls -la target/release/bundle/macos/XX交易终端.app/Contents/MacOS/counter-service
   ```

## 测试输出示例

### 成功案例
```
==========================================
开始测试: 应用结构完整性检查
==========================================
测试结果: 应用结构完整性检查 - PASS
详细信息: 所有必需文件都存在且可执行
----------------------------------------

==========================================
开始测试: Counter服务通信测试
==========================================
启动counter-service进程...
Counter服务已启动，进程ID: 12345
发送命令: ping
收到响应: {'type': 'response', 'command_id': 'ping_test', 'success': True, 'data': {'status': 'alive'}}
测试结果: Counter服务通信测试 - PASS
详细信息: 成功接收到3个推送数据，计数器正确递增
```

### 失败案例
```
==========================================
开始测试: 应用结构完整性检查
==========================================
测试结果: 应用结构完整性检查 - FAIL
详细信息: 缺失的服务: ['counter-service']
----------------------------------------
```

## 测试报告

### 汇总报告
```
============================================================
测试报告汇总
============================================================
总测试数: 4
通过测试: 4
失败测试: 0
通过率: 100.0%

详细结果:
  应用结构完整性检查: PASS
  Counter服务通信测试: PASS
  服务启动序列测试: PASS
  错误处理能力测试: PASS

详细日志已保存到: /tmp/test_packaged_app_20250722_191235.log
```

### 日志文件

详细的执行日志会保存到 `/tmp/test_packaged_app_YYYYMMDD_HHMMSS.log`，包含：
- 每个测试步骤的详细执行过程
- 服务启动和关闭的完整日志
- 命令和响应的原始数据
- 错误和异常的完整堆栈跟踪

## 故障排除

### 常见问题

1. **应用路径不存在**
   ```
   错误: 应用路径不存在: /path/to/app.app
   解决: 先执行构建命令 'cargo tauri build'
   ```

2. **服务文件缺失**
   ```
   错误: 缺失的服务: ['counter-service']
   解决: 检查 tauri.conf.json 中的 externalBin 配置
   ```

3. **服务无法启动**
   ```
   错误: 服务启动失败: 退出码 1
   解决: 检查 Python 环境和依赖是否正确打包
   ```

4. **通信超时**
   ```
   错误: 命令 ping 超时无响应
   解决: 检查服务的标准输入输出是否正常
   ```

### 调试技巧

1. **查看详细日志**
   ```bash
   tail -f /tmp/test_packaged_app_*.log
   ```

2. **手动测试服务**
   ```bash
   # 直接运行服务检查错误
   /path/to/app.app/Contents/MacOS/counter-service
   ```

3. **检查系统日志**
   ```bash
   log show --last 5m --predicate 'process CONTAINS "counter"'
   ```

## 扩展和定制

### 添加新的测试用例

1. 在 `PackagedAppTester` 类中添加新的测试方法
2. 方法名以 `test_` 开头
3. 使用 `self.log_test_start()` 和 `self.log_test_result()` 记录结果
4. 在 `run_all_tests()` 方法的 `tests` 列表中添加新方法

### 测试其他服务

修改 `services` 字典添加新服务：
```python
self.services = {
    'counter-service': os.path.join(app_path, "Contents", "MacOS", "counter-service"),
    'your-service': os.path.join(app_path, "Contents", "MacOS", "your-service"),
}
```

### 自定义测试超时

修改相关方法中的 `timeout` 参数：
```python
response = self._read_response_with_timeout(process, timeout=10)  # 10秒超时
```

## 与其他测试工具的集成

这个脚本可以与项目中的其他测试工具配合使用：

1. **与构建测试集成**: 在 `auto-test-build.sh` 中调用此脚本
2. **与监控工具配合**: 结合 `monitor-app-logs.sh` 进行实时日志监控
3. **作为 CI/CD 的一部分**: 在自动化流水线中使用此脚本验证构建结果

## 注意事项

- 此脚本目前主要支持 macOS 平台的 `.app` 包
- 测试过程中会临时启动服务进程，确保端口不被占用
- 某些测试需要一定时间完成，请耐心等待
- 建议在独立环境中运行测试，避免干扰正在运行的服务

## 维护和更新

此脚本由 Claude Code 创建，会根据项目需求持续更新。如需修改或扩展功能，请参考上述扩展指南。