# Tauri 应用自动化测试和调试流程

这套自动化测试工具旨在彻底解决 Tauri 应用中的 "Broken pipe" 和其他常见问题。通过全面的构建验证、应用测试和日志分析，帮助快速定位并解决问题。

## 🚀 快速开始

### 完整流程 (推荐首次使用)
```bash
# 完整清理并执行所有测试步骤
./scripts/master-test.sh --clean --full-flow
```

### 分步执行
```bash
# 1. 仅构建测试
./scripts/master-test.sh --build-only

# 2. 仅应用测试 (需要先构建)
./scripts/master-test.sh --test-only

# 3. 仅日志分析
./scripts/master-test.sh --analyze-only
```

## 📋 脚本说明

### 1. 主控制脚本 (`master-test.sh`)
整合所有测试流程的主入口，支持多种执行模式。

**主要功能**:
- 协调所有测试步骤的执行
- 提供灵活的命令行选项
- 生成综合测试报告
- 自动清理测试环境

**使用方法**:
```bash
./scripts/master-test.sh [选项]

选项:
  -h, --help              显示帮助信息
  -b, --build-only        仅执行构建测试
  -t, --test-only         仅执行应用测试
  -a, --analyze-only      仅执行日志分析
  -f, --full-flow         执行完整流程 (默认)
  -c, --clean             完整清理后再开始
  -s, --skip-build        跳过构建步骤
  --no-interaction        非交互模式
```

### 2. 构建测试脚本 (`auto-build-test.sh`)
验证整个构建过程，确保所有组件正确生成。

**测试步骤**:
1. **完全清理**: 删除所有缓存和构建产物
2. **Python 二进制构建**: 重新生成 Python 服务二进制文件
3. **脚本内容验证**: 检查生成的二进制文件内容
4. **前端构建**: 构建前端资源
5. **Tauri 构建**: 执行 Tauri 应用打包
6. **打包验证**: 验证应用包中的文件完整性

### 3. 应用测试脚本 (`auto-app-test.sh`)
测试打包后应用的实际运行情况。

**测试功能**:
- 启动应用并捕获所有输出日志
- 监控系统日志获取额外信息
- 测试服务连通性
- 收集进程和网络信息
- 执行功能测试验证

### 4. 日志分析工具 (`log-analyzer.py`)
智能分析所有日志文件，识别问题模式并提供修复建议。

**分析能力**:
- 识别常见错误模式 (Broken pipe, 连接拒绝, 权限问题等)
- 统计问题出现频率
- 提供具体的修复建议
- 生成详细的分析报告

## 📊 输出文件

所有测试运行后会在 `logs/` 目录下生成以下文件:

```
logs/
├── master_test_YYYYMMDD_HHMMSS.log           # 主日志文件
├── build_test_YYYYMMDD_HHMMSS.log            # 构建测试日志
├── app_test_YYYYMMDD_HHMMSS.log              # 应用测试日志
├── app_output_YYYYMMDD_HHMMSS.log            # 应用运行输出
├── system_log_YYYYMMDD_HHMMSS.log            # 系统日志
├── process_info_YYYYMMDD_HHMMSS.log          # 进程信息
├── analysis_report_YYYYMMDD_HHMMSS.txt       # 分析报告
├── final_report_YYYYMMDD_HHMMSS.md           # 最终综合报告
└── *-strings_YYYYMMDD_HHMMSS.log             # 二进制文件字符串分析
```

## 🔧 常见问题诊断

### Broken Pipe 错误
**症状**: 应用启动后 Python 服务无法正常通信
**可能原因**:
1. Python 服务启动失败
2. 端口绑定问题
3. 进程间通信中断
4. 权限或防火墙限制

**诊断步骤**:
1. 运行完整测试: `./scripts/master-test.sh --clean --full-flow`
2. 检查分析报告中的错误模式
3. 验证二进制文件是否正确生成
4. 检查应用输出日志中的启动信息

### 构建失败
**症状**: Tauri 构建过程中出错
**诊断步骤**:
1. 仅运行构建测试: `./scripts/master-test.sh --build-only`
2. 检查 Python 依赖是否完整
3. 验证前端构建是否成功
4. 检查 tauri.conf.json 配置

### 应用无法启动
**症状**: 打包后的应用无法正常启动
**诊断步骤**:
1. 运行应用测试: `./scripts/master-test.sh --test-only`
2. 检查应用权限设置
3. 查看系统控制台日志
4. 验证签名和安全设置

## 📈 性能优化建议

### 构建优化
- 使用增量构建避免完全重建
- 定期清理构建缓存防止问题积累
- 监控构建时间识别性能瓶颈

### 测试优化
- 根据变更范围选择合适的测试级别
- 使用并行测试加速验证过程
- 定期更新测试模式以覆盖新问题

## 🛠️ 自定义配置

### 添加新的错误模式
在 `log-analyzer.py` 中的 `ERROR_PATTERNS` 字典中添加新模式:

```python
'new_error_pattern': {
    'pattern': r'your_regex_pattern',
    'category': 'category_name',
    'severity': 'critical|high|medium|low',
    'description': '错误描述',
    'solution': '建议解决方案'
}
```

### 调整测试超时时间
在相应脚本中修改超时设置:
- 构建超时: 修改 `auto-build-test.sh` 中的构建命令
- 应用启动超时: 修改 `auto-app-test.sh` 中的 `max_wait` 变量
- 总体测试超时: 修改 `master-test.sh` 中的 `timeout` 命令参数

## 🔄 持续集成

这套工具可以集成到 CI/CD 流程中:

```yaml
# GitHub Actions 示例
- name: Run Tauri Tests
  run: |
    chmod +x scripts/master-test.sh
    ./scripts/master-test.sh --no-interaction --full-flow
```

## 📞 故障排除

如果自动化测试本身出现问题:

1. **检查权限**: 确保所有脚本具有执行权限
2. **验证依赖**: 确保 yarn, python3 等工具已安装
3. **清理环境**: 使用 `--clean` 选项重新开始
4. **手动执行**: 逐步手动运行各个脚本进行调试

## 📝 更新日志

- **v1.0**: 初始版本，包含完整的自动化测试流程
- 支持构建验证、应用测试和日志分析
- 提供详细的问题诊断和修复建议