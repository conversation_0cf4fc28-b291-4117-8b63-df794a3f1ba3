#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包后应用通信测试脚本
===================
用于验证打包后的Tauri应用是否能正常与Python服务通信

测试内容:
1. 检查应用结构和文件完整性
2. 启动counter-service服务并测试通信
3. 验证服务的启动、响应和停止功能
4. 检查服务是否能正常处理命令和推送数据
"""

import os
import sys
import subprocess
import json
import time
import threading
import signal
import logging
import requests
import select
from datetime import datetime
from typing import Optional, Dict, Any, List
import tempfile
import shutil

# 配置日志
log_file = f"/tmp/test_packaged_app_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file, encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

class PackagedAppTester:
    """打包后应用测试器"""
    
    def __init__(self, app_path: str):
        self.app_path = app_path
        self.running = True
        self.test_results = {}
        self.processes = {}
        
        # 服务路径
        self.services = {
            'counter-service': os.path.join(app_path, "Contents", "MacOS", "counter-service"),
            'futu-adapter': os.path.join(app_path, "Contents", "MacOS", "futu-adapter"),
            'huasheng-adapter': os.path.join(app_path, "Contents", "MacOS", "huasheng-adapter")
        }
        
    def log_test_start(self, test_name: str):
        """记录测试开始"""
        logger.info("=" * 60)
        logger.info(f"开始测试: {test_name}")
        logger.info("=" * 60)
        
    def log_test_result(self, test_name: str, success: bool, details: str = ""):
        """记录测试结果"""
        status = "PASS" if success else "FAIL"
        logger.info(f"测试结果: {test_name} - {status}")
        if details:
            logger.info(f"详细信息: {details}")
        self.test_results[test_name] = {'success': success, 'details': details}
        logger.info("-" * 40)
        
    def test_app_structure(self) -> bool:
        """测试1: 检查应用结构完整性"""
        self.log_test_start("应用结构完整性检查")
        
        try:
            # 检查应用主目录
            if not os.path.exists(self.app_path):
                self.log_test_result("应用结构完整性检查", False, f"应用路径不存在: {self.app_path}")
                return False
                
            # 检查主执行文件
            main_executable = os.path.join(self.app_path, "Contents", "MacOS", "XX交易终端")
            if not os.path.exists(main_executable):
                self.log_test_result("应用结构完整性检查", False, f"主执行文件不存在: {main_executable}")
                return False
                
            # 检查Python服务文件
            missing_services = []
            for service_name, service_path in self.services.items():
                if not os.path.exists(service_path):
                    missing_services.append(service_name)
                else:
                    # 检查是否可执行
                    if not os.access(service_path, os.X_OK):
                        logger.warning(f"服务文件不可执行: {service_path}")
                        
            if missing_services:
                self.log_test_result("应用结构完整性检查", False, f"缺失的服务: {missing_services}")
                return False
                
            # 检查资源文件
            resources_path = os.path.join(self.app_path, "Contents", "Resources")
            if os.path.exists(resources_path):
                logger.info(f"发现资源目录: {resources_path}")
                
            self.log_test_result("应用结构完整性检查", True, "所有必需文件都存在且可执行")
            return True
            
        except Exception as e:
            self.log_test_result("应用结构完整性检查", False, f"检查过程中出错: {e}")
            return False
            
    def test_service_executable(self, service_name: str) -> bool:
        """测试服务是否可独立运行"""
        self.log_test_start(f"服务可执行性测试: {service_name}")
        
        service_path = self.services.get(service_name)
        if not service_path or not os.path.exists(service_path):
            self.log_test_result(f"服务可执行性测试: {service_name}", False, "服务文件不存在")
            return False
            
        try:
            # 尝试运行服务并快速终止
            process = subprocess.Popen(
                [service_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True
            )
            
            # 等待短时间看是否能正常启动
            time.sleep(1)
            
            if process.poll() is not None:
                # 进程已退出，检查退出码
                stdout, stderr = process.communicate()
                if process.returncode != 0:
                    self.log_test_result(f"服务可执行性测试: {service_name}", False, 
                                       f"进程异常退出: {process.returncode}, stderr: {stderr[:200]}")
                    return False
            else:
                # 进程仍在运行，发送停止信号
                process.terminate()
                try:
                    process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    process.kill()
                    process.wait()
                    
            self.log_test_result(f"服务可执行性测试: {service_name}", True, "服务可以正常启动")
            return True
            
        except Exception as e:
            self.log_test_result(f"服务可执行性测试: {service_name}", False, f"测试过程中出错: {e}")
            return False
            
    def test_counter_service_communication(self) -> bool:
        """测试2: Counter服务通信功能"""
        self.log_test_start("Counter服务通信测试")
        
        service_path = self.services.get('counter-service')
        if not service_path or not os.path.exists(service_path):
            self.log_test_result("Counter服务通信测试", False, "counter-service文件不存在")
            return False
            
        process = None
        try:
            # 启动counter服务
            logger.info("启动counter-service进程...")
            process = subprocess.Popen(
                [service_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
                bufsize=0  # 无缓冲
            )
            
            # 等待服务启动
            time.sleep(2)
            
            if process.poll() is not None:
                stdout, stderr = process.communicate()
                self.log_test_result("Counter服务通信测试", False, 
                                   f"服务启动失败: 退出码 {process.returncode}, stderr: {stderr}")
                return False
                
            logger.info(f"Counter服务已启动，进程ID: {process.pid}")
            
            # 测试基本通信
            success = self._test_counter_commands(process)
            
            if success:
                # 测试数据推送
                success = self._test_counter_push_data(process)
                
            return success
            
        except Exception as e:
            self.log_test_result("Counter服务通信测试", False, f"测试过程中出错: {e}")
            return False
            
        finally:
            if process and process.poll() is None:
                logger.info("正在关闭counter服务...")
                try:
                    # 发送停止命令
                    stop_command = {
                        'id': 'stop_test',
                        'action': 'stop',
                        'params': {}
                    }
                    process.stdin.write(json.dumps(stop_command) + '\n')
                    process.stdin.flush()
                    
                    # 等待进程结束
                    try:
                        process.wait(timeout=5)
                        logger.info("Counter服务正常关闭")
                    except subprocess.TimeoutExpired:
                        logger.warning("Counter服务超时未关闭，强制终止")
                        process.terminate()
                        try:
                            process.wait(timeout=3)
                        except subprocess.TimeoutExpired:
                            process.kill()
                            process.wait()
                            
                except Exception as e:
                    logger.error(f"关闭counter服务时出错: {e}")
                    if process.poll() is None:
                        process.kill()
                        
    def _test_counter_commands(self, process) -> bool:
        """测试counter服务命令功能"""
        commands = [
            {'id': 'ping_test', 'action': 'ping', 'params': {}},
            {'id': 'get_counter_test', 'action': 'get_counter', 'params': {}},
            {'id': 'reset_counter_test', 'action': 'reset_counter', 'params': {}}
        ]
        
        for command in commands:
            try:
                logger.info(f"发送命令: {command['action']}")
                
                # 发送命令
                command_json = json.dumps(command) + '\n'
                process.stdin.write(command_json)
                process.stdin.flush()
                
                # 等待响应 (使用非阻塞读取)
                response = self._read_response_with_timeout(process, timeout=5)
                
                if response is None:
                    self.log_test_result("Counter服务通信测试", False, f"命令 {command['action']} 超时无响应")
                    return False
                    
                logger.info(f"收到响应: {response}")
                
                # 验证响应格式
                if not self._validate_response(response, command['id']):
                    self.log_test_result("Counter服务通信测试", False, f"命令 {command['action']} 响应格式错误")
                    return False
                    
                time.sleep(0.5)  # 命令间隔
                
            except Exception as e:
                self.log_test_result("Counter服务通信测试", False, f"命令 {command['action']} 执行失败: {e}")
                return False
                
        logger.info("所有命令测试通过")
        return True
        
    def _test_counter_push_data(self, process) -> bool:
        """测试counter服务数据推送功能"""
        logger.info("测试数据推送功能...")
        
        try:
            # 监听推送数据，等待几个计数器推送
            push_data_received = []
            start_time = time.time()
            timeout = 10  # 10秒超时
            
            while len(push_data_received) < 3 and (time.time() - start_time) < timeout:
                response = self._read_response_with_timeout(process, timeout=2)
                
                if response and response.get('type') == 'push' and response.get('source') == 'counter':
                    push_data_received.append(response)
                    logger.info(f"收到推送数据: counter={response.get('data', {}).get('counter')}")
                    
            if len(push_data_received) < 3:
                self.log_test_result("Counter服务通信测试", False, f"推送数据不足，期望3个，实际收到{len(push_data_received)}个")
                return False
                
            # 验证计数器递增
            counters = [data.get('data', {}).get('counter', 0) for data in push_data_received]
            if not all(counters[i] < counters[i+1] for i in range(len(counters)-1)):
                self.log_test_result("Counter服务通信测试", False, f"计数器未正确递增: {counters}")
                return False
                
            self.log_test_result("Counter服务通信测试", True, f"成功接收到{len(push_data_received)}个推送数据，计数器正确递增")
            return True
            
        except Exception as e:
            self.log_test_result("Counter服务通信测试", False, f"推送数据测试失败: {e}")
            return False
            
    def _read_response_with_timeout(self, process, timeout: float = 5) -> Optional[Dict[str, Any]]:
        """带超时的响应读取"""
        start_time = time.time()
        
        while (time.time() - start_time) < timeout:
            try:
                # 检查进程是否仍在运行
                if process.poll() is not None:
                    logger.error("进程已终止，无法读取响应")
                    return None
                    
                # 使用select检查是否有数据可读 (仅适用于Unix系统)
                if hasattr(select, 'select'):
                    ready, _, _ = select.select([process.stdout], [], [], 0.1)
                    if ready:
                        line = process.stdout.readline()
                        if line:
                            try:
                                return json.loads(line.strip())
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON解析失败: {e}, 原始数据: {line}")
                                continue
                else:
                    # Windows系统的备用方案
                    time.sleep(0.1)
                    if process.stdout.readable():
                        line = process.stdout.readline()
                        if line:
                            try:
                                return json.loads(line.strip())
                            except json.JSONDecodeError:
                                continue
                                
            except Exception as e:
                logger.error(f"读取响应时出错: {e}")
                time.sleep(0.1)
                
        return None
        
    def _validate_response(self, response: Dict[str, Any], expected_command_id: str) -> bool:
        """验证响应格式"""
        required_fields = ['type', 'source', 'timestamp', 'success']
        
        for field in required_fields:
            if field not in response:
                logger.error(f"响应缺少必需字段: {field}")
                return False
                
        if response.get('type') == 'response' and response.get('command_id') != expected_command_id:
            logger.error(f"命令ID不匹配，期望: {expected_command_id}, 实际: {response.get('command_id')}")
            return False
            
        return True
        
    def test_service_startup_sequence(self) -> bool:
        """测试3: 服务启动序列"""
        self.log_test_start("服务启动序列测试")
        
        try:
            # 按照预期的启动顺序测试各个服务
            services_to_test = ['counter-service']  # 先测试counter服务
            
            for service_name in services_to_test:
                logger.info(f"测试 {service_name} 启动序列...")
                
                if not self.test_service_executable(service_name):
                    self.log_test_result("服务启动序列测试", False, f"{service_name} 无法正常启动")
                    return False
                    
            self.log_test_result("服务启动序列测试", True, "所有服务都能正常启动")
            return True
            
        except Exception as e:
            self.log_test_result("服务启动序列测试", False, f"测试过程中出错: {e}")
            return False
            
    def test_error_handling(self) -> bool:
        """测试4: 错误处理能力"""
        self.log_test_start("错误处理能力测试")
        
        service_path = self.services.get('counter-service')
        if not service_path or not os.path.exists(service_path):
            self.log_test_result("错误处理能力测试", False, "counter-service文件不存在")
            return False
            
        process = None
        try:
            # 启动counter服务
            process = subprocess.Popen(
                [service_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                stdin=subprocess.PIPE,
                text=True,
                bufsize=0
            )
            
            time.sleep(1)
            
            if process.poll() is not None:
                self.log_test_result("错误处理能力测试", False, "服务无法启动")
                return False
                
            # 测试无效命令
            invalid_command = {
                'id': 'invalid_test',
                'action': 'invalid_action',
                'params': {}
            }
            
            logger.info("发送无效命令测试错误处理...")
            process.stdin.write(json.dumps(invalid_command) + '\n')
            process.stdin.flush()
            
            response = self._read_response_with_timeout(process, timeout=5)
            
            if not response:
                self.log_test_result("错误处理能力测试", False, "无效命令无响应")
                return False
                
            # 验证错误响应
            if response.get('success') is not False:
                self.log_test_result("错误处理能力测试", False, "无效命令应该返回失败状态")
                return False
                
            if not response.get('error'):
                self.log_test_result("错误处理能力测试", False, "错误响应应该包含错误信息")
                return False
                
            logger.info(f"错误处理正确: {response.get('error')}")
            
            # 测试格式错误的JSON
            logger.info("发送格式错误的JSON...")
            process.stdin.write("invalid_json_format\n")
            process.stdin.flush()
            
            # 等待一下，服务应该能正常处理这个错误而不崩溃
            time.sleep(1)
            
            # 发送正常命令验证服务仍在运行
            ping_command = {
                'id': 'ping_after_error',
                'action': 'ping',
                'params': {}
            }
            
            process.stdin.write(json.dumps(ping_command) + '\n')
            process.stdin.flush()
            
            response = self._read_response_with_timeout(process, timeout=5)
            
            if not response or not response.get('success'):
                self.log_test_result("错误处理能力测试", False, "服务在处理错误输入后无法响应正常命令")
                return False
                
            self.log_test_result("错误处理能力测试", True, "服务能正确处理各种错误情况")
            return True
            
        except Exception as e:
            self.log_test_result("错误处理能力测试", False, f"测试过程中出错: {e}")
            return False
            
        finally:
            if process and process.poll() is None:
                try:
                    process.terminate()
                    process.wait(timeout=3)
                except:
                    process.kill()
                    
    def generate_test_report(self):
        """生成测试报告"""
        logger.info("\n" + "=" * 60)
        logger.info("测试报告汇总")
        logger.info("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['success'])
        
        logger.info(f"总测试数: {total_tests}")
        logger.info(f"通过测试: {passed_tests}")
        logger.info(f"失败测试: {total_tests - passed_tests}")
        logger.info(f"通过率: {(passed_tests / total_tests * 100):.1f}%" if total_tests > 0 else "N/A")
        
        logger.info("\n详细结果:")
        for test_name, result in self.test_results.items():
            status = "PASS" if result['success'] else "FAIL"
            logger.info(f"  {test_name}: {status}")
            if not result['success'] and result['details']:
                logger.info(f"    原因: {result['details']}")
                
        logger.info(f"\n详细日志已保存到: {log_file}")
        
        return passed_tests == total_tests
        
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        logger.info("开始打包后应用通信测试")
        logger.info(f"应用路径: {self.app_path}")
        logger.info(f"测试时间: {datetime.now()}")
        logger.info(f"Python版本: {sys.version}")
        logger.info(f"操作系统: {os.uname() if hasattr(os, 'uname') else 'Windows'}")
        
        # 设置信号处理
        def signal_handler(sig, frame):
            logger.info("收到中断信号，正在停止测试...")
            self.running = False
            # 清理所有进程
            for proc in self.processes.values():
                if proc.poll() is None:
                    proc.terminate()
            sys.exit(1)
            
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        try:
            # 执行所有测试
            tests = [
                self.test_app_structure,
                self.test_counter_service_communication,
                self.test_service_startup_sequence,
                self.test_error_handling
            ]
            
            for test_func in tests:
                if not self.running:
                    break
                    
                try:
                    test_func()
                except Exception as e:
                    logger.error(f"测试 {test_func.__name__} 执行异常: {e}")
                    
            # 生成报告
            return self.generate_test_report()
            
        except Exception as e:
            logger.error(f"测试执行过程中发生异常: {e}")
            import traceback
            logger.error(f"异常详情: {traceback.format_exc()}")
            return False

def main():
    """主函数"""
    # 默认应用路径
    default_app_path = "/Users/<USER>/Desktop/stock/src-tauri/target/release/bundle/macos/XX交易终端.app"
    
    # 检查命令行参数
    app_path = sys.argv[1] if len(sys.argv) > 1 else default_app_path
    
    logger.info(f"打包后应用通信测试脚本")
    logger.info(f"使用应用路径: {app_path}")
    
    if not os.path.exists(app_path):
        logger.error(f"应用路径不存在: {app_path}")
        logger.error("请先构建应用或提供正确的应用路径")
        return 1
        
    # 创建测试器并运行测试
    tester = PackagedAppTester(app_path)
    success = tester.run_all_tests()
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())