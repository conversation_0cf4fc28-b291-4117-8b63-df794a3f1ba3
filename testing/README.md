# Testing 测试工具集

这个目录包含专门用于诊断和测试 Tauri 应用的自动化工具。

## 目录结构

```
testing/
├── automated-tests/     # 自动化测试脚本
│   ├── test-suite.sh                 # 主控测试脚本
│   ├── auto-test-build.sh           # 完整构建测试
│   ├── diagnose-tauri-cache.sh      # Tauri 缓存诊断
│   └── monitor-app-logs.sh          # 实时日志监控
├── test_packaged_app.py # 打包后应用通信测试脚本
├── docs/               # 测试相关文档
│   └── 自动化测试流程使用指南.md    # 详细使用指南
└── README.md          # 本文件
```

## 快速使用

### 主控测试脚本（推荐）
```bash
# 运行交互式测试菜单
./testing/automated-tests/test-suite.sh
```

### 直接运行特定测试
```bash
# 完整自动化构建测试
./testing/automated-tests/auto-test-build.sh

# 打包后应用通信测试
./testing/test_packaged_app.py

# Tauri 缓存问题诊断
./testing/automated-tests/diagnose-tauri-cache.sh

# 实时应用日志监控
./testing/automated-tests/monitor-app-logs.sh
```

## 测试工具说明

### 🎯 test-suite.sh - 主控测试脚本
统一入口，提供交互式菜单选择不同测试功能。

### ✅ test_packaged_app.py - 打包后应用通信测试
- 验证打包后应用的结构完整性
- 测试Python服务的通信功能  
- 检查counter-service服务的启动和响应
- 验证命令处理和数据推送功能
- 测试错误处理能力

### 🔍 auto-test-build.sh - 完整构建测试
- 清理构建产物
- 验证脚本生成
- 执行 Tauri 构建
- 测试打包后应用
- 生成详细测试报告

### 🔧 diagnose-tauri-cache.sh - Tauri 缓存诊断
- 检查文件时间戳
- 分析 Tauri 配置
- 查找缓存位置
- 比较脚本版本差异
- 生成修复建议

### 📺 monitor-app-logs.sh - 实时日志监控
- 监控系统控制台日志
- 跟踪进程状态变化
- 监控文件系统活动
- 生成分析报告

## 日志输出

所有测试工具会在 `~/Desktop/` 目录下生成带时间戳的日志目录：
- `tauri_debug_logs_YYYYMMDD_HHMMSS/` - 构建测试日志
- `tauri_cache_diagnosis_YYYYMMDD_HHMMSS.log` - 缓存诊断日志
- `tauri_runtime_logs_YYYYMMDD_HHMMSS/` - 运行时日志

## 故障排除

如果测试脚本无法运行：
1. 确保脚本有执行权限：`chmod +x testing/automated-tests/*.sh`
2. 检查依赖工具是否安装：`yarn`, `python3`, `uv`
3. 确保在项目根目录运行测试

## 维护说明

这些测试工具由 Claude Code 创建和维护，用于诊断 Tauri 应用的构建和运行问题。
不要与项目主要的 scripts/ 目录混淆，这里专门用于测试和诊断用途。