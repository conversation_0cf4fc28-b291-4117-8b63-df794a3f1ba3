# Electron 量化交易平台

基于 Electron + React + TypeScript 构建的量化交易平台，专注于港股市场的算法交易和风险管理。

## 🚀 技术栈

- **客户端框架**: Electron
- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **样式框架**: TailwindCSS
- **图表库**: Recharts
- **状态管理**: React Query
- **通信协议**: IPC (Inter-Process Communication)

## 📦 项目结构

```
├── src/                          # 前端源码 (React)
│   ├── components/               # React 组件
│   ├── hooks/                    # React Hooks
│   ├── pages/                    # 页面组件
│   ├── communication/            # IPC 通信客户端
│   ├── types/                    # TypeScript 类型定义
│   └── utils/                    # 工具函数
├── electron-main/                # Electron 主进程
│   ├── src/                      # 主进程源码
│   │   ├── trading/              # 交易系统核心
│   │   ├── ipc/                  # IPC 处理器
│   │   └── services/             # 系统服务
│   └── scripts/                  # 构建和测试脚本
└── docs/                         # 项目文档
```

## 🏗️ 系统架构

### 核心组件
- **TradingSystem**: 交易系统主入口
- **TaskManager**: 任务生命周期管理
- **MarketDataManager**: 行情数据管理
- **TradingManager**: 交易执行管理
- **StrategyEngine**: 策略引擎
- **RiskManager**: 风险控制

### 通信架构
- **前端**: React 应用，通过 IPC 与主进程通信
- **主进程**: Electron 主进程，管理交易系统和业务逻辑
- **富途接口**: 通过 WebSocket 连接富途 OpenD
- **华盛通接口**: 通过 TCP 连接华盛通 API

## 🚀 快速开始

### 1. 环境要求
- Node.js 16+
- Yarn 包管理器
- TypeScript 4.5+

### 2. 安装依赖
```bash
# 安装前端依赖
yarn install

# 安装主进程依赖
cd electron-main
yarn install
cd ..
```

### 3. 开发模式启动
```bash
# 一键启动（推荐）
yarn electron:dev

# 或分别启动
yarn dev                    # 前端开发服务器
cd electron-main && yarn dev   # Electron 主进程
```

### 4. 构建生产版本
```bash
yarn electron:build
```

## 🎯 核心功能

### ✅ 已实现
- 交易系统初始化和状态监控
- 任务创建、启动、停止、删除
- 实时系统状态更新
- 环境自动检测
- 完整的 TypeScript 类型支持
- IPC 通信架构

### 🚧 开发中
- 实时行情数据推送
- 策略执行引擎
- 风险管理规则
- 交易执行和仓位管理
- 历史数据分析

## 📖 使用指南

### 基本操作
1. 启动应用后，系统会自动初始化交易系统
2. 在 Dashboard 中可以创建和管理交易任务
3. 支持多种策略类型：大单监控、突破追涨、均值回归等
4. 实时监控任务状态和系统连接状态

### 配置说明
配置文件位于: `electron-main/src/trading/config.ts`

```typescript
// 富途行情配置
market: {
    host: '127.0.0.1',
    port: 33333,                    // WebSocket 端口
    encryptKey: '232df6441fb2bbdd'   // 加密密钥
}

// 华盛通交易配置
trading: {
    huasheng: {
        host: '*************',
        port: 7777,
        account: process.env.HUASHENG_ACCOUNT,
        password: process.env.HUASHENG_PASSWORD
    }
}
```

### 环境变量
创建 `.env` 文件：
```bash
HUASHENG_ACCOUNT=your_account
HUASHENG_PASSWORD=your_password
NODE_ENV=development
LOG_LEVEL=info
```

## 🧪 测试

```bash
# 单元测试
cd electron-main && npm test

# 集成测试
cd electron-main && ./scripts/test-integration.sh

# 代码检查
yarn lint
```

## 📊 性能监控

- 内存使用监控
- IPC 通信效率
- 任务执行状态
- 连接稳定性

## 🐛 故障排除

### 常见问题
1. **交易系统初始化失败**: 检查富途 OpenD 是否启动
2. **任务创建失败**: 确认系统已初始化且配置正确
3. **连接断开**: 检查网络和 API 服务状态

### 调试技巧
- 开启详细日志: `export LOG_LEVEL=debug`
- 打开开发者工具: `Ctrl+Shift+I`
- 查看主进程日志: 控制台输出

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交 Pull Request

## 📝 更新日志

### v0.1.0 (当前版本)
- ✅ 完成 Electron 架构搭建
- ✅ 实现基础交易系统
- ✅ 创建 IPC 通信机制
- ✅ 完成前端 Dashboard 界面
- ✅ 支持任务管理功能

## 📜 许可证

MIT License - 详见 [LICENSE](./LICENSE) 文件

## 📞 联系方式

- 项目仓库: [GitHub](https://github.com/your-username/electron-trading-platform)
- 问题反馈: [Issues](https://github.com/your-username/electron-trading-platform/issues)

---

**注意**: 本项目仅供学习和研究使用，请遵守相关法律法规，投资有风险，决策需谨慎。