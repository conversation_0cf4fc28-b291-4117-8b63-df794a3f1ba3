# 环境变量配置说明

## 📋 概述

为了提高安全性和灵活性，系统支持通过环境变量来配置敏感信息，避免在代码中硬编码账户、密码等敏感数据。

## 🔐 支持的环境变量

### 华盛通交易配置
```bash
# 华盛通服务器配置
HUASHENG_HOST=127.0.0.1           # 华盛通服务器地址
HUASHENG_PORT=8080                # 华盛通服务器端口
HUASHENG_ACCOUNT=your_account     # 华盛通账户
HUASHENG_PASSWORD=your_password   # 华盛通密码
HUASHENG_TIMEOUT=30000           # 连接超时时间(毫秒)
HUASHENG_ENABLED=true            # 是否启用华盛通交易
```

### 富途OpenD配置
```bash
# 富途OpenD配置
FUTU_HOST=127.0.0.1                    # 富途OpenD地址
FUTU_PORT=33333                        # 富途WebSocket端口
FUTU_ENCRYPT_KEY=232df6441fb2bbdd      # WebSocket加密密钥
FUTU_UNLOCK_PASSWORD=your_password     # 解锁密码(如需要)
FUTU_ENABLED=false                     # 是否启用富途交易
```

### 日志配置
```bash
# 日志系统配置
LOG_LEVEL=info                    # 日志级别: debug, info, warn, error
LOG_MAX_FILES=5                   # 最大日志文件数
LOG_MAX_SIZE=10MB                 # 单个日志文件最大大小
```

### 风险控制配置
```bash
# 风险控制参数
RISK_MAX_DAILY_LOSS=50000         # 最大日亏损限额
RISK_MAX_SINGLE_ORDER=100000      # 最大单笔订单金额
RISK_ENABLE_EMERGENCY_STOP=true   # 是否启用紧急停止
```

## 🚀 使用方式

### 1. 开发环境 (.env 文件)

在项目根目录创建 `.env` 文件：

```bash
# .env (不要提交到git)
HUASHENG_HOST=*************
HUASHENG_PORT=7777
HUASHENG_ACCOUNT=your_real_account
HUASHENG_PASSWORD=your_real_password
HUASHENG_ENABLED=true

FUTU_HOST=127.0.0.1
FUTU_PORT=33333
FUTU_ENCRYPT_KEY=232df6441fb2bbdd
FUTU_ENABLED=true

LOG_LEVEL=debug
```

### 2. 生产环境

```bash
# 系统环境变量设置
export HUASHENG_HOST=production.server.com
export HUASHENG_PORT=7777
export HUASHENG_ACCOUNT=prod_account
export HUASHENG_PASSWORD=secure_password
export HUASHENG_ENABLED=true

export FUTU_HOST=127.0.0.1
export FUTU_PORT=33333
export FUTU_ENCRYPT_KEY=your_production_key
export FUTU_ENABLED=true

export LOG_LEVEL=info
export RISK_MAX_DAILY_LOSS=100000
```

### 3. Docker 环境

```yaml
# docker-compose.yml
version: '3.8'
services:
  trading-app:
    image: trading-terminal
    environment:
      - HUASHENG_HOST=trading.server.com
      - HUASHENG_PORT=7777
      - HUASHENG_ACCOUNT=${HUASHENG_ACCOUNT}
      - HUASHENG_PASSWORD=${HUASHENG_PASSWORD}
      - FUTU_HOST=127.0.0.1
      - FUTU_PORT=33333
      - FUTU_ENCRYPT_KEY=${FUTU_ENCRYPT_KEY}
      - LOG_LEVEL=info
```

### 4. GitHub Actions / CI

```yaml
# .github/workflows/test.yml
env:
  HUASHENG_HOST: test.server.com
  HUASHENG_PORT: 7777
  HUASHENG_ACCOUNT: ${{ secrets.HUASHENG_ACCOUNT }}
  HUASHENG_PASSWORD: ${{ secrets.HUASHENG_PASSWORD }}
  FUTU_ENCRYPT_KEY: ${{ secrets.FUTU_ENCRYPT_KEY }}
  LOG_LEVEL: debug
```

## 🔄 配置优先级

配置加载的优先级顺序（从高到低）：

1. **环境变量** - 最高优先级
2. **用户配置文件** - `config.json`
3. **环境特定配置** - `config/development.json`
4. **默认配置文件** - `config/default.json`
5. **内置默认值** - 代码中的fallback值

## ⚠️ 安全注意事项

### 敏感信息保护
1. **不要提交敏感信息到git**：
   - 将 `.env` 文件添加到 `.gitignore`
   - 使用环境变量存储账户、密码等

2. **生产环境安全**：
   - 使用系统级环境变量
   - 考虑使用密钥管理服务
   - 定期轮换密码和密钥

3. **最小权限原则**：
   - 只配置必要的交易权限
   - 为不同环境使用不同的账户

### 配置验证
系统会自动验证关键配置项：
- 必填字段检查
- 数值范围验证
- 网络连通性测试

## 🔧 调试配置

### 查看当前配置
```bash
# 启用调试模式查看配置加载过程
export DEBUG=config:*
npm start
```

### 配置测试
```bash
# 测试配置有效性
npm run test:config
```

## 📝 示例 .env 文件

```bash
# ===========================================
# 量化交易终端环境变量配置
# ===========================================

# 华盛通交易配置
HUASHENG_HOST=127.0.0.1
HUASHENG_PORT=7777
HUASHENG_ACCOUNT=your_huasheng_account
HUASHENG_PASSWORD=your_huasheng_password
HUASHENG_TIMEOUT=30000
HUASHENG_ENABLED=true

# 富途OpenD配置
FUTU_HOST=127.0.0.1
FUTU_PORT=33333
FUTU_ENCRYPT_KEY=232df6441fb2bbdd
FUTU_UNLOCK_PASSWORD=
FUTU_ENABLED=true

# 系统配置
LOG_LEVEL=debug
LOG_MAX_FILES=10
LOG_MAX_SIZE=50MB

# 风险控制
RISK_MAX_DAILY_LOSS=10000
RISK_MAX_SINGLE_ORDER=50000
RISK_ENABLE_EMERGENCY_STOP=true
```

将此文件保存为 `.env` 并根据你的实际情况修改配置值。