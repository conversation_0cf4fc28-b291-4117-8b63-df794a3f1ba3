# 依赖和构建输出
node_modules/
dist/
build/
*.tsbuildinfo

# 环境变量和敏感配置
.env
.env.local
.env.*.local

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 运行时文件
*.pid
*.seed
*.pid.lock
.npm
.node_repl_history

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~

# Electron
out/
app/
*.dmg
*.exe
*.deb
*.rpm
*.tar.gz
*.zip

# 测试覆盖率
coverage/
.nyc_output/

# 临时文件
tmp/
temp/
.tmp/

# 备份文件
*.bak
*.backup

# 用户数据和配置
userData/

# Tauri (如果有混合使用)
src-tauri/target/

# 敏感数据文件
*.key
*.pem
*.p12
certificates/
secrets/