# Electron 主进程目录

这个目录包含 Electron 应用的主进程代码，已完全集成交易系统服务。

## 目录结构

- `src/` - 源代码目录
  - `ipc/` - IPC 通信处理
    - `handlers.ts` - IPC处理器（包含完整交易系统API）
    - `types.ts` - IPC通信类型定义
    - `channels.ts` - 通道定义
  - `services/` - 系统服务
    - `TradingSystemService.ts` - 交易系统服务管理器
  - `trading/` - 交易系统核心
    - `TradingSystem.ts` - 统一入口类
    - `TaskManager.ts` - 任务管理器
    - `MarketDataManager.ts` - 行情数据管理器
    - `TradingManager.ts` - 交易管理器
    - `StrategyEngine.ts` - 策略引擎
    - `RiskManager.ts` - 风险管理器
    - `config.ts` - 交易系统配置管理 ⭐
    - `__tests__/` - 真实环境测试
  - `utils/` - 工具函数
  - `windowManager.ts` - 窗口管理
  - `index.ts` - 主进程入口
- `scripts/` - 开发脚本
  - `test-integration.sh` - 交易系统集成测试脚本
  - `README.md` - 脚本使用说明
- `testing/` - 测试工具
- `dist/` - 编译输出目录
- `INTEGRATION.md` - 交易系统集成文档

## 🚀 快速开始

### 开发环境测试
```bash
# 使用集成测试脚本（推荐）
./scripts/test-integration.sh

# 或者手动启动
npm start
```

### 构建
```bash
yarn electron:compile
```

## 📡 交易系统特性

- ✅ 完整的任务管理系统（创建、启动、停止、删除）
- ✅ 真实接口集成（富途OpenD + 华盛通交易）
- ✅ 实时行情数据处理
- ✅ 多种交易策略支持
- ✅ 风险管理和控制
- ✅ IPC通信接口
- ✅ 事件驱动架构
- ✅ 环境配置管理

## 📋 API接口

### 交易系统管理
- `trading-system:getStatus` - 获取系统状态
- `trading-system:initialize` - 初始化系统
- `trading-system:shutdown` - 关闭系统
- `trading-system:getMarketStatus` - 获取行情连接状态
- `trading-system:getTradingStatus` - 获取交易连接状态

### 任务管理
- `task:create` - 创建交易任务
- `task:start` - 启动任务
- `task:stop` - 停止任务
- `task:delete` - 删除任务
- `task:getList` - 获取任务列表
- `task:getStatus` - 获取任务状态

详细API文档请参考 [INTEGRATION.md](INTEGRATION.md)

## 🛠️ 开发说明

### 环境要求
- Node.js 16+
- TypeScript 4.5+
- 富途OpenD (WebSocket端口33333, 密钥: 232df6441fb2bbdd)
- 华盛通交易服务 (端口7777)

### 环境变量配置
系统支持通过环境变量配置敏感信息，详见 [ENVIRONMENT_VARIABLES.md](ENVIRONMENT_VARIABLES.md)

**快速开始**：创建 `.env` 文件
```bash
# 华盛通配置
HUASHENG_HOST=127.0.0.1
HUASHENG_ACCOUNT=your_account
HUASHENG_PASSWORD=your_password

# 富途配置  
FUTU_ENCRYPT_KEY=232df6441fb2bbdd
```

### 调试模式
```bash
export DEBUG=trading-system:*
npm start
```

### 测试
```bash
# 真实环境集成测试
cd src/trading/__tests__
npm run demo

# 单元测试
npm test
```