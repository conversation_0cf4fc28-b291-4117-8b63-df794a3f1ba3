import { app, BrowserWindow, shell } from "electron";
import path from "path";
import { isDev } from "./utils/environment";

export class WindowManager {
    private static mainWindow: BrowserWindow | null = null;
    private static windowState = {
        width: 1250,
        height: 800,
        x: undefined as number | undefined,
        y: undefined as number | undefined,
        isMaximized: false
    };

    /**
     * 创建主窗口
     */
    static createMainWindow(): BrowserWindow {
        // 创建浏览器窗口
        this.mainWindow = new BrowserWindow({
            width: this.windowState.width,
            height: this.windowState.height,
            x: this.windowState.x,
            y: this.windowState.y,
            minWidth: 800,
            minHeight: 600,
            title: "量化交易终端",
            icon: path.join(__dirname, "../assets/icons/128x128.png"),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                sandbox: true,
                webSecurity: true,
                preload: path.join(__dirname, "preload.js")
            },
            show: false, // 先不显示，等内容加载完成
            backgroundColor: "#f8f9fa",
            titleBarStyle: process.platform === "darwin" ? "default" : "default",
            frame: true,
            titleBarOverlay: false
        });

        // 恢复最大化状态
        if (this.windowState.isMaximized) {
            this.mainWindow.maximize();
        }

        // 窗口准备好后显示
        this.mainWindow.once("ready-to-show", () => {
            this.mainWindow?.show();

            // 开发环境下自动打开 DevTools
            if (isDev()) {
                // this.mainWindow?.webContents.openDevTools();
            }
        });

        // 加载应用
        if (isDev()) {
            // 开发环境加载 Vite 开发服务器
            this.mainWindow.loadURL("http://localhost:1420");
        } else {
            // 生产环境加载打包后的文件
            const htmlPath = path.join(app.getAppPath(), "dist-web", "index.html");
            console.log("Loading HTML from:", htmlPath);

            // 使用 file:// URL 而不是 loadFile() 来确保资源路径正确解析
            const fileUrl = `file://${htmlPath}`;
            console.log("Loading URL:", fileUrl);

            this.mainWindow.loadURL(fileUrl).catch((error) => {
                console.error("Failed to load HTML URL:", error);
                console.error("HTML path:", htmlPath);
                console.error("File URL:", fileUrl);
                console.error("App path:", app.getAppPath());
            });
        }

        // 处理窗口事件
        this.setupWindowEvents();

        return this.mainWindow;
    }

    /**
     * 设置窗口事件
     */
    private static setupWindowEvents(): void {
        if (!this.mainWindow) return;

        // 窗口关闭事件
        this.mainWindow.on("closed", () => {
            this.mainWindow = null;
        });

        // 保存窗口状态
        this.mainWindow.on("resize", () => this.saveWindowState());
        this.mainWindow.on("move", () => this.saveWindowState());
        this.mainWindow.on("maximize", () => {
            this.windowState.isMaximized = true;
        });
        this.mainWindow.on("unmaximize", () => {
            this.windowState.isMaximized = false;
        });

        // 处理外部链接
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            shell.openExternal(url);
            return { action: "deny" };
        });

        // 阻止默认的文件拖放行为
        this.mainWindow.webContents.on("will-navigate", (event, url) => {
            if (url !== this.mainWindow?.webContents.getURL()) {
                event.preventDefault();
            }
        });

        // 处理证书错误
        this.mainWindow.webContents.on("certificate-error", (event, url, error, certificate, callback) => {
            if (isDev()) {
                // 开发环境下忽略证书错误
                event.preventDefault();
                callback(true);
            } else {
                // 生产环境使用默认行为
                callback(false);
            }
        });

        // 添加详细的加载状态监听
        this.mainWindow.webContents.on("did-start-loading", () => {
            console.log("Started loading page");
        });

        this.mainWindow.webContents.on("did-finish-load", () => {
            console.log("Finished loading page successfully");
        });

        this.mainWindow.webContents.on("did-fail-load", (event, errorCode, errorDescription, validatedURL) => {
            console.error("Failed to load page:", {
                errorCode,
                errorDescription,
                validatedURL
            });
        });

        // 监听渲染进程的控制台消息
        this.mainWindow.webContents.on("console-message", (event, level, message, line, sourceId) => {
            console.log(`[Renderer]`, message, line, sourceId);
        });
    }

    /**
     * 保存窗口状态
     */
    private static saveWindowState(): void {
        if (!this.mainWindow) return;

        const bounds = this.mainWindow.getBounds();
        this.windowState = {
            ...this.windowState,
            width: bounds.width,
            height: bounds.height,
            x: bounds.x,
            y: bounds.y
        };
    }

    /**
     * 获取主窗口
     */
    static getMainWindow(): BrowserWindow | null {
        return this.mainWindow;
    }

    /**
     * 发送消息到渲染进程
     */
    static send(channel: string, ...args: any[]): void {
        this.mainWindow?.webContents.send(channel, ...args);
    }

    /**
     * 显示开发者工具
     */
    static toggleDevTools(): void {
        if (this.mainWindow) {
            if (this.mainWindow.webContents.isDevToolsOpened()) {
                this.mainWindow.webContents.closeDevTools();
            } else {
                this.mainWindow.webContents.openDevTools();
            }
        }
    }

    /**
     * 重新加载窗口
     */
    static reload(): void {
        this.mainWindow?.webContents.reload();
    }

    /**
     * 最小化窗口
     */
    static minimize(): void {
        this.mainWindow?.minimize();
    }

    /**
     * 最大化/还原窗口
     */
    static toggleMaximize(): void {
        if (this.mainWindow) {
            if (this.mainWindow.isMaximized()) {
                this.mainWindow.unmaximize();
            } else {
                this.mainWindow.maximize();
            }
        }
    }

    /**
     * 关闭窗口
     */
    static close(): void {
        this.mainWindow?.close();
    }
}
