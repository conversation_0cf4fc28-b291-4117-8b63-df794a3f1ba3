import { Menu, MenuItem, app, BrowserWindow } from 'electron';
import { WindowManager } from './windowManager';

/**
 * 创建应用菜单
 */
export function createApplicationMenu(): void {
    const template: Electron.MenuItemConstructorOptions[] = [
        {
            label: '应用',
            submenu: [
                {
                    label: '关于量化交易终端',
                    click: () => {
                        // TODO: 显示关于对话框
                    }
                },
                { type: 'separator' },
                {
                    label: '偏好设置...',
                    accelerator: 'CmdOrCtrl+,',
                    click: () => {
                        // TODO: 打开设置窗口
                    }
                },
                { type: 'separator' },
                {
                    label: '隐藏',
                    accelerator: 'CmdOrCtrl+H',
                    role: 'hide'
                },
                {
                    label: '隐藏其他',
                    accelerator: 'CmdOrCtrl+Shift+H',
                    role: 'hideOthers'
                },
                {
                    label: '显示全部',
                    role: 'unhide'
                },
                { type: 'separator' },
                {
                    label: '退出',
                    accelerator: 'CmdOrCtrl+Q',
                    click: () => app.quit()
                }
            ]
        },
        {
            label: '编辑',
            submenu: [
                { role: 'undo', label: '撤销' },
                { role: 'redo', label: '重做' },
                { type: 'separator' },
                { role: 'cut', label: '剪切' },
                { role: 'copy', label: '复制' },
                { role: 'paste', label: '粘贴' },
                { role: 'selectAll', label: '全选' }
            ]
        },
        {
            label: '文件',
            submenu: [
                {
                    label: '新建任务',
                    accelerator: 'CmdOrCtrl+N',
                    click: () => {
                        // TODO: 触发新建任务
                        WindowManager.send('menu:new-task');
                    }
                },
                { type: 'separator' },
                {
                    label: '导入配置',
                    click: () => {
                        // TODO: 导入配置文件
                        WindowManager.send('menu:import-config');
                    }
                },
                {
                    label: '导出配置',
                    click: () => {
                        // TODO: 导出配置文件
                        WindowManager.send('menu:export-config');
                    }
                }
            ]
        },
        {
            label: '交易',
            submenu: [
                {
                    label: '全部启动',
                    accelerator: 'CmdOrCtrl+R',
                    click: () => {
                        WindowManager.send('menu:start-all-tasks');
                    }
                },
                {
                    label: '全部暂停',
                    accelerator: 'CmdOrCtrl+P',
                    click: () => {
                        WindowManager.send('menu:pause-all-tasks');
                    }
                },
                { type: 'separator' },
                {
                    label: '连接富途牛牛',
                    click: () => {
                        WindowManager.send('menu:connect-futu');
                    }
                },
                {
                    label: '连接华盛通',
                    click: () => {
                        WindowManager.send('menu:connect-huasheng');
                    }
                }
            ]
        },
        {
            label: '窗口',
            submenu: [
                {
                    label: '最小化',
                    accelerator: 'CmdOrCtrl+M',
                    click: () => WindowManager.minimize()
                },
                {
                    label: '缩放',
                    click: () => WindowManager.toggleMaximize()
                },
                { type: 'separator' },
                {
                    label: '重新加载',
                    accelerator: 'CmdOrCtrl+Shift+R',
                    click: () => WindowManager.reload()
                },
                {
                    label: '开发者工具',
                    accelerator: 'F12',
                    click: () => WindowManager.toggleDevTools()
                }
            ]
        },
        {
            label: '帮助',
            submenu: [
                {
                    label: '用户手册',
                    click: () => {
                        // TODO: 打开用户手册
                    }
                },
                {
                    label: '键盘快捷键',
                    click: () => {
                        // TODO: 显示快捷键帮助
                    }
                },
                { type: 'separator' },
                {
                    label: '检查更新',
                    click: () => {
                        // TODO: 检查应用更新
                    }
                }
            ]
        }
    ];

    // macOS 特殊处理
    if (process.platform === 'darwin') {
        template[0].label = app.getName();
        
        // 添加 macOS 特有的窗口操作
        const windowMenu = template.find(menu => menu.label === '窗口');
        if (windowMenu && windowMenu.submenu && Array.isArray(windowMenu.submenu)) {
            windowMenu.submenu.push(
                { type: 'separator' },
                {
                    label: '前置全部窗口',
                    role: 'front'
                }
            );
        }
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
}

/**
 * 创建右键上下文菜单
 */
export function createContextMenu(): Menu {
    const template: Electron.MenuItemConstructorOptions[] = [
        { role: 'undo', label: '撤销' },
        { role: 'redo', label: '重做' },
        { type: 'separator' },
        { role: 'cut', label: '剪切' },
        { role: 'copy', label: '复制' },
        { role: 'paste', label: '粘贴' },
        { role: 'selectAll', label: '全选' }
    ];

    return Menu.buildFromTemplate(template);
}