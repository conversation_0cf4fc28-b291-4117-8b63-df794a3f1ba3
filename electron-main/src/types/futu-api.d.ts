/**
 * 富途API TypeScript声明文件
 * ===========================
 * 基于futu-api包的接口定义TypeScript类型
 */

declare module 'futu-api' {
    // 基础安全信息
    interface Security {
        market: number;
        code: string;
    }

    // 连接配置
    interface ConnectionConfig {
        host?: string;
        port?: number;
        useHttps?: boolean;
        encryptKey?: string;
    }

    // 快照请求参数
    interface GetSecuritySnapshotRequest {
        c2s: {
            securityList: Security[];
        };
    }

    // 快照响应数据
    interface SecuritySnapshot {
        basic?: {
            security?: Security;
            curPrice?: number;
            priceChangeVal?: number;
            changePct?: number;
            volume?: any; // Long类型
            turnover?: number;
            highPrice?: number;
            lowPrice?: number;
            openPrice?: number;
            lastClosePrice?: number;
        };
    }

    interface GetSecuritySnapshotResponse {
        retType: number;
        retMsg?: string;
        s2c?: {
            snapshotList?: SecuritySnapshot[];
        };
    }

    // 订阅请求参数
    interface SubRequest {
        c2s: {
            securityList: Security[];
            subTypeList: number[];
            isSubOrUnSub: boolean;
            isRegOrUnRegPush: boolean;
        };
    }

    interface SubResponse {
        retType: number;
        retMsg?: string;
    }

    // 买卖盘请求参数
    interface GetOrderBookRequest {
        c2s: {
            security: Security;
            num: number;
        };
    }

    // 买卖盘档位
    interface OrderBookLevel {
        price?: number;
        volume?: number;
        orederCount?: number;
    }

    interface GetOrderBookResponse {
        retType: number;
        retMsg?: string;
        s2c?: {
            security?: Security;
            orderBookBidList?: OrderBookLevel[];
            orderBookAskList?: OrderBookLevel[];
        };
    }

    // 逐笔请求参数
    interface GetTickerRequest {
        c2s: {
            security: Security;
            maxRetNum: number;
        };
    }

    // 逐笔数据
    interface TickerData {
        price?: number;
        volume?: number;
        dir?: number;
        time?: number;
        turnover?: number;
    }

    interface GetTickerResponse {
        retType: number;
        retMsg?: string;
        s2c?: {
            security?: Security;
            tickerList?: TickerData[];
        };
    }

    // 经纪队列请求参数
    interface GetBrokerQueueRequest {
        c2s: {
            security: Security;
        };
    }

    interface GetBrokerQueueResponse {
        retType: number;
        retMsg?: string;
        s2c?: {
            security?: Security;
            orderBookBidList?: any[];
            orderBookAskList?: any[];
        };
    }

    // 推送数据类型
    interface PushData {
        s2c?: {
            basicQotList?: any[];
            security?: Security;
            tickerList?: TickerData[];
            orderBookBidList?: OrderBookLevel[];
            orderBookAskList?: OrderBookLevel[];
        };
    }

    // 富途WebSocket客户端类
    class FutuWebSocket {
        constructor();

        // 连接方法
        start(host: string, port: number, useHttps: boolean, encryptKey: string): void;
        stop(): void;

        // 事件回调
        onlogin: ((ret: boolean, msg: string) => void) | null;
        onPush: ((cmd: string, data: PushData) => void) | null;

        // API方法
        GetSecuritySnapshot(req: GetSecuritySnapshotRequest): Promise<GetSecuritySnapshotResponse>;
        Sub(req: SubRequest): Promise<SubResponse>;
        GetOrderBook(req: GetOrderBookRequest): Promise<GetOrderBookResponse>;
        GetTicker(req: GetTickerRequest): Promise<GetTickerResponse>;
        GetBrokerQueue(req: GetBrokerQueueRequest): Promise<GetBrokerQueueResponse>;
    }

    // 默认导出
    export default FutuWebSocket;

    // 命名导出（如果有的话）
    export {
        FutuWebSocket,
        Security,
        ConnectionConfig,
        GetSecuritySnapshotRequest,
        GetSecuritySnapshotResponse,
        SubRequest,
        SubResponse,
        GetOrderBookRequest,
        GetOrderBookResponse,
        GetTickerRequest,
        GetTickerResponse,
        GetBrokerQueueRequest,
        GetBrokerQueueResponse,
        PushData,
        SecuritySnapshot,
        OrderBookLevel,
        TickerData
    };
}