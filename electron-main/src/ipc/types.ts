// IPC 通信类型定义
import { TaskCreateOptions, TaskStatus, Task } from '@/trading/task-types';

// API 响应类型
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data: T | null;
    error?: string;
}

// 交易系统状态
export interface TradingSystemStatus {
    initialized: boolean;
    marketConnected: boolean;
    tradingConnected: boolean;
    activeTasksCount: number;
    lastUpdate: number;
    error?: string;
}

// 交易系统事件类型
export interface TradingSystemEventData {
    timestamp: number;
    [key: string]: any;
}

// 任务相关类型
export interface TaskListItem {
    id: string;
    status: TaskStatus;
    config: TaskCreateOptions;
}

export interface TaskStatusData {
    taskId: string;
    status: TaskStatus;
}

// 连接状态
export interface ConnectionStatus {
    connected: boolean;
    lastHeartbeat?: number;
    connectionTime?: number;
    error?: string;
}

// 连接配置
export interface ConnectionConfig {
    host?: string;
    port?: number;
    apiKey?: string;
    apiSecret?: string;
    encryptKey?: string;
}

// 行情数据类型
export enum DataType {
    Quote = 'Quote',
    Ticker = 'Ticker',
    OrderBook = 'OrderBook',
    BrokerQueue = 'BrokerQueue'
}

// 报价数据
export interface Quote {
    stockCode: string;
    price: number;
    open: number;
    high: number;
    low: number;
    prevClose: number;
    volume: number;
    amount: number;
    change: number;
    changePercent: number;
    timestamp: number;
}

// 逐笔成交
export interface Ticker {
    stockCode: string;
    price: number;
    volume: number;
    direction: 'BUY' | 'SELL' | 'NEUTRAL';
    timestamp: number;
}

// 买卖盘
export interface OrderBook {
    stockCode: string;
    asks: OrderBookItem[];
    bids: OrderBookItem[];
    timestamp: number;
}

export interface OrderBookItem {
    price: number;
    volume: number;
    orderCount?: number;
}

// 经纪队列
export interface BrokerQueue {
    stockCode: string;
    askBrokers: BrokerItem[];
    bidBrokers: BrokerItem[];
    timestamp: number;
}

export interface BrokerItem {
    brokerId: string;
    brokerName: string;
    volume: number;
}

// 风控配置
export interface RiskControlConfig {
    maxLoss?: number;
    maxPosition?: number;
    stopLossPercent?: number;
    takeProfitPercent?: number;
}

// 任务信息
export interface TaskInfo {
    id: string;
    stockCode: string;
    strategyType: string;
    status: TaskStatus;
    position?: Position;
    profit?: number;
    createdAt: number;
    updatedAt: number;
}

// 持仓信息
export interface Position {
    stockCode: string;
    quantity: number;
    avgCost: number;
    currentPrice: number;
    profit: number;
    profitPercent: number;
}

// 订单信息
export interface Order {
    orderId: string;
    stockCode: string;
    orderType: 'BUY' | 'SELL';
    price: number;
    quantity: number;
    filledQuantity: number;
    status: OrderStatus;
    createdAt: number;
    updatedAt: number;
}

// 订单状态
export enum OrderStatus {
    PENDING = 'pending',
    SUBMITTED = 'submitted',
    PARTIAL_FILLED = 'partial_filled',
    FILLED = 'filled',
    CANCELLED = 'cancelled',
    REJECTED = 'rejected'
}

// IPC 通道定义
export const IPC_CHANNELS = {
    // 交易系统相关
    TRADING_SYSTEM: {
        GET_STATUS: 'trading-system:getStatus',
        INITIALIZE: 'trading-system:initialize', 
        SHUTDOWN: 'trading-system:shutdown',
        GET_MARKET_STATUS: 'trading-system:getMarketStatus',
        GET_TRADING_STATUS: 'trading-system:getTradingStatus'
    },
    
    // 任务管理相关
    TASK: {
        CREATE: 'task:create',
        START: 'task:start',
        STOP: 'task:stop',
        DELETE: 'task:delete',
        GET_LIST: 'task:getList',
        GET_STATUS: 'task:getStatus'
    },

    // 配置管理相关
    CONFIG: {
        GET: 'config:get',
        SET: 'config:set',
        GET_ALL: 'config:getAll'
    },

    // 窗口控制相关
    WINDOW: {
        MINIMIZE: 'window:minimize',
        MAXIMIZE: 'window:maximize',
        CLOSE: 'window:close',
        TOGGLE_DEV_TOOLS: 'window:toggleDevTools',
        RELOAD: 'window:reload'
    },

    // 事件相关
    EVENTS: {
        TRADING_SYSTEM_SERVICE_READY: 'trading-system:service-ready',
        TRADING_SYSTEM_SERVICE_SHUTDOWN: 'trading-system:service-shutdown',
        TRADING_SYSTEM_INITIALIZATION_ERROR: 'trading-system:initialization-error',
        TRADING_SYSTEM_INITIALIZED: 'trading-system:initialized',
        TRADING_SYSTEM_SHUTDOWN: 'trading-system:shutdown',
        TRADING_SYSTEM_HEARTBEAT: 'trading-system:heartbeat',
        TRADING_SYSTEM_TASK_CREATED: 'trading-system:task-created',
        TRADING_SYSTEM_TASK_STARTED: 'trading-system:task-started',
        TRADING_SYSTEM_TASK_STOPPED: 'trading-system:task-stopped',
        REALTIME_DATA: 'event:realtimeData',
        CONNECTION_STATUS: 'event:connectionStatus',
        ERROR: 'event:error'
    }
} as const;