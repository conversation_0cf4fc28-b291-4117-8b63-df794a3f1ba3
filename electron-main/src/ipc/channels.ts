// IPC 通道定义
export enum IPCChannel {
    // 连接管理
    CONNECT = 'trading:connect',
    DISCONNECT = 'trading:disconnect',
    GET_STATUS = 'trading:getStatus',
    
    // 行情数据
    GET_QUOTE = 'market:getQuote',
    GET_ORDER_BOOK = 'market:getOrderBook',
    GET_TICKER = 'market:getTicker',
    GET_BROKER_QUEUE = 'market:getBrokerQueue',
    
    // 实时订阅
    SUBSCRIBE = 'realtime:subscribe',
    UNSUBSCRIBE = 'realtime:unsubscribe',
    
    // 交易操作
    PLACE_ORDER = 'trade:placeOrder',
    CANCEL_ORDER = 'trade:cancelOrder',
    GET_POSITIONS = 'trade:getPositions',
    GET_ORDERS = 'trade:getOrders',
    GET_ACCOUNTS = 'trade:getAccounts',
    
    // 任务管理
    TASK_CREATE = 'task:create',
    TASK_START = 'task:start',
    TASK_STOP = 'task:stop',
    TASK_DELETE = 'task:delete',
    TASK_GET_LIST = 'task:getList',
    TASK_GET_DETAIL = 'task:getDetail',
    
    // 配置管理
    CONFIG_GET = 'config:get',
    CONFIG_SET = 'config:set',
    CONFIG_DELETE = 'config:delete',
    CONFIG_GET_ALL = 'config:getAll',
    
    // 窗口控制
    WINDOW_MINIMIZE = 'window:minimize',
    WINDOW_MAXIMIZE = 'window:maximize',
    WINDOW_CLOSE = 'window:close',
    WINDOW_TOGGLE_DEV_TOOLS = 'window:toggleDevTools',
    WINDOW_RELOAD = 'window:reload',
    
    // 事件推送
    EVENT_REALTIME_DATA = 'event:realtimeData',
    EVENT_CONNECTION_STATUS = 'event:connectionStatus',
    EVENT_ERROR = 'event:error',
    EVENT_TASK_UPDATE = 'event:taskUpdate',
    EVENT_ORDER_UPDATE = 'event:orderUpdate',
    EVENT_POSITION_UPDATE = 'event:positionUpdate'
}