/**
 * 判断是否为开发环境
 */
export function isDev(): boolean {
    // 在打包后的应用中，通过检查是否存在app.asar来判断生产环境
    return process.env.NODE_ENV === 'development' && !process.resourcesPath?.includes('app.asar');
}

/**
 * 判断是否为生产环境
 */
export function isProd(): boolean {
    return process.env.NODE_ENV === 'production';
}

/**
 * 获取平台信息
 */
export function getPlatform(): NodeJS.Platform {
    return process.platform;
}

/**
 * 判断是否为 macOS
 */
export function isMac(): boolean {
    return process.platform === 'darwin';
}

/**
 * 判断是否为 Windows
 */
export function isWindows(): boolean {
    return process.platform === 'win32';
}

/**
 * 判断是否为 Linux
 */
export function isLinux(): boolean {
    return process.platform === 'linux';
}