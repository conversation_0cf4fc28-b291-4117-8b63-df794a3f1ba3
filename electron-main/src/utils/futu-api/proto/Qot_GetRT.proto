syntax = "proto2";
package Qot_GetRT;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetrt";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	required Qot_Common.Security security = 1; //股票
}

message S2C
{
	required Qot_Common.Security security = 1; //股票
	optional string name = 3; //股票名称
	repeated Qot_Common.TimeShare rtList = 2; //分时点
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
