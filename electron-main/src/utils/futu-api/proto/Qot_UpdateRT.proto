syntax = "proto2";
package Qot_UpdateRT;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotupdatert";

import "Common.proto";
import "Qot_Common.proto";

message S2C
{
	required Qot_Common.Security security = 1;
	optional string name = 3; //股票名称
	repeated Qot_Common.TimeShare rtList = 2; //推送的分时点
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
