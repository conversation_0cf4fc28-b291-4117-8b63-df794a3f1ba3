syntax = "proto2";
package Qot_GetRehab;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotgetrehab";

import "Common.proto";
import "Qot_Common.proto";

message C2S
{
	repeated Qot_Common.Security securityList = 1; //股票
}

message SecurityRehab
{
	required Qot_Common.Security security = 1; //股票
	repeated Qot_Common.Rehab rehabList = 2; //复权信息
}

message S2C
{
	repeated SecurityRehab securityRehabList = 1; //多支股票的复权信息
}

message Request
{
	required C2S c2s = 1;
}

message Response
{
	required int32 retType = 1 [default = -400]; //RetType,返回结果
	optional string retMsg = 2;
	optional int32 errCode = 3;
	
	optional S2C s2c = 4;
}
