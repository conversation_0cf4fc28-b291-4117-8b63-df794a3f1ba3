syntax = "proto2";
package Qot_Common;
option java_package = "com.futu.openapi.pb";
option go_package = "github.com/futuopen/ftapi4go/pb/qotcommon";

import "Common.proto";

enum QotMarket
{
    QotMarket_Unknown = 0; //未知市场
    QotMarket_HK_Security = 1; //香港市场
    QotMarket_HK_Future = 2; //港期货(已废弃，使用QotMarket_HK_Security即可)
    QotMarket_US_Security = 11; //美国市场
    QotMarket_CNSH_Security = 21; //沪股市场
    QotMarket_CNSZ_Security = 22; //深股市场
	QotMarket_SG_Security = 31; //新加坡市场
	QotMarket_JP_Security = 41; //日本市场
	QotMarket_AU_Security = 51; //澳大利亚市场
	QotMarket_MY_Security = 61; //马来西亚市场
	QotMarket_CA_Security = 71; // 加拿大市场
	QotMarket_FX_Security = 81; // 外汇市场
}

enum SecurityType
{
	SecurityType_Unknown = 0; //未知
    SecurityType_Bond = 1; //债券
    SecurityType_Bwrt = 2; //一揽子权证
    SecurityType_Eqty = 3; //正股
    SecurityType_Trust = 4; //信托,基金
    SecurityType_Warrant = 5; //窝轮
    SecurityType_Index = 6; //指数
    SecurityType_Plate = 7; //板块
    SecurityType_Drvt = 8; //期权
    SecurityType_PlateSet = 9; //板块集
	SecurityType_Future = 10; //期货
}

enum PlateSetType
{
	PlateSetType_All = 0; //所有板块
	PlateSetType_Industry = 1; //行业板块
	PlateSetType_Region = 2; //地域板块,港美股市场的地域分类数据暂为空
	PlateSetType_Concept = 3; //概念板块
	PlateSetType_Other = 4; //其他板块, 仅用于3207（获取股票所属板块）协议返回,不可作为其他协议的请求参数
}

enum WarrantType
{
	WarrantType_Unknown = 0; //未知
	WarrantType_Buy = 1; //认购
	WarrantType_Sell = 2; //认沽
	WarrantType_Bull = 3; //牛
	WarrantType_Bear = 4; //熊
	WarrantType_InLine = 5; //界内证
};

enum OptionType
{
	OptionType_Unknown = 0; //未知
	OptionType_Call = 1; //涨
	OptionType_Put = 2; //跌
};

enum IndexOptionType
{
	IndexOptionType_Unknown = 0; //未知
	IndexOptionType_Normal = 1; //正常普通的指数期权
	IndexOptionType_Small = 2; //小型指数期权
}

enum OptionAreaType
{
	OptionAreaType_Unknown = 0; //未知
	OptionAreaType_American = 1; //美式
	OptionAreaType_European = 2; //欧式
	OptionAreaType_Bermuda = 3; //百慕大
}

enum QotMarketState
{
	QotMarketState_None = 0; // 无交易
	QotMarketState_Auction = 1; // 竞价 
	QotMarketState_WaitingOpen = 2; // 早盘前等待开盘
	QotMarketState_Morning = 3; // 早盘 
	QotMarketState_Rest = 4; // 午间休市 
	QotMarketState_Afternoon = 5; // 午盘 
	QotMarketState_Closed = 6; // 收盘
	QotMarketState_PreMarketBegin = 8; // 盘前
	QotMarketState_PreMarketEnd = 9; // 盘前结束 
	QotMarketState_AfterHoursBegin = 10; // 盘后
	QotMarketState_AfterHoursEnd = 11; // 盘后结束	 
	QotMarketState_FUTU_SWITCH_DATE      = 12;
	QotMarketState_NightOpen = 13; // 夜市开盘 
	QotMarketState_NightEnd = 14; // 夜市收盘 
	QotMarketState_FutureDayOpen = 15; // 期货日市开盘 
	QotMarketState_FutureDayBreak = 16; // 期货日市休市 
	QotMarketState_FutureDayClose = 17; // 期货日市收盘 
	QotMarketState_FutureDayWaitForOpen = 18; // 期货日市等待开盘 
	QotMarketState_HkCas = 19; // 盘后竞价,港股市场增加CAS机制对应的市场状态	
	QotMarketState_FutureNightWait = 20; // 夜市等待开盘（已废弃）
	QotMarketState_FutureAfternoon = 21; // 期货下午开盘（已废弃）
	//美国期货新增加状态
	QotMarketState_FutureSwitchDate = 22; // 期货切交易日（已废弃）
	QotMarketState_FutureOpen = 23; // 期货开盘
	QotMarketState_FutureBreak = 24; // 期货中盘休息
	QotMarketState_FutureBreakOver = 25; // 期货休息后开盘
	QotMarketState_FutureClose = 26; // 期货收盘
	//科创板新增状态
    QotMarketState_StibAfterHoursWait = 27; // 科创板的盘后撮合时段（已废弃）
    QotMarketState_StibAfterHoursBegin = 28; // 科创板的盘后交易开始（已废弃）
    QotMarketState_StibAfterHoursEnd = 29; // 科创板的盘后交易结束（已废弃）

    QotMarketState_CLOSE_AUCTION         = 30;  // 收市竞价
    QotMarketState_AFTERNOON_END         = 31;  // 已收盘
    QotMarketState_NIGHT                 = 32;  // 交易中
    QotMarketState_OVERNIGHT_BEGIN       = 33;  // 夜盘开始
    QotMarketState_OVERNIGHT_END         = 34;  // 夜盘结束
    QotMarketState_TRADE_AT_LAST         = 35;  // 收盘前成交（在交易时间表内）
    QotMarketState_TRADE_AUCTION         = 36;  // 收盘前的竞价 （在交易时间表内）
	QotMarketState_OVERNIGHT         	 = 37;  // 美股夜盘交易时段
}

//交易日查询市场
enum TradeDateMarket
{
	TradeDateMarket_Unknown = 0; //未知
	TradeDateMarket_HK = 1; //港股市场
	TradeDateMarket_US = 2; //美股市场
	TradeDateMarket_CN = 3; //A股市场
	TradeDateMarket_NT = 4; //深（沪）股通
	TradeDateMarket_ST = 5; //港股通（深、沪）
	TradeDateMarket_JP_Future = 6; //日本期货
	TradeDateMarket_SG_Future = 7; //新加坡期货
}

//交易日类型
enum TradeDateType
{
	TradeDateType_Whole = 0; //全天交易
	TradeDateType_Morning = 1; //上午交易，下午休市
	TradeDateType_Afternoon = 2; //下午交易，上午休市
}			

enum RehabType
{
	RehabType_None = 0; //不复权
	RehabType_Forward = 1; //前复权
	RehabType_Backward = 2; //后复权
}

 //枚举值兼容旧协议定义
enum KLType
{
	KLType_Unknown = 0; //未知
	KLType_1Min = 1; //1分K
	KLType_Day = 2; //日K
	KLType_Week = 3; //周K
	KLType_Month = 4; //月K	
	KLType_Year = 5; //年K
	KLType_5Min = 6; //5分K
	KLType_15Min = 7; //15分K
	KLType_30Min = 8; //30分K
	KLType_60Min = 9; //60分K		
	KLType_3Min = 10; //3分K
	KLType_Quarter = 11; //季K
}

enum KLFields
{
	KLFields_None = 0; //
	KLFields_High = 1; //最高价
	KLFields_Open = 2; //开盘价
	KLFields_Low = 4; //最低价
	KLFields_Close = 8; //收盘价
	KLFields_LastClose = 16; //昨收价
	KLFields_Volume = 32; //成交量
	KLFields_Turnover = 64; //成交额
	KLFields_TurnoverRate = 128; //换手率
	KLFields_PE = 256; //市盈率
	KLFields_ChangeRate = 512; //涨跌幅
}

 //订阅类型
 //枚举值兼容旧协议定义
enum SubType
{
	SubType_None = 0;
	SubType_Basic = 1; //基础报价
	SubType_OrderBook = 2; //摆盘
	SubType_Ticker = 4; //逐笔
	SubType_RT = 5; //分时
	SubType_KL_Day = 6; //日K
	SubType_KL_5Min = 7; //5分K
	SubType_KL_15Min = 8; //15分K
	SubType_KL_30Min = 9; //30分K
	SubType_KL_60Min = 10; //60分K
	SubType_KL_1Min = 11; //1分K
	SubType_KL_Week = 12; //周K
	SubType_KL_Month = 13; //月K
	SubType_Broker = 14; //经纪队列
	SubType_KL_Qurater = 15; //季K
	SubType_KL_Year = 16; //年K
	SubType_KL_3Min = 17; //3分K
}

enum TickerDirection
{
	TickerDirection_Unknown = 0; //未知
	TickerDirection_Bid = 1; //外盘
	TickerDirection_Ask = 2; //内盘
	TickerDirection_Neutral = 3; //中性盘
}

enum TickerType
{
	TickerType_Unknown = 0; //未知
	TickerType_Automatch = 1; //自动对盘
	TickerType_Late = 2; //开市前成交盘
	TickerType_NoneAutomatch = 3; //非自动对盘
	TickerType_InterAutomatch = 4; //同一证券商自动对盘
	TickerType_InterNoneAutomatch = 5; //同一证券商非自动对盘
	TickerType_OddLot = 6; //碎股交易
	TickerType_Auction = 7; //竞价交易	
	TickerType_Bulk = 8; //批量交易
	TickerType_Crash = 9; //现金交易
	TickerType_CrossMarket = 10; //跨市场交易
	TickerType_BulkSold = 11; //批量卖出
	TickerType_FreeOnBoard = 12; //离价交易
	TickerType_Rule127Or155 = 13; //第127条交易（纽交所规则）或第155条交易
	TickerType_Delay = 14; //延迟交易
	TickerType_MarketCenterClosePrice = 15; //中央收市价
	TickerType_NextDay = 16; //隔日交易
	TickerType_MarketCenterOpening = 17; //中央开盘价交易
	TickerType_PriorReferencePrice = 18; //前参考价
	TickerType_MarketCenterOpenPrice = 19; //中央开盘价
	TickerType_Seller = 20; //卖方
	TickerType_T = 21; //T类交易(盘前和盘后交易)
	TickerType_ExtendedTradingHours = 22; //延长交易时段
	TickerType_Contingent = 23; //合单交易
	TickerType_AvgPrice = 24; //平均价成交
	TickerType_OTCSold = 25; //场外售出
	TickerType_OddLotCrossMarket = 26; //碎股跨市场交易
	TickerType_DerivativelyPriced = 27; //衍生工具定价
	TickerType_ReOpeningPriced = 28; //再开盘定价
	TickerType_ClosingPriced = 29; //收盘定价
	TickerType_ComprehensiveDelayPrice = 30; //综合延迟价格
	TickerType_Overseas = 31; //交易的一方不是香港交易所的成员，属于场外交易
}

enum DarkStatus
{
	DarkStatus_None = 0; //无暗盘交易
	DarkStatus_Trading = 1; //暗盘交易中
	DarkStatus_End = 2; //暗盘交易结束
}

enum SecurityStatus
{
	SecurityStatus_Unknown = 0; //未知
	SecurityStatus_Normal = 1; //正常状态
	SecurityStatus_Listing = 2; //待上市
	SecurityStatus_Purchasing = 3; //申购中
	SecurityStatus_Subscribing = 4; //认购中
	SecurityStatus_BeforeDrakTradeOpening = 5; //暗盘开盘前
	SecurityStatus_DrakTrading = 6; //暗盘交易中
	SecurityStatus_DrakTradeEnd = 7; //暗盘已收盘
	SecurityStatus_ToBeOpen = 8; //待开盘
	SecurityStatus_Suspended = 9; //停牌
	SecurityStatus_Called = 10; //已收回
	SecurityStatus_ExpiredLastTradingDate = 11; //已过最后交易日
	SecurityStatus_Expired = 12; //已过期
	SecurityStatus_Delisted = 13; //已退市
	SecurityStatus_ChangeToTemporaryCode = 14; //公司行动中，交易关闭，转至临时代码交易
	SecurityStatus_TemporaryCodeTradeEnd = 15; //临时买卖结束，交易关闭
	SecurityStatus_ChangedPlateTradeEnd = 16; //已转板，旧代码交易关闭
	SecurityStatus_ChangedCodeTradeEnd = 17; //已换代码，旧代码交易关闭
	SecurityStatus_RecoverableCircuitBreaker = 18; //可恢复性熔断
	SecurityStatus_UnRecoverableCircuitBreaker = 19; //不可恢复性熔断
	SecurityStatus_AfterCombination = 20; //盘后撮合
	SecurityStatus_AfterTransation = 21; //盘后交易
}

enum HolderCategory
{
	HolderCategory_Unknow = 0; //未知
	HolderCategory_Agency = 1; //机构
	HolderCategory_Fund = 2; //基金
	HolderCategory_SeniorManager = 3; //高管
}

//推送数据的分类，目前只有逐笔在使用
enum PushDataType
{
	PushDataType_Unknow = 0;
	PushDataType_Realtime = 1; //实时推送的数据
	PushDataType_ByDisConn = 2; //对后台行情连接断开期间拉取补充的数据 最多50个
	PushDataType_Cache = 3; //非实时非连接断开补充数据
}

//窝轮排序
enum SortField
{
	SortField_Unknow = 0;
    SortField_Code = 1; //代码
    SortField_CurPrice = 2; //最新价
	SortField_PriceChangeVal = 3; //涨跌额
    SortField_ChangeRate = 4; //涨跌幅%
	SortField_Status = 5; //状态
    SortField_BidPrice = 6; //买入价
    SortField_AskPrice = 7; //卖出价
    SortField_BidVol = 8; //买量
    SortField_AskVol = 9; //卖量
    SortField_Volume = 10; //成交量
    SortField_Turnover = 11; //成交额
	SortField_Amplitude = 30; //振幅%

	//以下排序字段只支持用于Qot_GetWarrant协议
	SortField_Score = 12; //综合评分
    SortField_Premium = 13; //溢价%
	SortField_EffectiveLeverage = 14; //有效杠杆
    SortField_Delta = 15; //对冲值,仅认购认沽支持该字段
    SortField_ImpliedVolatility = 16; //引伸波幅,仅认购认沽支持该字段
    SortField_Type = 17; //类型
    SortField_StrikePrice = 18; //行权价
    SortField_BreakEvenPoint = 19; //打和点
    SortField_MaturityTime = 20; //到期日
	SortField_ListTime = 21; //上市日期
    SortField_LastTradeTime = 22; //最后交易日
    SortField_Leverage = 23; //杠杆比率
    SortField_InOutMoney = 24; //价内/价外%
    SortField_RecoveryPrice = 25; //收回价,仅牛熊证支持该字段
	SortField_ChangePrice = 26; // 换股价
    SortField_Change = 27; //换股比率
    SortField_StreetRate = 28; //街货比%
	SortField_StreetVol = 29; //街货量
	SortField_WarrantName = 31; // 窝轮名称
    SortField_Issuer = 32; //发行人
	SortField_LotSize = 33; // 每手
    SortField_IssueSize = 34; //发行量
	SortField_UpperStrikePrice = 45; //上限价，仅用于界内证
	SortField_LowerStrikePrice = 46; //下限价，仅用于界内证
	SortField_InLinePriceStatus = 47; //界内界外，仅用于界内证

	//以下排序字段只支持用于Qot_GetPlateSecurity协议，并仅支持美股
	SortField_PreCurPrice = 35; //盘前最新价
	SortField_AfterCurPrice = 36; //盘后最新价
	SortField_PrePriceChangeVal = 37; //盘前涨跌额
	SortField_AfterPriceChangeVal = 38; //盘后涨跌额
    SortField_PreChangeRate = 39; //盘前涨跌幅%
	SortField_AfterChangeRate = 40; //盘后涨跌幅%
	SortField_PreAmplitude = 41; //盘前振幅%
	SortField_AfterAmplitude = 42; //盘后振幅%
	SortField_PreTurnover = 43; //盘前成交额
	SortField_AfterTurnover = 44; //盘后成交额

	//以下排序字段只支持用于Qot_GetPlateSecurity协议，并仅支持期货
	SortField_LastSettlePrice = 48; //昨结
	SortField_Position = 49; //持仓量
	SortField_PositionChange = 50; //日增仓
}

//窝轮发行人
enum Issuer
{
	Issuer_Unknow = 0; //未知
	Issuer_SG = 1; //法兴
    Issuer_BP = 2; //法巴
    Issuer_CS = 3; //瑞信
    Issuer_CT = 4; //花旗	
    Issuer_EA = 5; //东亚
    Issuer_GS = 6; //高盛
    Issuer_HS = 7; //汇丰
    Issuer_JP = 8; //摩通	
    Issuer_MB = 9; //麦银	
	Issuer_SC = 10; //渣打
	Issuer_UB = 11; //瑞银
	Issuer_BI = 12; //中银
	Issuer_DB = 13; //德银
    Issuer_DC = 14; //大和
	Issuer_ML = 15; //美林
	Issuer_NM = 16; //野村
    Issuer_RB = 17; //荷合
    Issuer_RS = 18; //苏皇	
    Issuer_BC = 19; //巴克莱
    Issuer_HT = 20; //海通
    Issuer_VT = 21; //瑞通
    Issuer_KC = 22; //比联
	Issuer_MS = 23; //摩利
	Issuer_GJ = 24; //国君
	Issuer_XZ = 25; //星展
	Issuer_HU = 26; //华泰
	Issuer_KS = 27; //韩投
	Issuer_CI = 28; //信证
}

//窝轮上市日
enum IpoPeriod
{
	IpoPeriod_Unknow = 0; //未知
    IpoPeriod_Today = 1; //今日上市
    IpoPeriod_Tomorrow = 2; //明日上市
    IpoPeriod_Nextweek = 3; //未来一周上市
    IpoPeriod_Lastweek = 4; //过去一周上市
    IpoPeriod_Lastmonth = 5; //过去一月上市
}

//窝轮价外/内,界内证表示界内界外
enum PriceType
{
	PriceType_Unknow = 0;
	PriceType_Outside = 1; //价外，界内证表示界外
	PriceType_WithIn = 2; //价内，界内证表示界内
}

//窝轮状态
enum WarrantStatus
{
    WarrantStatus_Unknow = 0; //未知
    WarrantStatus_Normal = 1; //正常状态
	WarrantStatus_Suspend = 2; //停牌
    WarrantStatus_StopTrade = 3; //终止交易
    WarrantStatus_PendingListing = 4; //等待上市
}

enum CompanyAct
{
	CompanyAct_None = 0; //无
	CompanyAct_Split = 1; //拆股		
	CompanyAct_Join = 2; //合股
	CompanyAct_Bonus = 4; //送股
	CompanyAct_Transfer = 8; //转赠股
	CompanyAct_Allot = 16; //配股	
	CompanyAct_Add = 32; //增发股
	CompanyAct_Dividend = 64; //现金分红
	CompanyAct_SPDividend = 128; //特别股息	
}

//行情权限
enum QotRight
{
	QotRight_Unknow = 0; //未知
	QotRight_Bmp = 1; //Bmp，无法订阅
	QotRight_Level1 = 2; //Level1
	QotRight_Level2 = 3; //Level2
	QotRight_SF = 4; //SF高级行情
	QotRight_No = 5; //无权限
}

// 提醒类型
enum PriceReminderType
{
    PriceReminderType_Unknown = 0; // 未知
    PriceReminderType_PriceUp = 1; // 价格涨到
    PriceReminderType_PriceDown = 2; // 价格跌到
    PriceReminderType_ChangeRateUp = 3; // 日涨幅超（该字段为百分比字段，设置时填 20 表示 20%）
    PriceReminderType_ChangeRateDown = 4; // 日跌幅超（该字段为百分比字段，设置时填 20 表示 20%）
    PriceReminderType_5MinChangeRateUp = 5; // 5 分钟涨幅超（该字段为百分比字段，设置时填 20 表示 20%）
    PriceReminderType_5MinChangeRateDown = 6; // 5 分钟跌幅超（该字段为百分比字段，设置时填 20 表示 20%）
    PriceReminderType_VolumeUp = 7; // 成交量超过
    PriceReminderType_TurnoverUp = 8; // 成交额超过
    PriceReminderType_TurnoverRateUp = 9; // 换手率超过（该字段为百分比字段，设置时填 20 表示 20%）
    PriceReminderType_BidPriceUp = 10; // 买一价高于
    PriceReminderType_AskPriceDown = 11; // 卖一价低于
    PriceReminderType_BidVolUp = 12; // 买一量高于    
    PriceReminderType_AskVolUp = 13; // 卖一量高于
    PriceReminderType_3MinChangeRateUp = 14; // 3 分钟涨幅超（该字段为百分比字段，设置时填 20 表示 20%）
    PriceReminderType_3MinChangeRateDown = 15; // 3 分钟跌幅超（该字段为百分比字段，设置时填 20 表示 20%）
}

// 提醒频率
enum PriceReminderFreq
{
	PriceReminderFreq_Unknown = 0; // 未知
    PriceReminderFreq_Always = 1; // 持续提醒
    PriceReminderFreq_OnceADay = 2; // 每日一次
    PriceReminderFreq_OnlyOnce = 3; // 仅提醒一次
}

// 资产类别
enum AssetClass
{
	AssetClass_Unknow = 0; //未知
	AssetClass_Stock = 1; //股票
	AssetClass_Bond = 2; //债券
	AssetClass_Commodity = 3; //商品
	AssetClass_CurrencyMarket = 4; //货币市场
	AssetClass_Future = 5; //期货
	AssetClass_Swap = 6; //掉期
}

// 交割周期
enum ExpirationCycle
{
	ExpirationCycle_Unknown = 0; //未知
	ExpirationCycle_Week = 1; //周期权
	ExpirationCycle_Month = 2; //月期权
	ExpirationCycle_MonthEnd = 3;  // 月末期权
	ExpirationCycle_Quarter = 4; //季度期权
	ExpirationCycle_WeekMon = 11; //周一
	ExpirationCycle_WeekTue = 12; //周二
	ExpirationCycle_WeekWed = 13; //周三
	ExpirationCycle_WeekThu = 14; //周四
	ExpirationCycle_WeekFri = 15; //周五
}

// 标准期权链类型
enum OptionStandardType
{
	OptionStandardType_Unknown = 0; //未知
	OptionStandardType_Standard = 1; // 标准
	OptionStandardType_NonStandard = 2; // 非标准
}

// 期权结算方式
enum OptionSettlementMode
{
	OptionSettlementMode_Unknown = 0; //未知
	OptionSettlementMode_AM = 1; // AM
	OptionSettlementMode_PM = 2; // PM
}

// 所属交易所
enum ExchType
{
    ExchType_Unknown = 0; //未知
    ExchType_HK_MainBoard = 1; // 港交所·主板
    ExchType_HK_GEMBoard = 2; //港交所·创业板
    ExchType_HK_HKEX = 3; //港交所
    ExchType_US_NYSE = 4; //纽交所
    ExchType_US_Nasdaq = 5; //纳斯达克
    ExchType_US_Pink = 6; //OTC 市场
    ExchType_US_AMEX = 7; //美交所 
    ExchType_US_Option = 8; //美国（仅美股期权适用）
    ExchType_US_NYMEX = 9; //NYMEX 
    ExchType_US_COMEX = 10; //COMEX
    ExchType_US_CBOT = 11; //CBOT
    ExchType_US_CME = 12; //CME
    ExchType_US_CBOE = 13; //CBOE
    ExchType_CN_SH = 14; //上交所  
    ExchType_CN_SZ = 15; //深交所
    ExchType_CN_STIB = 16; //科创板
    ExchType_SG_SGX = 17; //新交所
    ExchType_JP_OSE = 18; //大阪交易所 
}

// 周期类型
enum PeriodType
{
    PeriodType_Unknown = 0; // 未知
    PeriodType_INTRADAY = 1; // 实时
	PeriodType_DAY = 2; // 日
	PeriodType_WEEK = 3; // 周
	PeriodType_MONTH = 4; // 月
}

enum PriceReminderMarketStatus
{
	PriceReminderMarketStatus_Unknow = 0;
	PriceReminderMarketStatus_Open = 1; // 盘中
	PriceReminderMarketStatus_USPre = 2;  // 美股盘前
	PriceReminderMarketStatus_USAfter = 3; // 美股盘后
	PriceReminderMarketStatus_USOverNight = 4; // 美股夜盘
}

 //两个字段确定一支股票
message Security
{
	required int32 market = 1; //QotMarket,股票市场
	required string code = 2; //股票代码
}

message KLine
{
	required string time = 1; //时间戳字符串
	required bool isBlank = 2; //是否是空内容的点,若为ture则只有时间信息
	optional double highPrice = 3; //最高价
	optional double openPrice = 4; //开盘价
	optional double lowPrice = 5; //最低价
	optional double closePrice = 6; //收盘价
	optional double lastClosePrice = 7; //昨收价
	optional int64 volume = 8; //成交量
	optional double turnover = 9; //成交额
	optional double turnoverRate = 10; //换手率（该字段为百分比字段，展示为小数表示）
	optional double pe = 11; //市盈率
	optional double changeRate = 12; //涨跌幅（该字段为百分比字段，默认不展示%，如20实际对应20%）
	optional double timestamp = 13; //时间戳
}

message OptionBasicQotExData
{
	required double strikePrice = 1; //行权价
	required int32 contractSize = 2; //每份合约数(整型数据)
	optional double contractSizeFloat = 17; //每份合约数（浮点型数据）
	required int32 openInterest = 3; //未平仓合约数
	required double impliedVolatility = 4; //隐含波动率（该字段为百分比字段，默认不展示%，如20实际对应20%）
	required double premium = 5; //溢价（该字段为百分比字段，默认不展示%，如20实际对应20%）
	required double delta = 6; //希腊值 Delta
    required double gamma = 7; //希腊值 Gamma
	required double vega = 8; //希腊值 Vega
    required double theta = 9; //希腊值 Theta
    required double rho = 10; //希腊值 Rho
	optional int32 netOpenInterest = 11; //净未平仓合约数，仅港股期权适用
	optional int32 expiryDateDistance = 12; //距离到期日天数，负数表示已过期
	optional double contractNominalValue = 13; //合约名义金额，仅港股期权适用
	optional double ownerLotMultiplier = 14; //相等正股手数，指数期权无该字段，仅港股期权适用
	optional int32 optionAreaType = 15; //OptionAreaType，期权类型（按行权时间）
	optional double contractMultiplier = 16; //合约乘数
	optional int32 indexOptionType = 18; //IndexOptionType，指数期权类型
}

//美股支持盘前盘后数据
//科创板仅支持盘后数据：成交量，成交额
message PreAfterMarketData
{
	optional double price = 1;  // 盘前或盘后 - 价格
    optional double highPrice = 2;  // 盘前或盘后 - 最高价
    optional double lowPrice = 3;  // 盘前或盘后 - 最低价
    optional int64 volume = 4;  // 盘前或盘后 - 成交量
    optional double turnover = 5;  // 盘前或盘后 - 成交额
    optional double changeVal = 6;  // 盘前或盘后 - 涨跌额
    optional double changeRate = 7;  // 盘前或盘后 - 涨跌幅（该字段为百分比字段，默认不展示%，如20实际对应20%）
    optional double amplitude = 8;  // 盘前或盘后 - 振幅（该字段为百分比字段，默认不展示%，如20实际对应20%）
}

message FutureBasicQotExData
{
	required double lastSettlePrice = 1; //昨结
	required int32 position = 2; //持仓量
	required int32 positionChange = 3; //日增仓
	optional int32 expiryDateDistance = 4; //距离到期日天数
}

message WarrantBasicQotExData
{
    optional double delta = 1; //对冲值,仅认购认沽支持该字段
    optional double impliedVolatility = 2; //引申波幅,仅认购认沽支持该字段
    required double premium = 3; //溢价（该字段为百分比字段，默认不展示%，如20实际对应20%）
}

message BasicQot
{
	required Security security = 1; //股票
	optional string name = 24; // 股票名称
	required bool isSuspended = 2; //是否停牌
	required string listTime = 3; //上市日期字符串
	required double priceSpread = 4; //价差
	required string updateTime = 5; //最新价的更新时间字符串，对其他字段不适用
	required double highPrice = 6; //最高价
	required double openPrice = 7; //开盘价
	required double lowPrice = 8; //最低价
	required double curPrice = 9; //最新价
	required double lastClosePrice = 10; //昨收价
	required int64 volume = 11; //成交量
	required double turnover = 12; //成交额
	required double turnoverRate = 13; //换手率（该字段为百分比字段，默认不展示%，如20实际对应20%）
	required double amplitude = 14; //振幅（该字段为百分比字段，默认不展示%，如20实际对应20%）
	optional int32 darkStatus = 15; //DarkStatus, 暗盘交易状态	
	optional OptionBasicQotExData optionExData = 16; //期权特有字段
	optional double listTimestamp = 17; //上市日期时间戳
	optional double updateTimestamp = 18; //最新价的更新时间戳，对其他字段不适用
	optional PreAfterMarketData preMarket = 19; //盘前数据
	optional PreAfterMarketData afterMarket = 20; //盘后数据
	optional int32 secStatus = 21; //SecurityStatus, 股票状态
	optional FutureBasicQotExData futureExData = 22; //期货特有字段
	optional WarrantBasicQotExData warrantExData = 23; //窝轮特有字段
	optional PreAfterMarketData overnight = 25; //夜盘数据
}

message TimeShare
{
	required string time = 1; //时间字符串
	required int32 minute = 2; //距离0点过了多少分钟
	required bool isBlank = 3; //是否是空内容的点,若为ture则只有时间信息
	optional double price = 4; //当前价
	optional double lastClosePrice = 5; //昨收价
	optional double avgPrice = 6; //均价
	optional int64 volume = 7; //成交量
	optional double turnover = 8; //成交额
	optional double timestamp = 9; //时间戳
}

message SecurityStaticBasic
{
	required Qot_Common.Security security = 1; //股票
	required int64 id = 2; //股票ID
	required int32 lotSize = 3; //每手数量,期权以及期货类型表示合约乘数
	required int32 secType = 4; //Qot_Common.SecurityType,股票类型
	required string name = 5; //股票名字
	required string listTime = 6; //上市时间字符串
	optional bool delisting = 7; //是否退市
	optional double listTimestamp = 8; //上市时间戳
	optional int32 exchType = 9; //Qot_Common.ExchType,所属交易所
}

message WarrantStaticExData
{
	required int32 type = 1; //Qot_Common.WarrantType,窝轮类型
	required Qot_Common.Security owner = 2; //所属正股
}

message OptionStaticExData
{
	required int32 type = 1; //Qot_Common.OptionType,期权
	required Qot_Common.Security owner = 2; //标的股
	required string strikeTime = 3; //行权日
	required double strikePrice = 4; //行权价
	required bool suspend = 5; //是否停牌
	required string market = 6; //发行市场名字
	optional double strikeTimestamp = 7; //行权日时间戳
	optional int32 indexOptionType = 8; //Qot_Common.IndexOptionType, 指数期权的类型，仅在指数期权有效
	optional int32 expirationCycle = 9; // ExpirationCycle，交割周期
	optional int32 optionStandardType = 10; // OptionStandardType，标准期权
	optional int32 optionSettlementMode = 11; // OptionSettlementMode，结算方式
}

message FutureStaticExData
{
	required string lastTradeTime = 1; //最后交易日，只有非主连期货合约才有该字段
	optional double lastTradeTimestamp = 2; //最后交易日时间戳，只有非主连期货合约才有该字段
	required bool isMainContract = 3; //是否主连合约
}

message SecurityStaticInfo
{
	required SecurityStaticBasic basic = 1; //基本股票静态信息
	optional WarrantStaticExData warrantExData = 2; //窝轮额外股票静态信息
	optional OptionStaticExData optionExData = 3; //期权额外股票静态信息
	optional FutureStaticExData futureExData = 4; //期货额外股票静态信息
}

message Broker
{
	required int64 id = 1; //经纪ID
	required string name = 2; //经纪名称
	required int32 pos = 3; //经纪档位

	//以下为SF行情特有字段
	optional int64 orderID = 4; //交易所订单ID，与交易接口返回的订单ID并不一样
	optional int64 volume = 5; //订单股数
}

message Ticker
{
	required string time = 1; //时间字符串
	required int64 sequence = 2; // 唯一标识
	required int32 dir = 3; //TickerDirection, 买卖方向
	required double price = 4; //价格
	required int64 volume = 5; //成交量
	required double turnover = 6; //成交额
	optional double recvTime = 7; //收到推送数据的本地时间戳，用于定位延迟
	optional int32 type = 8; //TickerType, 逐笔类型
	optional int32 typeSign = 9; //逐笔类型符号
	optional int32 pushDataType = 10; //用于区分推送情况
	optional double timestamp = 11; //时间戳
}

message OrderBookDetail
{
	required int64 orderID = 1; //交易所订单ID，与交易接口返回的订单ID并不一样
	required int64 volume = 2; //订单股数
}

message OrderBook
{
	required double price = 1; //委托价格
	required int64 volume = 2; //委托数量
	required int32 orederCount = 3; //委托订单个数
	repeated OrderBookDetail detailList = 4; //订单信息，SF行情特有
}

//持股变动
message ShareHoldingChange
{
    required string holderName = 1; //持有者名称（机构名称 或 基金名称 或 高管姓名）
    required double holdingQty = 2; //当前持股数量
    required double holdingRatio = 3; //当前持股百分比（该字段为百分比字段，默认不展示%，如20实际对应20%）
    required double changeQty = 4; //较上一次变动数量
	required double changeRatio = 5; //较上一次变动百分比（该字段为百分比字段，默认不展示%，如20实际对应20%。是相对于自身的比例，而不是总的。如总股本1万股，持有100股，持股百分比是1%，卖掉50股，变动比例是50%，而不是0.5%）
    required string time = 6; //发布时间(YYYY-MM-DD HH:MM:SS字符串)
	optional double timestamp = 7; //时间戳
}

message SubInfo
{
	required int32 subType = 1; //Qot_Common.SubType,订阅类型
	repeated Qot_Common.Security securityList = 2; //订阅该类型行情的股票
}

message ConnSubInfo
{
	repeated SubInfo subInfoList = 1; //该连接订阅信息
	required int32 usedQuota = 2; //该连接已经使用的订阅额度
	required bool isOwnConnData = 3; //用于区分是否是自己连接的数据
}

message PlateInfo
{
	required Qot_Common.Security plate = 1; //板块
	required string name = 2; //板块名字
	optional int32 plateType = 3; //PlateSetType 板块类型, 仅3207（获取股票所属板块）协议返回该字段
}

message Rehab
{
	required string time = 1; //时间字符串
	required int64 companyActFlag = 2; //公司行动(CompanyAct)组合标志位,指定某些字段值是否有效
	required double fwdFactorA = 3; //前复权因子A
	required double fwdFactorB = 4; //前复权因子B
	required double bwdFactorA = 5; //后复权因子A
	required double bwdFactorB = 6; //后复权因子B
	optional int32 splitBase = 7; //拆股(例如，1拆5，Base为1，Ert为5)
	optional int32 splitErt = 8;	
	optional int32 joinBase = 9; //合股(例如，50合1，Base为50，Ert为1)
	optional int32 joinErt = 10;	
	optional int32 bonusBase = 11; //送股(例如，10送3, Base为10,Ert为3)
	optional int32 bonusErt = 12;	
	optional int32 transferBase = 13; //转赠股(例如，10转3, Base为10,Ert为3)
	optional int32 transferErt = 14;	
	optional int32 allotBase = 15; //配股(例如，10送2, 配股价为6.3元, Base为10, Ert为2, Price为6.3)
	optional int32 allotErt = 16;	
	optional double allotPrice = 17;	
	optional int32 addBase = 18; //增发股(例如，10送2, 增发股价为6.3元, Base为10, Ert为2, Price为6.3)
	optional int32 addErt = 19;	
	optional double addPrice = 20;	
	optional double dividend = 21; //现金分红(例如，每10股派现0.5元,则该字段值为0.05)
	optional double spDividend = 22; //特别股息(例如，每10股派特别股息0.5元,则该字段值为0.05)
	optional double timestamp = 23; //时间戳
}