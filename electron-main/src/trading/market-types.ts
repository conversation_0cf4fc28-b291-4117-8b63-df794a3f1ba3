/**
 * 富途牛牛行情适配器类型定义
 * =============================
 * 定义行情数据相关的接口、枚举和类型
 */

// 市场枚举
export enum Market {
    HK = 1,    // 港股
    US = 2,    // 美股
    CN = 3,    // A股
    SG = 4,    // 新加坡
    JP = 5     // 日本
}

// 连接状态枚举
export enum ConnectionStatus {
    Disconnected = 'disconnected',
    Connecting = 'connecting',
    Connected = 'connected',
    Error = 'error'
}

// 数据类型枚举
export enum DataType {
    Quote = 'quote',           // 基础报价
    OrderBook = 'orderbook',   // 买卖盘
    Ticker = 'ticker',         // 逐笔成交
    BrokerQueue = 'brokerqueue', // 经纪队列
    KLine = 'kline'            // K线数据
}

// 富途市场配置接口
export interface FutuMarketConfig {
    host?: string;        // OpenD 主机地址，默认 127.0.0.1
    port?: number;        // OpenD 端口，默认 33333
    useHttps?: boolean;   // 是否使用 HTTPS，默认 false
    encryptKey?: string;  // 加密密钥
}

// 基础报价数据
export interface Quote {
    stockCode: string;     // 股票代码
    market: Market;        // 市场
    price: number;         // 现价
    change: number;        // 涨跌额
    changePercent: number; // 涨跌幅 (%)
    volume: number;        // 成交量
    turnover: number;      // 成交额
    high: number;          // 最高价
    low: number;           // 最低价
    open: number;          // 开盘价
    lastClose: number;     // 昨收价
    timestamp: number;     // 时间戳
}

// 买卖盘档位
export interface OrderBookLevel {
    price: number;         // 价格
    volume: number;        // 数量
    orderCount: number;    // 订单数
}

// 买卖盘数据
export interface OrderBook {
    stockCode: string;           // 股票代码
    market: Market;              // 市场
    bids: OrderBookLevel[];      // 买盘
    asks: OrderBookLevel[];      // 卖盘
    timestamp: number;           // 时间戳
}

// 逐笔成交数据
export interface Ticker {
    price: number;         // 成交价
    volume: number;        // 成交量
    direction: number;     // 成交方向 (1=买入, 2=卖出, 3=中性)
    timestamp: number;     // 成交时间
    turnover: number;      // 成交额
}

// 经纪队列数据
export interface BrokerQueue {
    stockCode: string;     // 股票代码
    market: Market;        // 市场
    bidBrokers: any[];     // 买盘经纪队列
    askBrokers: any[];     // 卖盘经纪队列
    timestamp: number;     // 时间戳
}

// 实时数据统一接口
export interface RealtimeData {
    type: DataType;        // 数据类型
    stockCode: string;     // 股票代码
    market: Market;        // 市场
    data: Quote | OrderBook | Ticker | BrokerQueue; // 具体数据
    timestamp: number;     // 时间戳
}

// 订阅信息
export interface SubscriptionInfo {
    stockCode: string;       // 股票代码
    market: Market;          // 市场
    dataTypes: DataType[];   // 订阅的数据类型
    subscriptionId: string;  // 订阅ID
    isActive: boolean;       // 是否活跃
}

// 行情适配器接口
export interface MarketAdapter {
    // 连接管理
    connect(config: FutuMarketConfig): Promise<void>;
    disconnect(): Promise<void>;
    isConnected(): boolean;
    getConnectionStatus(): ConnectionStatus;

    // 数据获取接口
    getQuote(stockCode: string, market?: Market): Promise<Quote>;
    getOrderBook(stockCode: string, market?: Market): Promise<OrderBook>;
    getTicker(stockCode: string, market?: Market, count?: number): Promise<Ticker[]>;
    getBrokerQueue(stockCode: string, market?: Market): Promise<BrokerQueue>;

    // 实时订阅接口
    subscribe(stockCode: string, market: Market, dataTypes: DataType[]): Promise<string>;
    unsubscribe(stockCode: string, market: Market, dataTypes: DataType[]): Promise<void>;
    
    // 订阅管理
    getActiveSubscriptions(): SubscriptionInfo[];

    // 事件监听
    on(event: 'data', listener: (data: RealtimeData) => void): this;
    on(event: 'connected', listener: () => void): this;
    on(event: 'disconnected', listener: () => void): this;
    on(event: 'error', listener: (error: Error) => void): this;
}

// K线数据类型
export interface KLineData {
    timestamp: number;     // 时间戳
    open: number;          // 开盘价
    high: number;          // 最高价
    low: number;           // 最低价
    close: number;         // 收盘价
    volume: number;        // 成交量
    turnover: number;      // 成交额
}

// 错误类型
export class FutuMarketError extends Error {
    constructor(
        message: string,
        public code?: string,
        public details?: any
    ) {
        super(message);
        this.name = 'FutuMarketError';
    }
}

// 数据过滤器
export interface DataFilter {
    stockCodes?: string[];     // 股票代码过滤
    markets?: Market[];        // 市场过滤
    dataTypes?: DataType[];    // 数据类型过滤
    timeRange?: {              // 时间范围过滤
        start: number;
        end: number;
    };
}

// 统计信息
export interface MarketStatistics {
    totalSubscriptions: number;    // 总订阅数
    activeConnections: number;     // 活跃连接数
    dataReceived: number;          // 已接收数据量
    lastUpdateTime: number;        // 最后更新时间
}