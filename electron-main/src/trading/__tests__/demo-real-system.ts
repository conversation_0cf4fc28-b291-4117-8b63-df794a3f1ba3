/**
 * 交易系统启动脚本
 * ==================
 * 演示如何创建和启动使用真实接口的交易系统
 */

import { TradingSystem } from "../TradingSystem";
import { StrategyType, Market, DataType, LiquidationType, RiskConditionType } from "../task-types";

async function startTradingSystem() {
    console.log('🚀 启动交易系统...\n');

    try {
        // 使用开发环境配置创建交易系统
        console.log('📡 连接到真实服务...');
        const tradingSystem = await TradingSystem.createWithConfig('development');
        
        // 初始化系统
        console.log('🔧 初始化交易系统...');
        await tradingSystem.initialize();
        
        // 检查系统状态
        const systemStatus = tradingSystem.getSystemStatus();
        console.log('📊 系统状态:');
        console.log(`   初始化: ${systemStatus.isInitialized ? '✅' : '❌'}`);
        console.log(`   行情连接: ${systemStatus.marketDataConnected ? '✅' : '❌'}`);
        console.log(`   交易连接: ${systemStatus.tradingConnected ? '✅' : '❌'}`);
        
        // 获取行情数据统计
        const marketStats = tradingSystem.getMarketDataStats();
        console.log('📈 行情数据统计:');
        console.log(`   总订阅数: ${marketStats.totalSubscriptions}`);
        console.log(`   活跃任务: ${marketStats.activeTasks}`);
        console.log(`   缓存大小: ${marketStats.cacheSize}`);
        
        // 获取交易状态
        const tradingStatus = tradingSystem.getTradingStatus();
        console.log('💰 交易状态:');
        for (const [name, status] of Object.entries(tradingStatus)) {
            console.log(`   ${name}: ${status.connected ? '已连接' : '未连接'}`);
        }
        
        // 获取可用策略
        const strategies = tradingSystem.getStrategyTemplates();
        console.log(`\n📋 可用策略: ${strategies.length} 个`);
        strategies.forEach(strategy => {
            console.log(`   • ${strategy.name} (${strategy.category})`);
        });
        
        // 演示创建任务
        console.log('\n🎯 创建示例任务...');
        const taskId = await tradingSystem.createTask({
            name: '腾讯控股大单监控',
            stockCode: 'HK.00700',
            stockName: '腾讯控股',
            market: Market.HK,
            strategyConfig: {
                strategyType: StrategyType.BigOrderMonitor,
                params: {
                    monitorThreshold: 50000,  // 监控5万元以上大单
                    durationSeconds: 3600,   // 监控1小时
                    targetBrokers: ['高盛', '摩根士丹利', '瑞银'],
                    orderSize: 1000          // 每次下单1000股
                },
                requiredDataTypes: [DataType.Quote, DataType.OrderBook, DataType.BrokerQueue]
            },
            riskConfig: {
                triggerLogic: 'any',
                conditions: [
                    {
                        id: 'stop-loss',
                        type: RiskConditionType.StopLoss,
                        params: {
                            stopPrice: 400  // 400元止损
                        }
                    },
                    {
                        id: 'profit-take',
                        type: RiskConditionType.TakeProfit,
                        params: {
                            profitPrice: 450  // 450元止盈
                        }
                    }
                ],
                liquidationStrategy: {
                    type: LiquidationType.SmartExecution,
                    params: {
                        aggressiveness: 'medium',
                        maxSlippageBps: 10
                    }
                }
            }
        });
        
        console.log(`✅ 任务创建成功: ${taskId}`);
        
        // 获取任务详情
        const task = tradingSystem.getTask(taskId);
        if (task) {
            console.log(`📝 任务详情:`);
            console.log(`   名称: ${task.name}`);
            console.log(`   股票: ${task.stockCode} (${task.stockName})`);
            console.log(`   状态: ${task.status}`);
            console.log(`   策略: ${task.strategyConfig.strategyType}`);
        }
        
        // 启动任务
        console.log('\n🚀 启动任务，开始接收真实行情数据...');
        await tradingSystem.startTask(taskId);
        
        console.log('📡 任务已启动，正在接收真实行情数据');
        console.log('⏰ 将运行30秒后自动停止...\n');
        
        // 监控任务运行30秒
        let countdown = 30;
        const monitorInterval = setInterval(async () => {
            const currentTask = tradingSystem.getTask(taskId);
            if (currentTask) {
                console.log(`⏱️  ${countdown}s - 任务状态: ${currentTask.status}, 最后更新: ${new Date(currentTask.updatedAt).toLocaleTimeString()}`);
            }
            
            countdown--;
            if (countdown <= 0) {
                clearInterval(monitorInterval);
                
                // 停止并清理任务
                console.log('\n🛑 停止任务...');
                await tradingSystem.stopTask(taskId);
                await tradingSystem.deleteTask(taskId);
                console.log('✅ 任务已停止并删除');
                
                // 关闭系统
                console.log('\n🔧 关闭交易系统...');
                await tradingSystem.shutdown();
                console.log('✅ 交易系统已关闭');
                
                process.exit(0);
            }
        }, 1000);
        
    } catch (error) {
        console.error('❌ 启动失败:', error);
        process.exit(1);
    }
}

// 处理程序退出
process.on('SIGINT', () => {
    console.log('\n\n⚠️  收到中断信号，正在关闭...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n\n⚠️  收到终止信号，正在关闭...');
    process.exit(0);
});

// 启动系统
if (require.main === module) {
    startTradingSystem();
}