/**
 * 真实环境集成测试
 * ==================
 * 使用真实的富途API和华盛通接口进行测试
 */

import { TradingSystem } from "../TradingSystem";
import { 
    TaskStatus, 
    StrategyType, 
    Market, 
    DataType,
    LiquidationType,
    RiskConditionType
} from "../task-types";

class RealIntegrationTest {
    private tradingSystem!: TradingSystem;
    private testResults: Array<{ name: string; status: 'pass' | 'fail'; message?: string }> = [];

    async runAllTests(): Promise<void> {
        console.log('🚀 开始真实环境集成测试\n');

        try {
            await this.setupRealEnvironment();
            await this.testSystemInitialization();
            await this.testMarketDataConnection();
            await this.testTradingConnection();
            await this.testRealDataSubscription();
            await this.testSystemShutdown();
            
            this.printTestResults();
        } catch (error) {
            console.error('❌ 测试运行失败:', error);
        }
    }

    private async setupRealEnvironment(): Promise<void> {
        console.log('🔧 设置真实环境...');
        
        try {
            // 使用测试环境配置创建TradingSystem
            this.tradingSystem = await TradingSystem.createWithConfig('test');
            console.log('✅ 真实环境设置完成\n');
        } catch (error) {
            console.error('❌ 真实环境设置失败:', error);
            throw error;
        }
    }

    private async testSystemInitialization(): Promise<void> {
        console.log('📋 测试1: 系统初始化');
        
        try {
            await this.tradingSystem.initialize();
            
            const status = this.tradingSystem.getSystemStatus();
            if (status.isInitialized) {
                this.addTestResult('系统初始化', 'pass');
                console.log('   ✅ 系统初始化成功');
            } else {
                throw new Error('系统初始化失败');
            }
        } catch (error) {
            this.addTestResult('系统初始化', 'fail', error instanceof Error ? error.message : String(error));
            console.log('   ❌ 系统初始化失败:', error);
        }
        console.log('');
    }

    private async testMarketDataConnection(): Promise<void> {
        console.log('📋 测试2: 行情数据连接');
        
        try {
            // 等待行情连接稳定
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // 检查行情数据管理器状态
            const marketStats = this.tradingSystem.getMarketDataStats();
            
            if (marketStats.isConnected) {
                this.addTestResult('行情数据连接', 'pass');
                console.log('   ✅ 行情数据连接成功');
                console.log(`   📊 连接状态: ${marketStats.isConnected ? '已连接' : '未连接'}`);
            } else {
                throw new Error('行情数据连接失败');
            }
        } catch (error) {
            this.addTestResult('行情数据连接', 'fail', error instanceof Error ? error.message : String(error));
            console.log('   ❌ 行情数据连接失败:', error);
        }
        console.log('');
    }

    private async testTradingConnection(): Promise<void> {
        console.log('📋 测试3: 交易连接');
        
        try {
            // 检查交易管理器状态
            const tradingStatus = this.tradingSystem.getTradingStatus();
            
            console.log('   📊 交易适配器状态:');
            for (const [name, status] of Object.entries(tradingStatus)) {
                console.log(`      ${name}: ${status.connected ? '已连接' : '未连接'}`);
            }
            
            this.addTestResult('交易连接', 'pass', '连接状态已检查');
            console.log('   ✅ 交易连接状态检查完成');
        } catch (error) {
            this.addTestResult('交易连接', 'fail', error instanceof Error ? error.message : String(error));
            console.log('   ❌ 交易连接检查失败:', error);
        }
        console.log('');
    }

    private async testRealDataSubscription(): Promise<void> {
        console.log('📋 测试4: 真实数据订阅');
        
        try {
            // 创建一个真实的任务来测试数据订阅
            const taskId = await this.tradingSystem.createTask({
                name: '真实数据测试任务',
                stockCode: 'HK.00700',  // 腾讯控股
                stockName: '腾讯控股',
                market: Market.HK,
                strategyConfig: {
                    strategyType: StrategyType.BigOrderMonitor,
                    params: {
                        monitorThreshold: 10000,
                        durationSeconds: 60,
                        targetBrokers: ['高盛'],
                        orderSize: 100
                    },
                    requiredDataTypes: [DataType.Quote, DataType.OrderBook]
                },
                riskConfig: {
                    triggerLogic: 'any',
                    conditions: [], // 测试时不设置风险条件
                    liquidationStrategy: {
                        type: LiquidationType.Market,
                        params: {}
                    }
                }
            });
            
            console.log(`   ✅ 测试任务创建成功: ${taskId}`);
            
            // 启动任务开始接收真实数据
            await this.tradingSystem.startTask(taskId);
            console.log('   ✅ 任务启动成功，开始接收真实行情数据');
            
            // 等待一段时间接收数据
            console.log('   ⏳ 等待接收真实行情数据...');
            await new Promise(resolve => setTimeout(resolve, 10000)); // 等待10秒
            
            // 检查是否接收到数据
            const task = this.tradingSystem.getTask(taskId);
            if (task) {
                console.log(`   📊 任务状态: ${task.status}`);
                console.log(`   📈 最后更新: ${new Date(task.updatedAt).toLocaleString()}`);
            }
            
            // 停止并删除测试任务
            await this.tradingSystem.stopTask(taskId);
            await this.tradingSystem.deleteTask(taskId);
            console.log('   ✅ 测试任务已清理');
            
            this.addTestResult('真实数据订阅', 'pass');
            console.log('   ✅ 真实数据订阅测试完成');
            
        } catch (error) {
            this.addTestResult('真实数据订阅', 'fail', error instanceof Error ? error.message : String(error));
            console.log('   ❌ 真实数据订阅测试失败:', error);
        }
        console.log('');
    }

    private async testSystemShutdown(): Promise<void> {
        console.log('📋 测试5: 系统关闭');
        
        try {
            await this.tradingSystem.shutdown();
            
            const status = this.tradingSystem.getSystemStatus();
            if (!status.isInitialized) {
                console.log('   ✅ 系统关闭成功');
                this.addTestResult('系统关闭', 'pass');
            } else {
                throw new Error('系统关闭失败');
            }
        } catch (error) {
            this.addTestResult('系统关闭', 'fail', error instanceof Error ? error.message : String(error));
            console.log('   ❌ 系统关闭测试失败:', error);
        }
        console.log('');
    }

    private addTestResult(name: string, status: 'pass' | 'fail', message?: string): void {
        this.testResults.push({ name, status, message });
    }

    private printTestResults(): void {
        console.log('\n📊 真实环境测试结果汇总');
        console.log('='.repeat(50));
        
        let passCount = 0;
        let failCount = 0;
        
        this.testResults.forEach(result => {
            const icon = result.status === 'pass' ? '✅' : '❌';
            const message = result.message ? ` (${result.message})` : '';
            console.log(`${icon} ${result.name}${message}`);
            
            if (result.status === 'pass') {
                passCount++;
            } else {
                failCount++;
            }
        });
        
        console.log('\n' + '='.repeat(50));
        console.log(`总计: ${this.testResults.length} 项测试`);
        console.log(`通过: ${passCount} 项`);
        console.log(`失败: ${failCount} 项`);
        
        if (failCount === 0) {
            console.log('\n🎉 所有真实环境测试通过！交易系统与真实接口工作正常。');
        } else {
            console.log('\n⚠️  部分测试失败，请检查相关配置和网络连接。');
        }
    }
}

// 运行测试
async function runRealIntegrationTests() {
    const test = new RealIntegrationTest();
    await test.runAllTests();
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
    runRealIntegrationTests().catch(console.error);
}

export { RealIntegrationTest, runRealIntegrationTests };