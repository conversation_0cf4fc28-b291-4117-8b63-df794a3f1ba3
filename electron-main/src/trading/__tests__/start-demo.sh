#!/bin/bash

# 交易系统快速启动脚本
# ===================

echo "🚀 启动交易系统演示..."
echo ""

# 检查当前目录
if [[ ! -f "demo-real-system.ts" ]]; then
    echo "❌ 请在 src/trading/__tests__ 目录下运行此脚本"
    exit 1
fi

# 编译并运行演示
echo "🔨 编译TypeScript..."
npm run compile:demo

if [[ $? -eq 0 ]]; then
    echo ""
    echo "✅ 编译完成，启动演示程序..."
    echo ""
    node ../../../dist/trading/__tests__/demo-real-system.js
else
    echo "❌ 编译失败，请检查代码"
    exit 1
fi