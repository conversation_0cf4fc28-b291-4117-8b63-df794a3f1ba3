# 交易系统测试套件

## 📁 目录结构

```
__tests__/
├── real-integration-test.ts        # 真实环境集成测试 ⭐
├── demo-real-system.ts            # 真实系统演示 ⭐
├── package.json                    # 测试配置
└── README.md                       # 本文件
```

## 🚀 运行测试

### 💡 真实接口测试

#### 1. 真实环境集成测试
```bash
cd src/trading/__tests__
# 编译并运行真实环境测试
npx tsc real-integration-test.ts ../TradingSystem.ts ../config.ts --outDir ../../../dist/trading/__tests__ --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --strict false --skipLibCheck true
node ../../../dist/trading/__tests__/real-integration-test.js
```

#### 2. 真实系统演示
```bash
cd src/trading/__tests__
# 编译并运行真实系统演示
npx tsc demo-real-system.ts ../TradingSystem.ts ../config.ts --outDir ../../../dist/trading/__tests__ --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --strict false --skipLibCheck true
node ../../../dist/trading/__tests__/demo-real-system.js
```

## 📋 测试内容

### 🌟 真实环境测试 (real-integration-test.ts)
- ✅ 连接真实富途OpenD服务
- ✅ 连接真实华盛通交易接口
- ✅ 真实行情数据订阅和接收
- ✅ 系统完整性验证
- ✅ 网络连接和服务状态检查

### 🎯 真实系统演示 (demo-real-system.ts)
- ✅ 完整的交易系统启动流程
- ✅ 创建和管理真实交易任务
- ✅ 实时监控腾讯控股(HK.00700)
- ✅ 真实行情数据处理演示
- ✅ 优雅的系统关闭流程

## ⚙️ 配置说明

### 环境配置 (config.ts)
```typescript
// 生产环境：连接真实服务器
PRODUCTION_CONFIG = {
    market: { host: '127.0.0.1', port: 33333, encryptKey: '232df6441fb2bbdd' },
    trading: { huasheng: { host: '*************', port: 7777 } }
}

// 测试环境：使用测试服务器
TEST_CONFIG = {
    market: { host: '127.0.0.1', port: 33333, encryptKey: '232df6441fb2bbdd' },
    trading: { huasheng: { host: '127.0.0.1', port: 7777 } }
}

// 开发环境：本地开发配置
DEVELOPMENT_CONFIG = {
    market: { host: '127.0.0.1', port: 33333, encryptKey: '232df6441fb2bbdd' },
    trading: { huasheng: { host: '127.0.0.1', port: 7777 } }
}
```

### 服务要求
1. **富途OpenD**: 需要在本地运行OpenD程序
   - WebSocket端口: 33333
   - WebSocket密钥: 232df6441fb2bbdd
   - 需要有效的富途账户

2. **华盛通交易**: 需要华盛通交易服务
   - 默认端口: 7777
   - 需要有效的华盛通账户

## 📊 真实环境测试结果示例

```
🚀 开始真实环境集成测试

🔧 设置真实环境...
[TradingSystem] 使用 test 环境配置
[MarketDataManager] 连接成功
[TradingSystem] 行情数据管理器连接成功
[TradingSystem] 正在初始化交易系统...
[TaskManager] 正在初始化...
[TaskManager] 初始化完成
[TradingSystem] 交易系统初始化完成
✅ 真实环境设置完成

📋 测试1: 系统初始化
   ✅ 系统初始化成功

📋 测试2: 行情数据连接
   ✅ 行情数据连接成功
   📊 连接状态: 已连接

📋 测试3: 交易连接
   📊 交易适配器状态:
      huasheng: 已连接
   ✅ 交易连接状态检查完成

📋 测试4: 真实数据订阅
   ✅ 测试任务创建成功: task_xxx
   ✅ 任务启动成功，开始接收真实行情数据
   ⏳ 等待接收真实行情数据...
   📊 任务状态: Running
   📈 最后更新: 14:30:25
   ✅ 测试任务已清理
   ✅ 真实数据订阅测试完成

📋 测试5: 系统关闭
   ✅ 系统关闭成功

📊 真实环境测试结果汇总
==================================================
✅ 系统初始化
✅ 行情数据连接
✅ 交易连接
✅ 真实数据订阅
✅ 系统关闭

总计: 5 项测试
通过: 5 项
失败: 0 项

🎉 所有真实环境测试通过！交易系统与真实接口工作正常。
```

## 🛠️ 开发说明

### 使用真实接口的优势
1. **真实数据验证**: 确保系统能处理真实市场数据
2. **网络连接测试**: 验证网络连接的稳定性
3. **性能测试**: 真实环境下的性能表现
4. **接口兼容性**: 确保与真实API的兼容性

### 开发建议
1. **环境配置**: 使用不同环境配置管理不同部署场景
2. **错误处理**: 在真实环境中测试各种错误情况
3. **连接管理**: 处理网络断开和重连场景
4. **数据验证**: 验证接收到的真实数据格式

### 注意事项
- 确保富途OpenD程序正在运行
- 确保华盛通交易服务可访问
- 测试时使用小额度避免意外损失
- 生产环境前进行充分测试

## 🚀 快速开始

### 1. 启动服务
```bash
# 启动富途OpenD (WebSocket端口33333, 密钥: 232df6441fb2bbdd)
# 启动华盛通交易服务 (端口7777)
```

### 2. 运行演示
```bash
cd src/trading/__tests__
npm run demo  # 或者手动编译运行
```

### 3. 查看系统状态
演示程序会显示：
- 系统连接状态
- 行情数据统计
- 交易接口状态
- 可用策略列表
- 实时任务监控

### 4. 集成到项目
```typescript
import { TradingSystem } from './TradingSystem';

// 创建交易系统实例
const tradingSystem = await TradingSystem.createWithConfig('production');

// 初始化系统
await tradingSystem.initialize();

// 创建交易任务
const taskId = await tradingSystem.createTask({...});
```