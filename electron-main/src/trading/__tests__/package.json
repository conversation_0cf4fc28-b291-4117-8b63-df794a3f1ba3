{"name": "trading-system-tests", "version": "1.0.0", "description": "交易系统真实环境测试套件", "scripts": {"test": "npm run compile && npm run test:real", "demo": "npm run compile:demo && node ../../../dist/trading/__tests__/demo-real-system.js", "test:real": "npm run compile:test && node ../../../dist/trading/__tests__/real-integration-test.js", "compile": "npm run compile:test && npm run compile:demo", "compile:test": "npx tsc real-integration-test.ts ../TradingSystem.ts ../config.ts ../TaskManager.ts ../MarketDataManager.ts ../TradingManager.ts ../StrategyEngine.ts ../StrategyFactory.ts ../RiskManager.ts ../task-types.ts ../market-types.ts ../types.ts ../HuashengAdapter.ts ../FutuMarketAdapter.ts --outDir ../../../dist/trading/__tests__ --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --strict false --skipLibCheck true", "compile:demo": "npx tsc demo-real-system.ts ../TradingSystem.ts ../config.ts ../TaskManager.ts ../MarketDataManager.ts ../TradingManager.ts ../StrategyEngine.ts ../StrategyFactory.ts ../RiskManager.ts ../task-types.ts ../market-types.ts ../types.ts ../HuashengAdapter.ts ../FutuMarketAdapter.ts --outDir ../../../dist/trading/__tests__ --moduleResolution node --target es2020 --module commonjs --esModuleInterop true --strict false --skipLibCheck true"}, "keywords": ["trading", "real-interface", "futu", "hua<PERSON><PERSON>", "typescript"], "private": true}