/**
 * 交易管理器
 * ==========
 * 统一管理不同交易平台的适配器
 * 提供统一的交易接口和状态管理
 */

import { EventEmitter } from 'events';
import { HuashengAdapter, TradingAdapter } from './HuashengAdapter';
import { HuashengConfig } from './types';

export interface TradingManagerConfig {
    huasheng?: HuashengConfig;
    // 未来可以添加其他交易平台配置
    // futu?: FutuConfig;
}

export class TradingManager extends EventEmitter {
    private adapters: Map<string, TradingAdapter> = new Map();
    private config: TradingManagerConfig;

    constructor(config: TradingManagerConfig) {
        super();
        this.config = config;
        this.initializeAdapters();
    }

    /**
     * 初始化适配器
     */
    private initializeAdapters(): void {
        // 初始化华盛通适配器
        if (this.config.huasheng && this.config.huasheng.enabled) {
            console.log('[TradingManager] 初始化华盛通适配器...');
            const huashengAdapter = new HuashengAdapter(this.config.huasheng);
            
            // 监听适配器事件
            huashengAdapter.on('connected', () => {
                this.emit('adapter-connected', 'huasheng');
            });
            
            huashengAdapter.on('disconnected', (hadError: boolean) => {
                this.emit('adapter-disconnected', 'huasheng', hadError);
            });
            
            huashengAdapter.on('error', (error: Error) => {
                this.emit('adapter-error', 'huasheng', error);
            });
            
            huashengAdapter.on('login-success', (data: any) => {
                this.emit('login-success', 'huasheng', data);
            });
            
            huashengAdapter.on('login-error', (error: Error) => {
                this.emit('login-error', 'huasheng', error);
            });
            
            huashengAdapter.on('order-update', (data: any) => {
                this.emit('order-update', 'huasheng', data);
            });
            
            huashengAdapter.on('statistics-update', (data: any) => {
                this.emit('statistics-update', 'huasheng', data);
            });

            this.adapters.set('huasheng', huashengAdapter);
            console.log('[TradingManager] 华盛通适配器已初始化');
        } else if (this.config.huasheng && !this.config.huasheng.enabled) {
            console.log('[TradingManager] 华盛通适配器已禁用，跳过初始化');
        }
    }

    /**
     * 获取适配器
     */
    getAdapter(name: string): TradingAdapter | undefined {
        return this.adapters.get(name);
    }

    /**
     * 连接指定适配器
     */
    async connect(adapterName: string, config?: any): Promise<{ success: boolean; message: string }> {
        const adapter = this.adapters.get(adapterName);
        if (!adapter) {
            throw new Error(`适配器 ${adapterName} 不存在`);
        }

        try {
            await adapter.connect(config);
            console.log(`适配器 ${adapterName} 连接成功`);
            return { success: true, message: '连接成功' };
        } catch (error) {
            console.error(`适配器 ${adapterName} 连接失败:`, error);
            throw error;
        }
    }

    /**
     * 断开指定适配器
     */
    async disconnect(adapterName: string): Promise<void> {
        const adapter = this.adapters.get(adapterName);
        if (!adapter) {
            throw new Error(`适配器 ${adapterName} 不存在`);
        }

        try {
            await adapter.disconnect();
            console.log(`适配器 ${adapterName} 已断开连接`);
        } catch (error) {
            console.error(`适配器 ${adapterName} 断开连接失败:`, error);
            throw error;
        }
    }

    /**
     * 登录指定适配器
     */
    async login(adapterName: string, account?: string, password?: string): Promise<{ success: boolean; message: string; data?: any }> {
        const adapter = this.adapters.get(adapterName);
        if (!adapter) {
            throw new Error(`适配器 ${adapterName} 不存在`);
        }

        if ('login' in adapter && typeof adapter.login === 'function') {
            const result = await (adapter as any).login(account, password);
            return { success: true, message: '登录成功', data: result };
        } else {
            throw new Error(`适配器 ${adapterName} 不支持登录功能`);
        }
    }

    /**
     * 获取资金信息
     */
    async getFunds(adapterName: string): Promise<any> {
        const adapter = this.adapters.get(adapterName);
        if (!adapter) {
            throw new Error(`适配器 ${adapterName} 不存在`);
        }

        return await adapter.getFunds();
    }

    /**
     * 获取持仓信息
     */
    async getPositions(adapterName: string): Promise<any[]> {
        const adapter = this.adapters.get(adapterName);
        if (!adapter) {
            throw new Error(`适配器 ${adapterName} 不存在`);
        }

        return await adapter.getPositions();
    }

    /**
     * 获取订单列表
     */
    async getOrders(adapterName: string, filter?: any): Promise<any[]> {
        const adapter = this.adapters.get(adapterName);
        if (!adapter) {
            throw new Error(`适配器 ${adapterName} 不存在`);
        }

        return await adapter.getOrders(filter);
    }

    /**
     * 下单
     */
    async placeOrder(adapterName: string, orderParams: any): Promise<any> {
        const adapter = this.adapters.get(adapterName);
        if (!adapter) {
            throw new Error(`适配器 ${adapterName} 不存在`);
        }

        return await adapter.placeOrder(orderParams);
    }

    /**
     * 撤单
     */
    async cancelOrder(adapterName: string, orderId: string): Promise<void> {
        const adapter = this.adapters.get(adapterName);
        if (!adapter) {
            throw new Error(`适配器 ${adapterName} 不存在`);
        }

        return await adapter.cancelOrder(orderId);
    }

    /**
     * 获取所有适配器状态
     */
    getAllStatus(): Record<string, any> {
        const status: Record<string, any> = {};
        
        for (const [name, adapter] of this.adapters) {
            if ('getStatus' in adapter && typeof adapter.getStatus === 'function') {
                status[name] = adapter.getStatus();
            } else {
                status[name] = {
                    connected: adapter.isConnected(),
                    available: true
                };
            }
        }
        
        return status;
    }

    /**
     * 获取适配器列表
     */
    getAdapterNames(): string[] {
        return Array.from(this.adapters.keys());
    }

    /**
     * 检查适配器是否存在
     */
    hasAdapter(name: string): boolean {
        return this.adapters.has(name);
    }

    /**
     * 断开所有适配器
     */
    async disconnectAll(): Promise<void> {
        const promises: Promise<void>[] = [];
        
        for (const [name, adapter] of this.adapters) {
            promises.push(
                adapter.disconnect().catch(error => {
                    console.error(`断开适配器 ${name} 时出错:`, error);
                })
            );
        }
        
        await Promise.all(promises);
        console.log('所有适配器已断开连接');
    }
}