/**
 * 策略引擎
 * ========
 * 实现各种交易策略的执行逻辑
 * 处理策略信号生成和交易决策
 */

import { EventEmitter } from "events";
import {
    StrategyConfig,
    StrategyType,
    BigOrderMonitorConfig,
    BreakoutChaseConfig,
    TaskExecutionContext,
    StrategySignal,
    StrategyExecutionResult
} from "./task-types";
import { DataType, RealtimeData, Quote, OrderBook, Ticker, BrokerQueue } from "./market-types";

// 策略接口
export interface Strategy {
    readonly strategyType: StrategyType;
    readonly name: string;
    readonly description: string;
    readonly requiredDataTypes: DataType[];
    
    // 执行策略
    execute(context: TaskExecutionContext): Promise<StrategyExecutionResult>;
    
    // 验证配置
    validateConfig(config: StrategyConfig): boolean;
    
    // 获取策略状态
    getStatus(): StrategyStatus;
}

// 策略状态
export interface StrategyStatus {
    lastExecutionTime: number;
    executionCount: number;
    signalCount: number;
    lastSignal?: StrategySignal;
    errors: string[];
}

// 抽象策略基类
export abstract class BaseStrategy extends EventEmitter implements Strategy {
    abstract readonly strategyType: StrategyType;
    abstract readonly name: string;
    abstract readonly description: string;
    abstract readonly requiredDataTypes: DataType[];
    
    protected status: StrategyStatus = {
        lastExecutionTime: 0,
        executionCount: 0,
        signalCount: 0,
        errors: []
    };
    
    async execute(context: TaskExecutionContext): Promise<StrategyExecutionResult> {
        try {
            this.status.lastExecutionTime = Date.now();
            this.status.executionCount++;
            
            const result = await this.executeStrategy(context);
            
            if (result.signal) {
                this.status.signalCount++;
                this.status.lastSignal = result.signal;
                this.emit('signal', result.signal, context);
            }
            
            return result;
            
        } catch (error) {
            const errorMsg = error instanceof Error ? error.message : String(error);
            this.status.errors.push(errorMsg);
            this.emit('error', error, context);
            
            return {
                shouldContinue: true,
                error: error instanceof Error ? error : new Error(errorMsg)
            };
        }
    }
    
    abstract validateConfig(config: StrategyConfig): boolean;
    abstract executeStrategy(context: TaskExecutionContext): Promise<StrategyExecutionResult>;
    
    getStatus(): StrategyStatus {
        return { ...this.status };
    }
    
    protected createSignal(
        type: StrategySignal['type'],
        confidence: number,
        reason: string,
        quantity?: number,
        price?: number
    ): StrategySignal {
        return {
            type,
            confidence: Math.max(0, Math.min(1, confidence)),
            quantity,
            price,
            reason,
            timestamp: Date.now()
        };
    }
}

/**
 * 策略A: 大单监控策略
 * 监控大额买单，在特定条件下跟单
 */
export class BigOrderMonitorStrategy extends BaseStrategy {
    readonly strategyType = StrategyType.BigOrderMonitor;
    readonly name = "大单监控策略";
    readonly description = "监控主力经纪商大额买单，在条件满足时跟单买入";
    readonly requiredDataTypes = [DataType.Quote, DataType.OrderBook, DataType.BrokerQueue];
    
    private lastCheckTime: number = 0;
    private monitoringData: Map<string, any> = new Map(); // 存储监控数据
    
    validateConfig(config: StrategyConfig): boolean {
        if (config.strategyType !== StrategyType.BigOrderMonitor) {
            return false;
        }
        
        const params = config.params;
        return !!(
            params.monitorThreshold &&
            params.durationSeconds &&
            params.targetBrokers &&
            Array.isArray(params.targetBrokers) &&
            params.orderSize > 0
        );
    }
    
    async executeStrategy(context: TaskExecutionContext): Promise<StrategyExecutionResult> {
        const config = context.task.strategyConfig as BigOrderMonitorConfig;
        const marketData = context.marketData;
        
        if (!marketData) {
            return { shouldContinue: true };
        }
        
        // 获取当前报价
        const quote = this.extractQuote(marketData);
        if (!quote) {
            return { shouldContinue: true };
        }
        
        // 检查价格条件（如果设置了）
        if (config.params.priceCondition && quote.price > config.params.priceCondition) {
            return { shouldContinue: true };
        }
        
        // 获取经纪队列数据
        const brokerQueue = this.extractBrokerQueue(marketData);
        if (!brokerQueue) {
            return { shouldContinue: true };
        }
        
        // 分析大单情况
        const bigOrderAnalysis = this.analyzeBigOrders(brokerQueue, config);
        
        if (bigOrderAnalysis.detected) {
            // 检查持续时间
            const key = `${context.task.stockCode}_big_order`;
            const existingData = this.monitoringData.get(key);
            
            if (!existingData) {
                // 首次检测到大单
                this.monitoringData.set(key, {
                    startTime: Date.now(),
                    analysis: bigOrderAnalysis
                });
                return { shouldContinue: true };
            }
            
            // 检查是否满足持续时间要求
            const duration = (Date.now() - existingData.startTime) / 1000;
            if (duration >= config.params.durationSeconds) {
                // 生成买入信号
                const signal = this.createSignal(
                    'buy',
                    bigOrderAnalysis.confidence,
                    `检测到${config.params.targetBrokers.join(',')}等主力经纪商持续${duration.toFixed(0)}秒大额买入`,
                    config.params.orderSize,
                    quote.price
                );
                
                // 清除监控数据
                this.monitoringData.delete(key);
                
                return { 
                    signal,
                    shouldContinue: true 
                };
            }
        } else {
            // 未检测到大单，清除监控数据
            const key = `${context.task.stockCode}_big_order`;
            this.monitoringData.delete(key);
        }
        
        return { shouldContinue: true };
    }
    
    private extractQuote(marketData: RealtimeData): Quote | null {
        if (marketData.type === DataType.Quote) {
            return marketData.data as Quote;
        }
        return null;
    }
    
    private extractBrokerQueue(marketData: RealtimeData): BrokerQueue | null {
        if (marketData.type === DataType.BrokerQueue) {
            return marketData.data as BrokerQueue;
        }
        return null;
    }
    
    private analyzeBigOrders(brokerQueue: BrokerQueue, config: BigOrderMonitorConfig): {
        detected: boolean;
        confidence: number;
        details: any;
    } {
        // 分析买盘经纪队列
        const bidBrokers = brokerQueue.bidBrokers || [];
        let totalBigOrderVolume = 0;
        let targetBrokerCount = 0;
        
        for (const broker of bidBrokers) {
            // 检查是否是目标经纪商
            const isTargetBroker = config.params.targetBrokers.some(target => 
                broker.name?.includes(target) || broker.id?.includes(target)
            );
            
            if (isTargetBroker && broker.volume >= config.params.monitorThreshold) {
                totalBigOrderVolume += broker.volume;
                targetBrokerCount++;
            }
        }
        
        const detected = totalBigOrderVolume >= config.params.monitorThreshold && targetBrokerCount > 0;
        const confidence = Math.min(0.9, targetBrokerCount * 0.3 + (totalBigOrderVolume / config.params.monitorThreshold) * 0.1);
        
        return {
            detected,
            confidence,
            details: {
                totalBigOrderVolume,
                targetBrokerCount,
                analyzedBrokers: bidBrokers.length
            }
        };
    }
}

/**
 * 策略B: 突破追涨策略
 * 监控价格突破和成交量放大，追涨操作
 */
export class BreakoutChaseStrategy extends BaseStrategy {
    readonly strategyType = StrategyType.BreakoutChase;
    readonly name = "突破追涨策略";
    readonly description = "监控价格突破前期高点，配合成交量放大信号追涨";
    readonly requiredDataTypes = [DataType.Quote, DataType.Ticker];
    
    private priceHistory: Map<string, number[]> = new Map();
    private volumeHistory: Map<string, number[]> = new Map();
    
    validateConfig(config: StrategyConfig): boolean {
        if (config.strategyType !== StrategyType.BreakoutChase) {
            return false;
        }
        
        const params = config.params;
        return !!(
            params.breakoutPeriod > 0 &&
            params.volumeMultiplier > 1 &&
            params.pullbackPercent >= 0
        );
    }
    
    async executeStrategy(context: TaskExecutionContext): Promise<StrategyExecutionResult> {
        const config = context.task.strategyConfig as BreakoutChaseConfig;
        const marketData = context.marketData;
        
        if (!marketData) {
            return { shouldContinue: true };
        }
        
        const quote = this.extractQuote(marketData);
        if (!quote) {
            return { shouldContinue: true };
        }
        
        // 更新价格历史
        this.updatePriceHistory(context.task.stockCode, quote.price);
        this.updateVolumeHistory(context.task.stockCode, quote.volume);
        
        // 检查突破条件
        const breakoutAnalysis = this.analyzeBreakout(context.task.stockCode, config);
        
        if (breakoutAnalysis.isBreakout) {
            const signal = this.createSignal(
                'buy',
                breakoutAnalysis.confidence,
                `价格突破前期高点${breakoutAnalysis.breakoutPrice.toFixed(2)}，成交量放大${breakoutAnalysis.volumeRatio.toFixed(1)}倍`,
                undefined, // 数量由风控决定
                quote.price
            );
            
            return { 
                signal,
                shouldContinue: true 
            };
        }
        
        return { shouldContinue: true };
    }
    
    private updatePriceHistory(stockCode: string, price: number): void {
        if (!this.priceHistory.has(stockCode)) {
            this.priceHistory.set(stockCode, []);
        }
        
        const history = this.priceHistory.get(stockCode)!;
        history.push(price);
        
        // 保留最近100个数据点
        if (history.length > 100) {
            history.shift();
        }
    }
    
    private updateVolumeHistory(stockCode: string, volume: number): void {
        if (!this.volumeHistory.has(stockCode)) {
            this.volumeHistory.set(stockCode, []);
        }
        
        const history = this.volumeHistory.get(stockCode)!;
        history.push(volume);
        
        // 保留最近100个数据点
        if (history.length > 100) {
            history.shift();
        }
    }
    
    private analyzeBreakout(stockCode: string, config: BreakoutChaseConfig): {
        isBreakout: boolean;
        confidence: number;
        breakoutPrice: number;
        volumeRatio: number;
    } {
        const priceHistory = this.priceHistory.get(stockCode) || [];
        const volumeHistory = this.volumeHistory.get(stockCode) || [];
        
        if (priceHistory.length < config.params.breakoutPeriod) {
            return { isBreakout: false, confidence: 0, breakoutPrice: 0, volumeRatio: 0 };
        }
        
        const currentPrice = priceHistory[priceHistory.length - 1];
        const periodPrices = priceHistory.slice(-config.params.breakoutPeriod);
        const highestPrice = Math.max(...periodPrices.slice(0, -1)); // 排除当前价格
        
        // 检查价格突破
        const priceBreakout = currentPrice > highestPrice;
        
        // 检查成交量放大
        const recentVolumes = volumeHistory.slice(-5); // 最近5个数据点
        const historicalVolumes = volumeHistory.slice(-config.params.breakoutPeriod, -5);
        
        const avgRecentVolume = recentVolumes.reduce((sum, v) => sum + v, 0) / recentVolumes.length;
        const avgHistoricalVolume = historicalVolumes.reduce((sum, v) => sum + v, 0) / historicalVolumes.length;
        
        const volumeRatio = avgHistoricalVolume > 0 ? avgRecentVolume / avgHistoricalVolume : 0;
        const volumeBreakout = volumeRatio >= config.params.volumeMultiplier;
        
        const isBreakout = priceBreakout && volumeBreakout;
        const confidence = isBreakout ? Math.min(0.8, 0.4 + volumeRatio * 0.1) : 0;
        
        return {
            isBreakout,
            confidence,
            breakoutPrice: highestPrice,
            volumeRatio
        };
    }
    
    private extractQuote(marketData: RealtimeData): Quote | null {
        if (marketData.type === DataType.Quote) {
            return marketData.data as Quote;
        }
        return null;
    }
}

/**
 * 策略引擎管理器
 * 管理所有策略实例，提供策略工厂功能
 */
export class StrategyEngine {
    private strategies: Map<StrategyType, Strategy> = new Map();
    
    constructor() {
        this.registerDefaultStrategies();
    }
    
    /**
     * 注册默认策略
     */
    private registerDefaultStrategies(): void {
        this.registerStrategy(new BigOrderMonitorStrategy());
        this.registerStrategy(new BreakoutChaseStrategy());
    }
    
    /**
     * 注册策略
     */
    registerStrategy(strategy: Strategy): void {
        this.strategies.set(strategy.strategyType, strategy);
        console.log(`[StrategyEngine] 注册策略: ${strategy.name}`);
    }
    
    /**
     * 获取策略
     */
    getStrategy(strategyType: StrategyType): Strategy | null {
        return this.strategies.get(strategyType) || null;
    }
    
    /**
     * 创建策略实例
     */
    createStrategy(strategyType: StrategyType): Strategy | null {
        const StrategyClass = this.getStrategyClass(strategyType);
        if (StrategyClass) {
            return new StrategyClass();
        }
        return null;
    }
    
    /**
     * 获取所有可用策略
     */
    getAvailableStrategies(): Array<{
        type: StrategyType;
        name: string;
        description: string;
        requiredDataTypes: DataType[];
    }> {
        return Array.from(this.strategies.values()).map(strategy => ({
            type: strategy.strategyType,
            name: strategy.name,
            description: strategy.description,
            requiredDataTypes: strategy.requiredDataTypes
        }));
    }
    
    /**
     * 验证策略配置
     */
    validateStrategyConfig(config: StrategyConfig): boolean {
        const strategy = this.strategies.get(config.strategyType);
        return strategy ? strategy.validateConfig(config) : false;
    }
    
    /**
     * 获取策略类
     */
    private getStrategyClass(strategyType: StrategyType): (new() => Strategy) | null {
        switch (strategyType) {
            case StrategyType.BigOrderMonitor:
                return BigOrderMonitorStrategy;
            case StrategyType.BreakoutChase:
                return BreakoutChaseStrategy;
            default:
                return null;
        }
    }
}