/**
 * 风险管理器
 * ===========
 * 提供风险条件检查和清仓策略执行的统一接口
 */

import { EventEmitter } from "events";
import {
    RiskConfig,
    RiskCondition,
    RiskConditionType,
    LiquidationStrategy,
    LiquidationType,
    RiskCheckResult,
    TaskExecutionContext
} from "./task-types";

export class RiskManager extends EventEmitter {
    private conditionCheckers: Map<RiskConditionType, RiskConditionChecker> = new Map();
    private liquidationExecutors: Map<LiquidationType, LiquidationExecutor> = new Map();

    constructor() {
        super();
        this.registerDefaultCheckers();
        this.registerDefaultExecutors();
    }

    /**
     * 注册默认的风险条件检查器
     */
    private registerDefaultCheckers(): void {
        this.conditionCheckers.set(RiskConditionType.Price, new PriceRiskChecker());
        this.conditionCheckers.set(RiskConditionType.PnLRatio, new PnLRatioRiskChecker());
        this.conditionCheckers.set(RiskConditionType.StopLoss, new StopLossRiskChecker());
        this.conditionCheckers.set(RiskConditionType.TakeProfit, new TakeProfitRiskChecker());
        this.conditionCheckers.set(RiskConditionType.Time, new TimeRiskChecker());
        this.conditionCheckers.set(RiskConditionType.TrailingStop, new TrailingStopRiskChecker());
    }

    /**
     * 注册默认的清仓执行器
     */
    private registerDefaultExecutors(): void {
        this.liquidationExecutors.set(LiquidationType.Market, new MarketLiquidationExecutor());
        this.liquidationExecutors.set(LiquidationType.LimitOptimized, new LimitOptimizedLiquidationExecutor());
        this.liquidationExecutors.set(LiquidationType.SmartExecution, new SmartLiquidationExecutor());
        this.liquidationExecutors.set(LiquidationType.TWAP, new TWAPLiquidationExecutor());
    }

    /**
     * 执行风险检查
     */
    async checkRiskConditions(context: TaskExecutionContext): Promise<RiskCheckResult> {
        try {
            const { task } = context;
            const riskConfig = task.riskConfig;
            
            if (!riskConfig.conditions || riskConfig.conditions.length === 0) {
                return {
                    shouldLiquidate: false,
                    triggeredConditions: [],
                    urgency: 'low'
                };
            }
            
            const triggeredConditions: string[] = [];
            let highestUrgency: 'low' | 'medium' | 'high' = 'low';
            
            // 并发检查所有风险条件
            const checkPromises = riskConfig.conditions.map(async (condition) => {
                const checker = this.conditionCheckers.get(condition.type);
                if (!checker) {
                    console.warn(`[RiskManager] 未找到风险条件检查器: ${condition.type}`);
                    return { condition, triggered: false };
                }
                
                const triggered = await checker.check(context, condition);
                return { condition, triggered };
            });
            
            const results = await Promise.all(checkPromises);
            
            // 收集触发的条件
            for (const { condition, triggered } of results) {
                if (triggered) {
                    triggeredConditions.push(condition.id);
                    
                    const urgency = this.getConditionUrgency(condition.type);
                    if (urgency === 'high' || (urgency === 'medium' && highestUrgency === 'low')) {
                        highestUrgency = urgency;
                    }
                }
            }
            
            // 根据触发逻辑判断是否需要清仓
            const shouldLiquidate = riskConfig.triggerLogic === 'any' 
                ? triggeredConditions.length > 0 
                : triggeredConditions.length === riskConfig.conditions.length;
            
            return {
                shouldLiquidate,
                triggeredConditions,
                liquidationStrategy: shouldLiquidate ? riskConfig.liquidationStrategy : undefined,
                urgency: shouldLiquidate ? highestUrgency : 'low'
            };
            
        } catch (error) {
            console.error('[RiskManager] 风险检查失败:', error);
            return {
                shouldLiquidate: false,
                triggeredConditions: [],
                urgency: 'low'
            };
        }
    }

    /**
     * 执行清仓策略
     */
    async executeLiquidation(task: any, liquidationStrategy?: LiquidationStrategy): Promise<void> {
        try {
            if (task.position === 0) {
                console.log(`[RiskManager] 任务 ${task.id} 无仓位，无需清仓`);
                return;
            }
            
            const strategy = liquidationStrategy || task.riskConfig.liquidationStrategy;
            const executor = this.liquidationExecutors.get(strategy.type);
            
            if (!executor) {
                console.warn(`[RiskManager] 未找到清仓执行器: ${strategy.type}，使用市价单`);
                const marketExecutor = this.liquidationExecutors.get(LiquidationType.Market);
                if (marketExecutor) {
                    await marketExecutor.execute(task, strategy.params);
                }
                return;
            }
            
            await executor.execute(task, strategy.params);
            
        } catch (error) {
            console.error(`[RiskManager] 执行清仓失败 ${task.id}:`, error);
            throw error;
        }
    }

    /**
     * 获取条件紧急程度
     */
    private getConditionUrgency(conditionType: RiskConditionType): 'low' | 'medium' | 'high' {
        switch (conditionType) {
            case RiskConditionType.StopLoss:
            case RiskConditionType.Price:
                return 'high';
            case RiskConditionType.PnLRatio:
            case RiskConditionType.TakeProfit:
                return 'medium';
            case RiskConditionType.Time:
            case RiskConditionType.TrailingStop:
                return 'low';
            default:
                return 'low';
        }
    }

    /**
     * 注册自定义风险条件检查器
     */
    registerConditionChecker(type: RiskConditionType, checker: RiskConditionChecker): void {
        this.conditionCheckers.set(type, checker);
        console.log(`[RiskManager] 注册风险条件检查器: ${type}`);
    }

    /**
     * 注册自定义清仓执行器
     */
    registerLiquidationExecutor(type: LiquidationType, executor: LiquidationExecutor): void {
        this.liquidationExecutors.set(type, executor);
        console.log(`[RiskManager] 注册清仓执行器: ${type}`);
    }
}

// ====================== 风险条件检查器接口和实现 ======================

export interface RiskConditionChecker {
    check(context: TaskExecutionContext, condition: RiskCondition): Promise<boolean>;
}

export class PriceRiskChecker implements RiskConditionChecker {
    async check(context: TaskExecutionContext, condition: RiskCondition): Promise<boolean> {
        const { currentPrice } = context;
        const { trigger, direction, price } = condition.params;
        
        if (direction === 'above') {
            return trigger === 'touch' ? currentPrice >= price : currentPrice > price;
        } else {
            return trigger === 'touch' ? currentPrice <= price : currentPrice < price;
        }
    }
}

export class PnLRatioRiskChecker implements RiskConditionChecker {
    async check(context: TaskExecutionContext, condition: RiskCondition): Promise<boolean> {
        const { task } = context;
        const { lossRatio, profitRatio } = condition.params;
        
        if (!task.avgCost || task.position === 0) {
            return false;
        }
        
        const pnlPercent = (task.pnl / (task.avgCost * Math.abs(task.position))) * 100;
        
        if (lossRatio && pnlPercent <= -Math.abs(lossRatio)) {
            return true;
        }
        
        if (profitRatio && pnlPercent >= profitRatio) {
            return true;
        }
        
        return false;
    }
}

export class StopLossRiskChecker implements RiskConditionChecker {
    async check(context: TaskExecutionContext, condition: RiskCondition): Promise<boolean> {
        const { task, currentPrice } = context;
        const { stopPrice } = condition.params;
        
        if (!task.avgCost || task.position === 0) {
            return false;
        }
        
        if (task.position > 0) {
            return currentPrice <= stopPrice;
        } else {
            return currentPrice >= stopPrice;
        }
    }
}

export class TakeProfitRiskChecker implements RiskConditionChecker {
    async check(context: TaskExecutionContext, condition: RiskCondition): Promise<boolean> {
        const { task, currentPrice } = context;
        const { targetPrice } = condition.params;
        
        if (!task.avgCost || task.position === 0) {
            return false;
        }
        
        if (task.position > 0) {
            return currentPrice >= targetPrice;
        } else {
            return currentPrice <= targetPrice;
        }
    }
}

export class TimeRiskChecker implements RiskConditionChecker {
    async check(context: TaskExecutionContext, condition: RiskCondition): Promise<boolean> {
        const { task } = context;
        const { holdingMinutes } = condition.params;
        
        if (task.position === 0) {
            return false;
        }
        
        const holdingTime = (Date.now() - task.updatedAt.getTime()) / (1000 * 60);
        return holdingTime >= holdingMinutes;
    }
}

export class TrailingStopRiskChecker implements RiskConditionChecker {
    async check(context: TaskExecutionContext, condition: RiskCondition): Promise<boolean> {
        const { task, currentPrice } = context;
        const { currentStopPrice } = condition.params;
        
        if (!currentStopPrice || task.position === 0) {
            return false;
        }
        
        if (task.position > 0) {
            return currentPrice <= currentStopPrice;
        } else {
            return currentPrice >= currentStopPrice;
        }
    }
}

// ====================== 清仓执行器接口和实现 ======================

export interface LiquidationExecutor {
    execute(task: any, params: any): Promise<void>;
}

export class MarketLiquidationExecutor implements LiquidationExecutor {
    async execute(task: any, params: any): Promise<void> {
        console.log(`[MarketLiquidation] 市价单清仓 ${task.id}: ${Math.abs(task.position)}股`);
        
        const executionPrice = (task.avgCost || 0) * (1 + (Math.random() - 0.5) * 0.01);
        const realizedPnL = (executionPrice - (task.avgCost || 0)) * Math.abs(task.position);
        
        task.position = 0;
        task.avgCost = undefined;
        task.pnl += realizedPnL;
        task.updatedAt = new Date();
    }
}

export class LimitOptimizedLiquidationExecutor implements LiquidationExecutor {
    async execute(task: any, params: any): Promise<void> {
        console.log(`[LimitOptimizedLiquidation] 优化限价单清仓 ${task.id}: ${Math.abs(task.position)}股`);
        
        const { priceOffset = 0, timeoutSeconds = 30 } = params;
        const executionPrice = (task.avgCost || 0) * (1 + priceOffset * 0.001);
        
        await new Promise(resolve => setTimeout(resolve, Math.min(timeoutSeconds * 100, 3000)));
        
        const realizedPnL = (executionPrice - (task.avgCost || 0)) * Math.abs(task.position);
        
        task.position = 0;
        task.avgCost = undefined;
        task.pnl += realizedPnL;
        task.updatedAt = new Date();
    }
}

export class SmartLiquidationExecutor implements LiquidationExecutor {
    async execute(task: any, params: any): Promise<void> {
        console.log(`[SmartLiquidation] 智能清仓 ${task.id}: ${Math.abs(task.position)}股`);
        
        const { aggressiveness = 'medium', maxSlippageBps = 15 } = params;
        const slippageMultiplier = aggressiveness === 'high' ? 1.5 : 
                                  aggressiveness === 'low' ? 0.5 : 1.0;
        
        const actualSlippage = (maxSlippageBps / 10000) * slippageMultiplier;
        const executionPrice = (task.avgCost || 0) * (1 - actualSlippage);
        
        const realizedPnL = (executionPrice - (task.avgCost || 0)) * Math.abs(task.position);
        
        task.position = 0;
        task.avgCost = undefined;
        task.pnl += realizedPnL;
        task.updatedAt = new Date();
    }
}

export class TWAPLiquidationExecutor implements LiquidationExecutor {
    async execute(task: any, params: any): Promise<void> {
        console.log(`[TWAPLiquidation] TWAP清仓 ${task.id}: ${Math.abs(task.position)}股`);
        
        const { executionMinutes = 5 } = params;
        const quantity = Math.abs(task.position);
        const batches = Math.min(executionMinutes, 5);
        const batchSize = Math.floor(quantity / batches);
        let totalRealizedPnL = 0;
        
        for (let i = 0; i < batches; i++) {
            const currentBatchSize = i === batches - 1 ? quantity - (batchSize * i) : batchSize;
            const executionPrice = (task.avgCost || 0) * (1 + (Math.random() - 0.5) * 0.005);
            
            totalRealizedPnL += (executionPrice - (task.avgCost || 0)) * currentBatchSize;
            
            if (i < batches - 1) {
                await new Promise(resolve => setTimeout(resolve, 500));
            }
        }
        
        task.position = 0;
        task.avgCost = undefined;
        task.pnl += totalRealizedPnL;
        task.updatedAt = new Date();
    }
}