/**
 * 华盛通交易适配器 - 基于可工作的实现
 * =====================================
 * 参考工作代码重新实现，正确处理异步响应和状态管理
 */

import { EventEmitter } from 'events';
import { HuashengTcpClient } from './HuashengTcpClient-fixed';
import {
    HuashengConfig,
    ApiRequestType,
    Side,
    OrderType,
    LoginRequest,
    LoginResponse,
} from './types';

interface PendingRequest {
    resolve: (value: any) => void;
    reject: (reason: any) => void;
}

export class HuashengAdapter extends EventEmitter {
    private tcpClient: HuashengTcpClient;
    private requestId: number = 1;
    private hasLoggedIn: boolean = false;
    private token: string | null = null;
    private pendingRequests: Map<number, PendingRequest> = new Map();
    private config: HuashengConfig;

    constructor(config: HuashengConfig) {
        super();
        this.config = config;
        this.tcpClient = new HuashengTcpClient(config.host, config.port);
        
        this.setupEventHandlers();
    }

    /**
     * 设置事件处理器
     */
    private setupEventHandlers(): void {
        // 监听连接事件
        this.tcpClient.on('connected', () => {
            console.log('华盛通TCP连接已建立');
            this.emit('connected');
        });

        // 监听断开连接事件
        this.tcpClient.on('disconnected', () => {
            console.log('华盛通TCP连接已断开');
            this.hasLoggedIn = false;
            this.token = null;
            this.emit('disconnected');
        });

        // 监听消息事件
        this.tcpClient.on('message', (data) => {
            this.handleResponse(data);
        });

        // 监听错误事件
        this.tcpClient.on('error', (error) => {
            console.error('华盛通TCP连接错误:', error.message);
            this.emit('error', error);
        });
    }

    /**
     * 连接到华盛通服务器
     */
    async connect(): Promise<void> {
        try {
            await this.tcpClient.connect();
            // 等待2秒确保连接稳定
            await new Promise(resolve => setTimeout(resolve, 2000));
        } catch (error) {
            console.error('华盛通连接失败:', error);
            throw error;
        }
    }

    /**
     * 断开连接
     */
    async disconnect(): Promise<void> {
        this.tcpClient.disconnect();
        this.hasLoggedIn = false;
        this.token = null;
    }

    /**
     * 检查连接状态
     */
    isConnected(): boolean {
        return this.tcpClient.getConnectionStatus().connected;
    }

    /**
     * 用户登录
     */
    async login(account?: string, password?: string): Promise<void> {
        const loginAccount = account || this.config.account;
        const loginPassword = password || this.config.password;
        
        if (!loginAccount || !loginPassword) {
            throw new Error('账户或密码未设置');
        }

        this.requestId++;
        const currentRequestId = this.requestId;
        
        const data = {
            RequestId: currentRequestId,
            RequestType: ApiRequestType.Login,
            Account: loginAccount,
            Password: loginPassword
        };

        try {
            const response = await this.sendRequest(data, '登录');
            if (response.LoginResoult && response.Token) {
                this.hasLoggedIn = true;
                this.token = response.Token;
                console.log('华盛通登录成功');
                this.emit('login-success', { account: loginAccount });
            } else {
                throw new Error('登录失败: ' + response.ResponseMsg);
            }
        } catch (error) {
            console.error('华盛通登录失败:', error);
            this.emit('login-error', error);
            throw error;
        }
    }

    /**
     * 获取账户信息
     */
    async getAccountInfo(): Promise<any> {
        return {
            account: this.config.account,
            loginStatus: this.hasLoggedIn,
            connectionStatus: this.isConnected(),
            loginTime: Date.now()
        };
    }

    /**
     * 查询资金信息
     */
    async getFunds(): Promise<any> {
        if (!this.hasLoggedIn || !this.token) {
            throw new Error('请先登录华盛通账户');
        }

        this.requestId++;
        const data = {
            RequestId: this.requestId,
            RequestType: ApiRequestType.QueryFunds,
            Token: this.token
        };

        try {
            const response = await this.sendRequest(data, '查询资金');
            const payload = response.Payload || {};
            
            return {
                availableFunds: payload.AvailableFunds || 0,
                buyingPower: payload.BuyingPower || 0,
                totalAssets: payload.TotalAssets || 0,
                frozenFunds: payload.FrozenFunds || 0,
                currency: payload.Currency || 'HKD',
                timestamp: Date.now()
            };
        } catch (error) {
            console.error('查询资金信息失败:', error);
            throw error;
        }
    }

    /**
     * 查询持仓信息
     */
    async getPositions(): Promise<any[]> {
        if (!this.hasLoggedIn || !this.token) {
            throw new Error('请先登录华盛通账户');
        }

        this.requestId++;
        const data = {
            RequestId: this.requestId,
            RequestType: ApiRequestType.QueryPosition,
            Token: this.token
        };

        try {
            const response = await this.sendRequest(data, '查询持仓');
            return response.Payload || [];
        } catch (error) {
            console.error('查询持仓信息失败:', error);
            throw error;
        }
    }

    /**
     * 查询订单列表
     */
    async getOrders(filter?: { pageNo?: number; pageSize?: number }): Promise<any[]> {
        if (!this.hasLoggedIn || !this.token) {
            throw new Error('请先登录华盛通账户');
        }

        this.requestId++;
        const data: any = {
            RequestId: this.requestId,
            RequestType: ApiRequestType.QueryEntrust,
            Token: this.token
        };

        if (filter?.pageNo) data.PageNo = filter.pageNo;
        if (filter?.pageSize) data.PageSize = filter.pageSize;

        try {
            const response = await this.sendRequest(data, '查询订单');
            return response.Payload || [];
        } catch (error) {
            console.error('查询订单列表失败:', error);
            throw error;
        }
    }

    /**
     * 下单
     */
    async placeOrder(orderParams: {
        stockCode: string;
        price: number;
        quantity: number;
        side: 'BUY' | 'SELL';
        orderType?: 'LIMIT' | 'MARKET';
    }): Promise<any> {
        if (!this.hasLoggedIn || !this.token) {
            throw new Error('请先登录华盛通账户');
        }

        const side = orderParams.side === 'BUY' ? Side.Buy : Side.Sell;
        const orderType = orderParams.orderType === 'MARKET' ? OrderType.Market : OrderType.Limited;

        this.requestId++;
        const data = {
            RequestId: this.requestId,
            RequestType: ApiRequestType.PlaceOrder,
            StockCode: orderParams.stockCode,
            Price: orderParams.price,
            Qty: orderParams.quantity,
            Side: side,
            OrderType: orderType,
            Token: this.token
        };

        try {
            const response = await this.sendRequest(data, '下单');
            const payload = response.Payload || {};
            
            return {
                orderId: payload.OrderId,
                entrustNo: payload.EntrustNo,
                status: payload.Status,
                stockCode: orderParams.stockCode,
                price: orderParams.price,
                quantity: orderParams.quantity,
                side: orderParams.side,
                timestamp: Date.now()
            };
        } catch (error) {
            console.error('下单失败:', error);
            throw error;
        }
    }

    /**
     * 撤单
     */
    async cancelOrder(entrustNo: string): Promise<void> {
        if (!this.hasLoggedIn || !this.token) {
            throw new Error('请先登录华盛通账户');
        }

        this.requestId++;
        const data = {
            RequestId: this.requestId,
            RequestType: ApiRequestType.UnOrder,
            EntrustNo: entrustNo,
            Token: this.token
        };

        try {
            await this.sendRequest(data, '撤单');
            console.log('撤单成功:', entrustNo);
        } catch (error) {
            console.error('撤单失败:', error);
            throw error;
        }
    }

    /**
     * 通用请求方法
     */
    private async sendRequest(requestData: any, operationName: string, timeout: number = 10000): Promise<any> {
        const requestId = requestData.RequestId;
        
        // 创建Promise等待响应
        const responsePromise = new Promise((resolve, reject) => {
            this.pendingRequests.set(requestId, { resolve, reject });
            
            setTimeout(() => {
                if (this.pendingRequests.has(requestId)) {
                    this.pendingRequests.delete(requestId);
                    reject(new Error(`${operationName}请求超时`));
                }
            }, timeout);
        });

        try {
            await this.tcpClient.sendMessage(requestData);
            console.log(`${operationName}请求已发送`);
            return await responsePromise;
        } catch (error) {
            this.pendingRequests.delete(requestId);
            console.error(`发送${operationName}请求失败:`, error);
            throw error;
        }
    }

    /**
     * 处理服务器响应
     */
    private handleResponse(responseData: any): void {
        try {
            const requestId = responseData.RequestId;
            const requestType = responseData.RequestType;
            const responseCode = responseData.ResponseCode;

            // 检查是否有对应的待处理请求（处理字符串和数字的转换）
            const numericRequestId = parseInt(requestId);
            let pendingRequest = null;
            
            if (this.pendingRequests.has(requestId)) {
                pendingRequest = this.pendingRequests.get(requestId);
                this.pendingRequests.delete(requestId);
            } else if (this.pendingRequests.has(numericRequestId)) {
                pendingRequest = this.pendingRequests.get(numericRequestId);
                this.pendingRequests.delete(numericRequestId);
            }

            if (pendingRequest) {
                const { resolve, reject } = pendingRequest;

                if (responseCode === 0) {
                    // 请求成功，resolve Promise
                    resolve(responseData);
                } else {
                    // 请求失败，reject Promise
                    reject(new Error(`请求失败: ${responseData.ResponseMsg || '未知错误'}`));
                    return;
                }
            }

            // 处理推送消息
            if (responseCode === 0 && requestType === ApiRequestType.Push) {
                const responseType = responseData.ResponseType;
                if (responseType === 101) {
                    console.log('收到订单推送:', responseData);
                    this.emit('order-update', responseData.Payload);
                }
            }

            // 处理统计数据推送
            if (requestType === ApiRequestType.StatisticData) {
                console.log('收到统计数据推送:', responseData);
                this.emit('statistics-update', responseData.Payload);
            }

        } catch (error) {
            console.error('处理响应数据异常:', error);
        }
    }

    /**
     * 获取适配器状态
     */
    getStatus(): any {
        return {
            connected: this.isConnected(),
            loggedIn: this.hasLoggedIn,
            account: this.config.account
        };
    }
}