/**
 * 华盛通 TCP 客户端 - 基于可工作的实现
 * =========================================
 * 参考工作代码重新实现，正确处理 TCP 粘包和异步响应
 */

import * as net from 'net';
import { EventEmitter } from 'events';

export class HuashengTcpClient extends EventEmitter {
    private socket: net.Socket | null = null;
    private host: string;
    private port: number;
    private isConnected: boolean = false;
    private reconnectInterval: number = 2000;
    private maxReconnectAttempts: number = 3;
    private reconnectAttempts: number = 0;
    
    // 消息缓冲区，用于处理粘包问题
    private messageBuffer: Buffer = Buffer.alloc(0);

    constructor(host: string = '127.0.0.1', port: number = 8080) {
        super();
        this.host = host;
        this.port = port;
    }

    /**
     * 建立与服务器的连接
     */
    connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.isConnected) {
                resolve();
                return;
            }

            this.socket = new net.Socket();
            
            // 连接成功事件
            this.socket.on('connect', () => {
                console.log(`已连接到华盛通服务器 ${this.host}:${this.port}`);
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.emit('connected');
                resolve();
            });

            // 接收数据事件
            this.socket.on('data', (data) => {
                this.handleIncomingData(data);
            });

            // 连接关闭事件
            this.socket.on('close', () => {
                console.log('与华盛通服务器的连接已关闭');
                this.isConnected = false;
                this.emit('disconnected');
                this.attemptReconnect();
            });

            // 连接错误事件
            this.socket.on('error', (error) => {
                console.error('华盛通连接异常:', error.message);
                this.isConnected = false;
                this.emit('error', error);
                
                if (this.reconnectAttempts === 0) {
                    reject(error);
                }
            });

            // 尝试连接
            this.socket.connect(this.port, this.host);
        });
    }

    /**
     * 处理接收到的数据
     * 正确处理TCP粘包问题，解析完整的消息
     */
    private handleIncomingData(data: Buffer): void {
        // 将新数据追加到缓冲区
        this.messageBuffer = Buffer.concat([this.messageBuffer, data]);

        // 持续解析完整的消息
        while (this.messageBuffer.length >= 4) {
            // 读取消息头（4字节，小端序）
            const messageLength = this.messageBuffer.readUInt32LE(0);
            
            // 检查是否收到完整的消息
            if (this.messageBuffer.length >= 4 + messageLength) {
                // 提取消息体
                const messageBody = this.messageBuffer.subarray(4, 4 + messageLength);
                
                // 更新缓冲区，移除已处理的消息
                this.messageBuffer = this.messageBuffer.subarray(4 + messageLength);
                
                // 解析并处理消息
                this.processMessage(messageBody);
            } else {
                // 消息不完整，等待更多数据
                break;
            }
        }
    }

    /**
     * 处理完整的消息
     */
    private processMessage(messageBody: Buffer): void {
        try {
            // 将字节数组转换为UTF-8字符串
            const messageString = messageBody.toString('utf8');
            
            // 解析JSON数据
            const messageData = JSON.parse(messageString);
            
            // 发射消息事件
            this.emit('message', messageData);
        } catch (error) {
            console.error('消息解析错误:', error);
            this.emit('error', new Error(`消息解析失败: ${error}`));
        }
    }

    /**
     * 发送消息到服务器
     */
    sendMessage(requestData: any): Promise<void> {
        return new Promise((resolve, reject) => {
            if (!this.isConnected || !this.socket) {
                reject(new Error('连接未建立，无法发送消息'));
                return;
            }

            try {
                // 将对象转换为JSON字符串
                const jsonString = JSON.stringify(requestData);
                
                // 将JSON字符串编码为UTF-8字节数组
                const messageBody = Buffer.from(jsonString, 'utf8');
                
                // 获取消息体长度
                const messageLength = messageBody.length;
                
                // 创建消息头（4字节，小端序）
                const messageHeader = Buffer.allocUnsafe(4);
                messageHeader.writeUInt32LE(messageLength, 0);
                
                // 拼接消息头和消息体
                const fullMessage = Buffer.concat([messageHeader, messageBody]);
                
                // 发送消息
                this.socket.write(fullMessage, (error) => {
                    if (error) {
                        console.error('发送消息异常:', error.message);
                        reject(error);
                    } else {
                        resolve();
                    }
                });
            } catch (error) {
                console.error('消息编码异常:', error);
                reject(error);
            }
        });
    }

    /**
     * 尝试重新连接
     */
    private attemptReconnect(): void {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`华盛通服务器尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            
            setTimeout(() => {
                this.connect().catch((error) => {
                    console.error('华盛通重连失败:', error.message);
                });
            }, this.reconnectInterval);
        } else {
            console.error('华盛通已达到最大重连次数，停止重连');
            this.emit('maxReconnectAttemptsReached');
        }
    }

    /**
     * 断开连接
     */
    disconnect(): void {
        if (this.socket) {
            this.isConnected = false;
            this.socket.destroy();
            this.socket = null;
            console.log('已主动断开华盛通连接');
        }
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus(): { connected: boolean } {
        return { connected: this.isConnected };
    }
}