/**
 * Electron 任务管理器类型定义
 * ============================
 * 定义任务管理相关的接口、枚举和类型
 */

import { Market, DataType } from "./market-types";

// 重新导出market-types中的类型
export { Market, DataType } from "./market-types";

// 任务状态枚举
export enum TaskStatus {
    Stopped = 'stopped',     // 已停止
    Running = 'running',     // 运行中
    Paused = 'paused',      // 已暂停
    Error = 'error',        // 错误状态
    Liquidated = 'liquidated' // 已清仓
}

// 策略类型枚举
export enum StrategyType {
    BigOrderMonitor = 'strategy_a_big_order_monitor',      // 大单监控
    BreakoutChase = 'strategy_b_breakout_chase',           // 突破追涨
    MeanReversion = 'strategy_e_mean_reversion',           // 均值回归
    Momentum = 'strategy_d_momentum',                      // 动量追踪
    ValueInvesting = 'strategy_f_value_investing',         // 价值投资
    DividendCapture = 'strategy_i_dividend_capture',       // 分红捕获
    PairsTrading = 'strategy_j_pairs_trading',             // 配对交易
    TrendFollowing = 'strategy_k_trend_following'          // 趋势跟踪
}

// 风险条件类型
export enum RiskConditionType {
    Price = 'price',                    // 价格条件
    PnLRatio = 'pnl_ratio',            // 盈亏比例
    Behavior = 'behavior',              // 行为条件
    Time = 'time',                      // 时间条件
    StopLoss = 'stop_loss',            // 止损
    TakeProfit = 'take_profit',        // 止盈
    TrailingStop = 'trailing_stop'      // 跟踪止损
}

// 清仓策略类型
export enum LiquidationType {
    Market = 'market',                  // 市价单
    LimitOptimized = 'limit_optimized', // 优化限价单
    TWAP = 'twap_vwap',                // TWAP/VWAP算法
    SmartExecution = 'smart_execution'  // 智能执行
}

// 任务接口
export interface Task {
    id: string;
    name: string;               // 任务名称
    stockCode: string;          // 股票代码
    stockName: string;          // 股票名称
    market: Market;             // 市场
    strategyName: string;       // 策略名称
    status: TaskStatus;         // 任务状态
    position: number;           // 持仓数量
    avgCost?: number;          // 平均成本价
    pnl: number;               // 浮动盈亏
    createdAt: Date;           // 创建时间
    updatedAt: Date;           // 更新时间
    strategyConfig: StrategyConfig;  // 策略配置
    riskConfig: RiskConfig;          // 风险配置
    isActive: boolean;              // 是否激活
}

// 策略配置基础接口
export interface StrategyConfig {
    strategyType: StrategyType;
    params: Record<string, any>;
    requiredDataTypes: DataType[];  // 策略需要的数据类型
}

// 大单监控策略配置
export interface BigOrderMonitorConfig extends StrategyConfig {
    strategyType: StrategyType.BigOrderMonitor;
    params: {
        monitorThreshold: number;    // 监控买盘阈值(股)
        durationSeconds: number;     // 持续时间(秒)
        targetBrokers: string[];     // 目标经纪商列表
        orderSize: number;           // 单笔买入股数
        priceCondition?: number;     // 价格条件
    };
    requiredDataTypes: [DataType.Quote, DataType.OrderBook, DataType.BrokerQueue];
}

// 突破追涨策略配置
export interface BreakoutChaseConfig extends StrategyConfig {
    strategyType: StrategyType.BreakoutChase;
    params: {
        breakoutPeriod: number;      // 突破周期(日)
        volumeMultiplier: number;    // 成交量放大倍数
        pullbackPercent: number;     // 回踩幅度(%)
    };
    requiredDataTypes: [DataType.Quote, DataType.Ticker];
}

// 风险配置接口
export interface RiskConfig {
    triggerLogic: 'any' | 'all';           // 触发逻辑
    conditions: RiskCondition[];            // 风险条件列表
    liquidationStrategy: LiquidationStrategy; // 清仓策略
}

// 风险条件接口
export interface RiskCondition {
    id: string;
    type: RiskConditionType;
    params: Record<string, any>;
}

// 价格风险条件
export interface PriceRiskCondition extends RiskCondition {
    type: RiskConditionType.Price;
    params: {
        trigger: 'touch' | 'break';     // 触及或突破
        direction: 'above' | 'below';   // 高于或低于
        price: number;                  // 目标价格
    };
}

// 盈亏比例风险条件
export interface PnLRatioRiskCondition extends RiskCondition {
    type: RiskConditionType.PnLRatio;
    params: {
        lossRatio: number;              // 亏损比例(%)
        profitRatio?: number;           // 盈利比例(%)
    };
}

// 清仓策略接口
export interface LiquidationStrategy {
    type: LiquidationType;
    params: Record<string, any>;
}

// 市价单清仓策略
export interface MarketLiquidationStrategy extends LiquidationStrategy {
    type: LiquidationType.Market;
    params: Record<string, never>; // 无参数
}

// 优化限价单清仓策略
export interface LimitOptimizedLiquidationStrategy extends LiquidationStrategy {
    type: LiquidationType.LimitOptimized;
    params: {
        basePrice: 'bid1' | 'ask1' | 'last';  // 基础价格
        priceOffset: number;                   // 价格偏移(ticks)
        direction: 'up' | 'down';             // 偏移方向
        timeoutSeconds: number;                // 超时时间(秒)
        timeoutAction: 'cancel_and_market' | 'cancel_only'; // 超时动作
    };
}

// 任务事件类型
export enum TaskEventType {
    Created = 'task:created',
    Updated = 'task:updated', 
    Deleted = 'task:deleted',
    Started = 'task:started',
    Stopped = 'task:stopped',
    Paused = 'task:paused',
    Liquidated = 'task:liquidated',
    Error = 'task:error',
    PositionChanged = 'task:position_changed',
    PnLChanged = 'task:pnl_changed'
}

// 任务事件接口
export interface TaskEvent {
    type: TaskEventType;
    taskId: string;
    timestamp: number;
    data?: any;
}

// 任务统计信息
export interface TaskStatistics {
    totalTasks: number;
    runningTasks: number;
    pausedTasks: number;
    stoppedTasks: number;
    errorTasks: number;
    liquidatedTasks: number;
    totalMarketValue: number;
    totalPnL: number;
    totalPosition: number;
}

// 任务配置选项
export interface TaskCreateOptions {
    name: string;
    stockCode: string;
    stockName: string;
    market: Market;
    strategyConfig: StrategyConfig;
    riskConfig: RiskConfig;
    autoStart?: boolean;
}

// 任务更新选项
export interface TaskUpdateOptions {
    name?: string;
    strategyConfig?: Partial<StrategyConfig>;
    riskConfig?: Partial<RiskConfig>;
    status?: TaskStatus;
    position?: number;
    avgCost?: number;
    pnl?: number;
}

// 任务管理器配置
export interface TaskManagerConfig {
    maxConcurrentTasks: number;     // 最大并发任务数
    dataUpdateInterval: number;     // 数据更新间隔(ms)
    persistenceEnabled: boolean;    // 是否启用持久化
    riskCheckInterval: number;      // 风险检查间隔(ms)
}

// 任务执行上下文
export interface TaskExecutionContext {
    task: Task;
    marketData: any;               // 实时市场数据
    currentPrice: number;          // 当前价格
    lastUpdateTime: number;        // 最后更新时间
}

// 策略信号
export interface StrategySignal {
    type: 'buy' | 'sell' | 'hold';
    confidence: number;            // 信号置信度 0-1
    quantity?: number;            // 建议数量
    price?: number;               // 建议价格
    reason: string;               // 信号原因
    timestamp: number;            // 信号时间
}

// 策略执行结果
export interface StrategyExecutionResult {
    signal?: StrategySignal;
    shouldContinue: boolean;       // 是否继续执行
    nextCheckTime?: number;        // 下次检查时间
    error?: Error;                // 执行错误
}

// 风险检查结果
export interface RiskCheckResult {
    shouldLiquidate: boolean;      // 是否应该清仓
    triggeredConditions: string[]; // 触发的风险条件ID
    liquidationStrategy?: LiquidationStrategy; // 建议的清仓策略
    urgency: 'low' | 'medium' | 'high'; // 紧急程度
}

// 策略配置字段定义
export interface StrategyConfigField {
    name: string; // 字段名
    label: string; // 显示标签
    type: 'number' | 'text' | 'select' | 'multiselect' | 'checkbox'; // 字段类型
    required?: boolean; // 是否必填
    defaultValue?: any; // 默认值
    options?: { label: string; value: any }[]; // 选项（用于select/multiselect）
    placeholder?: string; // 占位符文本
    validation?: {
        min?: number;
        max?: number;
        step?: number;
        pattern?: string;
    };
    helpText?: string; // 帮助说明
}