/**
 * 华盛通交易 SDK TypeScript 类型定义
 * =================================
 * 基于 ProApi 接口文档 V1.2.2 实现
 */

// API 请求类型枚举
export enum ApiRequestType {
    Login = 100,          // 登录
    QueryFunds = 2,       // 查询资金
    QueryPosition = 3,    // 查询持仓
    QueryEntrust = 5,     // 查询委托记录
    PlaceOrder = 7,       // 下单
    UnOrder = 8,          // 撤单
    ProL2Popup = 9,       // 客户端L2弹窗
    Push = 0,             // 推送消息
    StatisticData = 10001 // 统计数据推送
}

// API 响应类型枚举
export enum ApiResponseType {
    Login = 100,
    OrderPush = 101,     // 订单推送（服务器主动推送）
    Funds = 102,         // 查询资金响应  
    Position = 103,      // 查询持仓响应  
    Entrust = 105,       // 查询委托记录响应
    PlaceOrder = 107,    // 下单响应
    UnOrder = 108,       // 撤单响应
    StatisticData = 10001
}

// 买卖方向枚举
export enum Side {
    Buy = 1,    // 买入
    Sell = 2    // 卖出
}

// 订单类型枚举
export enum OrderType {
    BiddingAndLimited = 0,    // 竞价限价单
    Bidding = 1,              // 竞价单
    EnhanceLimited = 2,       // 增强限价单
    Limited = 3,              // 限价单
    SpecialLimited = 4,       // 特别限价单
    Market = 5                // 市价单
}

// 订单状态枚举
export enum OrderStatus {
    Submitted = "SUBMITTED",   // 已提交
    Pending = "PENDING",       // 待成交
    Filled = "FILLED",         // 已成交
    PartialFilled = "PARTIAL_FILLED", // 部分成交
    Cancelled = "CANCELLED",   // 已撤销
    Rejected = "REJECTED"      // 已拒绝
}

// 基础API请求接口
export interface BaseApiRequest {
    RequestId: number;
    RequestType: ApiRequestType;
    Token?: string;
}

// 基础API响应接口
export interface BaseApiResponse {
    RequestId: number;
    RequestType: number;
    ResponseCode: number;
    ResponseType: number;
    ResponseMsg: string;
}

// 登录请求
export interface LoginRequest extends BaseApiRequest {
    Account: string;
    Password: string;
}

// 登录响应
export interface LoginResponse extends BaseApiResponse {
    Account: string;
    LoginResoult: boolean;
    Token: string;
}

// 资金查询响应
export interface FundsResponse extends BaseApiResponse {
    Payload: {
        // 根据实际API响应调整字段名
        dayNetProfit: number;          // 日净利润
        buyingPower: number;           // 购买力
        dealerAccountId: number;       // 交易员账户ID
        frozenBalance: number;         // 冻结余额
        dayClosePositionProfit: number; // 日平仓盈亏
        enableBalance: number;         // 可用余额
        dayMarketValue: number;        // 日市值
        prevMarketValue: number;       // 昨日市值
        
        // 向后兼容的字段映射（如果API文档中的字段存在）
        AvailableFunds?: number;       // 可用资金
        BuyingPower?: number;          // 购买力
        TotalAssets?: number;          // 总资产
        FrozenFunds?: number;          // 冻结资金
        Currency?: string;             // 币种
    };
}

// 持仓信息
export interface Position {
    StockCode: string;         // 股票代码
    StockName: string;         // 股票名称
    Position: number;          // 持仓数量
    AvgCost: number;          // 平均成本
    CurrentPrice: number;      // 当前价格
    MarketValue: number;       // 市值
    UnrealizedPnL: number;    // 未实现盈亏
    UnrealizedPnLRatio: number; // 未实现盈亏率
}

// 持仓查询响应
export interface PositionResponse extends BaseApiResponse {
    Payload: Position[];
}

// 订单信息
export interface Order {
    OrderId: string;           // 订单号
    StockCode: string;         // 股票代码
    OrderType: string;         // 订单类型
    Side: Side;               // 买卖方向
    Quantity: number;          // 数量
    Price: number;            // 价格
    Status: OrderStatus;       // 状态
    FilledQuantity?: number;   // 已成交数量
    AvgPrice?: number;        // 平均成交价
    Timestamp: number;         // 时间戳
}

// 委托记录查询请求
export interface EntrustRequest extends BaseApiRequest {
    PageNo?: number;          // 页码
    PageSize?: number;        // 每页数量
}

// 委托记录查询响应
export interface EntrustResponse extends BaseApiResponse {
    Payload: Order[];
}

// 下单请求
export interface PlaceOrderRequest extends BaseApiRequest {
    StockCode: string;        // 股票代码
    Price: number;           // 价格
    Qty: number;             // 数量
    Side: Side;              // 买卖方向
    OrderType: OrderType;    // 订单类型
}

// 下单响应
export interface PlaceOrderResponse extends BaseApiResponse {
    Payload: {
        EntrustNo: string;    // 委托编号
        OrderId: string;      // 订单ID
        Status: string;       // 状态
    };
}

// 撤单请求
export interface UnOrderRequest extends BaseApiRequest {
    EntrustNo: string;        // 委托编号
}

// 撤单响应
export interface UnOrderResponse extends BaseApiResponse {
    Payload: {
        EntrustNo: string;    // 委托编号
        Status: string;       // 状态
    };
}

// 连接配置
export interface HuashengConfig {
    host: string;
    port: number;
    account: string;
    password: string;
    enabled: boolean;
    heartbeatInterval?: number;
    reconnectInterval?: number;
    maxReconnectAttempts?: number;
    commandTimeout?: number;
    timeout?: number;
}

// 连接状态
export interface ConnectionStatus {
    connected: boolean;
    lastHeartbeat?: number;
    connectionTime?: number;
    error?: string;
}

// 实时数据回调类型
export type RealtimeDataCallback = (data: any) => void;

// 错误处理回调类型
export type ErrorCallback = (error: Error) => void;