/**
 * 富途牛牛行情适配器
 * ==================
 * 基于富途牛牛 JavaScript SDK (futu-api) 实现的行情数据适配器
 * 提供实时行情数据订阅、快照获取、推送数据处理等功能
 */

import { EventEmitter } from "events";
import FutuWebSocket from "futu-api-ts";
import { MarketAdapter, FutuMarketConfig, ConnectionStatus, Quote, OrderBook, Ticker, BrokerQueue, RealtimeData, DataType, SubscriptionInfo, Market } from "./market-types";

export class FutuMarketAdapter extends EventEmitter implements MarketAdapter {
    private websocket: any; // 使用any类型避免类型检查问题
    private config: FutuMarketConfig | null = null;
    private connectionStatus: ConnectionStatus = ConnectionStatus.Disconnected;
    private subscriptions: Map<string, SubscriptionInfo> = new Map();
    private isInitialized: boolean = false;

    constructor() {
        super();
        this.websocket = new FutuWebSocket();
        this.setupEventHandlers();
    }

    /**
     * 设置事件处理器
     */
    private setupEventHandlers() {
        // 登录成功回调
        this.websocket.onlogin = (ret: boolean, msg: string) => {
            if (ret) {
                this.connectionStatus = ConnectionStatus.Connected;
                this.emit("connected");
                console.log("[FutuMarketAdapter] 连接成功");
            } else {
                this.connectionStatus = ConnectionStatus.Error;
                this.emit("error", new Error(`登录失败: ${msg}`));
                console.error("[FutuMarketAdapter] 登录失败:", msg);
            }
        };

        // 实时数据推送回调
        this.websocket.onPush = (cmd: string, res: any) => {
            this.handlePushData(cmd, res);
        };
    }

    /**
     * 连接到富途OpenD
     */
    async connect(config: FutuMarketConfig): Promise<void> {
        try {
            this.config = config;
            this.connectionStatus = ConnectionStatus.Connecting;

            console.log("[FutuMarketAdapter] 正在连接...", {
                host: config.host,
                port: config.port
            });

            // 启动WebSocket连接
            this.websocket.start(config.host || "127.0.0.1", config.port || 33333, config.useHttps || false, config.encryptKey || "");

            // 等待连接结果
            await this.waitForConnection();
        } catch (error) {
            this.connectionStatus = ConnectionStatus.Error;
            throw new Error(`富途连接失败: ${error}`);
        }
    }

    /**
     * 等待连接完成
     */
    private waitForConnection(): Promise<void> {
        return new Promise((resolve, reject) => {
            const timeout = setTimeout(() => {
                reject(new Error("连接超时"));
            }, 10000); // 10秒超时

            this.once("connected", () => {
                clearTimeout(timeout);
                resolve();
            });

            this.once("error", (error) => {
                clearTimeout(timeout);
                reject(error);
            });
        });
    }

    /**
     * 断开连接
     */
    async disconnect(): Promise<void> {
        try {
            if (this.websocket && this.connectionStatus === ConnectionStatus.Connected) {
                this.websocket.stop();
                this.connectionStatus = ConnectionStatus.Disconnected;
                this.subscriptions.clear();
                this.emit("disconnected");
                console.log("[FutuMarketAdapter] 已断开连接");
            }
        } catch (error) {
            console.error("[FutuMarketAdapter] 断开连接失败:", error);
            throw error;
        }
    }

    /**
     * 检查连接状态
     */
    isConnected(): boolean {
        return this.connectionStatus === ConnectionStatus.Connected;
    }

    /**
     * 获取股票快照数据
     */
    async getQuote(stockCode: string, market: Market = Market.HK): Promise<Quote> {
        if (!this.isConnected()) {
            throw new Error("未连接到富途服务器");
        }

        try {
            const result = await this.websocket.GetSecuritySnapshot({
                c2s: {
                    securityList: [{ code: stockCode, market }]
                }
            });

            if (result && result.s2c && result.s2c.snapshotList && result.s2c.snapshotList.length > 0) {
                const snapshot = result.s2c.snapshotList[0];
                return this.transformQuote(snapshot);
            } else {
                throw new Error("获取快照数据失败");
            }
        } catch (error) {
            console.error("[FutuMarketAdapter] 获取快照失败:", error);
            throw error;
        }
    }

    /**
     * 获取买卖盘数据
     */
    async getOrderBook(stockCode: string, market: Market = Market.HK): Promise<OrderBook> {
        if (!this.isConnected()) {
            throw new Error("未连接到富途服务器");
        }

        try {
            const result = await this.websocket.GetOrderBook({
                c2s: {
                    security: { code: stockCode, market },
                    num: 10 // 10档买卖盘
                }
            });

            if (result && result.s2c) {
                return this.transformOrderBook(result.s2c);
            } else {
                throw new Error("获取买卖盘数据失败");
            }
        } catch (error) {
            console.error("[FutuMarketAdapter] 获取买卖盘失败:", error);
            throw error;
        }
    }

    /**
     * 获取逐笔成交数据
     */
    async getTicker(stockCode: string, market: Market = Market.HK, count: number = 100): Promise<Ticker[]> {
        if (!this.isConnected()) {
            throw new Error("未连接到富途服务器");
        }

        try {
            const result = await this.websocket.GetTicker({
                c2s: {
                    security: { code: stockCode, market },
                    maxRetNum: count
                }
            });

            if (result && result.s2c && result.s2c.tickerList) {
                return result.s2c.tickerList.map((ticker: any) => this.transformTicker(ticker));
            } else {
                return [];
            }
        } catch (error) {
            console.error("[FutuMarketAdapter] 获取逐笔数据失败:", error);
            throw error;
        }
    }

    /**
     * 获取经纪队列数据
     */
    async getBrokerQueue(stockCode: string, market: Market = Market.HK): Promise<BrokerQueue> {
        if (!this.isConnected()) {
            throw new Error("未连接到富途服务器");
        }

        try {
            const result = await this.websocket.GetBrokerQueue({
                c2s: {
                    security: { code: stockCode, market }
                }
            });

            if (result && result.s2c) {
                return this.transformBrokerQueue(result.s2c);
            } else {
                throw new Error("获取经纪队列数据失败");
            }
        } catch (error) {
            console.error("[FutuMarketAdapter] 获取经纪队列失败:", error);
            throw error;
        }
    }

    /**
     * 订阅实时数据
     */
    async subscribe(stockCode: string, market: Market = Market.HK, dataTypes: DataType[]): Promise<string> {
        if (!this.isConnected()) {
            throw new Error("未连接到富途服务器");
        }

        try {
            const subTypeList = this.mapDataTypes(dataTypes);
            const subscriptionKey = `${market}_${stockCode}`;

            const result = await this.websocket.Sub({
                c2s: {
                    securityList: [{ market, code: stockCode }],
                    subTypeList,
                    isSubOrUnSub: true, // true 表示订阅
                    isRegOrUnRegPush: true // true 表示注册推送
                }
            });

            if (result && result.retType === 0) {
                // 记录订阅信息
                this.subscriptions.set(subscriptionKey, {
                    stockCode,
                    market,
                    dataTypes,
                    subscriptionId: subscriptionKey,
                    isActive: true
                });

                console.log("[FutuMarketAdapter] 订阅成功:", subscriptionKey, dataTypes);
                return subscriptionKey;
            } else {
                throw new Error(`订阅失败: ${result?.retMsg || "未知错误"}`);
            }
        } catch (error) {
            console.error("[FutuMarketAdapter] 订阅失败:", error);
            throw error;
        }
    }

    /**
     * 取消订阅
     */
    async unsubscribe(stockCode: string, market: Market = Market.HK, dataTypes: DataType[]): Promise<void> {
        if (!this.isConnected()) {
            throw new Error("未连接到富途服务器");
        }

        try {
            const subTypeList = this.mapDataTypes(dataTypes);
            const subscriptionKey = `${market}_${stockCode}`;

            const result = await this.websocket.Sub({
                c2s: {
                    securityList: [{ market, code: stockCode }],
                    subTypeList,
                    isSubOrUnSub: false, // false 表示取消订阅
                    isRegOrUnRegPush: false // false 表示取消推送
                }
            });

            if (result && result.retType === 0) {
                // 移除订阅记录
                this.subscriptions.delete(subscriptionKey);
                console.log("[FutuMarketAdapter] 取消订阅成功:", subscriptionKey);
            } else {
                throw new Error(`取消订阅失败: ${result?.retMsg || "未知错误"}`);
            }
        } catch (error) {
            console.error("[FutuMarketAdapter] 取消订阅失败:", error);
            throw error;
        }
    }

    /**
     * 处理推送数据
     */
    private handlePushData(cmd: string, res: any) {
        try {
            let realtimeData: RealtimeData | null = null;

            switch (cmd) {
                case "QotUpdateBasicQot":
                    // 基础报价推送
                    realtimeData = this.handleQuotePush(res);
                    break;
                case "QotUpdateOrderBook":
                    // 买卖盘推送
                    realtimeData = this.handleOrderBookPush(res);
                    break;
                case "QotUpdateTicker":
                    // 逐笔推送
                    realtimeData = this.handleTickerPush(res);
                    break;
                case "QotUpdateBrokerQueue":
                    // 经纪队列推送
                    realtimeData = this.handleBrokerQueuePush(res);
                    break;
                default:
                    console.log("[FutuMarketAdapter] 未处理的推送类型:", cmd);
                    return;
            }

            if (realtimeData) {
                this.emit("data", realtimeData);
            }
        } catch (error) {
            console.error("[FutuMarketAdapter] 处理推送数据失败:", error);
            this.emit("error", error);
        }
    }

    // 数据转换方法
    private transformQuote(snapshot: any): Quote {
        return {
            stockCode: snapshot.basic?.security?.code || "",
            market: snapshot.basic?.security?.market || Market.HK,
            price: snapshot.basic?.curPrice || 0,
            change: snapshot.basic?.priceChangeVal || 0,
            changePercent: snapshot.basic?.changePct || 0,
            volume: snapshot.basic?.volume || 0,
            turnover: snapshot.basic?.turnover || 0,
            high: snapshot.basic?.highPrice || 0,
            low: snapshot.basic?.lowPrice || 0,
            open: snapshot.basic?.openPrice || 0,
            lastClose: snapshot.basic?.lastClosePrice || 0,
            timestamp: Date.now()
        };
    }

    private transformOrderBook(data: any): OrderBook {
        return {
            stockCode: data.security?.code || "",
            market: data.security?.market || Market.HK,
            bids: (data.orderBookAskList || []).map((item: any) => ({
                price: item.price || 0,
                volume: item.volume || 0,
                orderCount: item.orederCount || 0
            })),
            asks: (data.orderBookBidList || []).map((item: any) => ({
                price: item.price || 0,
                volume: item.volume || 0,
                orderCount: item.orederCount || 0
            })),
            timestamp: Date.now()
        };
    }

    private transformTicker(ticker: any): Ticker {
        return {
            price: ticker.price || 0,
            volume: ticker.volume || 0,
            direction: ticker.dir || 0,
            timestamp: ticker.time || Date.now(),
            turnover: ticker.turnover || 0
        };
    }

    private transformBrokerQueue(data: any): BrokerQueue {
        return {
            stockCode: data.security?.code || "",
            market: data.security?.market || Market.HK,
            bidBrokers: data.orderBookBidList || [],
            askBrokers: data.orderBookAskList || [],
            timestamp: Date.now()
        };
    }

    // 推送数据处理方法
    private handleQuotePush(res: any): RealtimeData | null {
        if (res && res.s2c && res.s2c.basicQotList && res.s2c.basicQotList.length > 0) {
            const quote = this.transformQuote({ basic: res.s2c.basicQotList[0] });
            return {
                type: DataType.Quote,
                stockCode: quote.stockCode,
                market: quote.market,
                data: quote,
                timestamp: Date.now()
            };
        }
        return null;
    }

    private handleOrderBookPush(res: any): RealtimeData | null {
        if (res && res.s2c) {
            const orderBook = this.transformOrderBook(res.s2c);
            return {
                type: DataType.OrderBook,
                stockCode: orderBook.stockCode,
                market: orderBook.market,
                data: orderBook,
                timestamp: Date.now()
            };
        }
        return null;
    }

    private handleTickerPush(res: any): RealtimeData | null {
        if (res && res.s2c && res.s2c.tickerList && res.s2c.tickerList.length > 0) {
            const ticker = this.transformTicker(res.s2c.tickerList[0]);
            return {
                type: DataType.Ticker,
                stockCode: res.s2c.security?.code || "",
                market: res.s2c.security?.market || Market.HK,
                data: ticker,
                timestamp: Date.now()
            };
        }
        return null;
    }

    private handleBrokerQueuePush(res: any): RealtimeData | null {
        if (res && res.s2c) {
            const brokerQueue = this.transformBrokerQueue(res.s2c);
            return {
                type: DataType.BrokerQueue,
                stockCode: brokerQueue.stockCode,
                market: brokerQueue.market,
                data: brokerQueue,
                timestamp: Date.now()
            };
        }
        return null;
    }

    /**
     * 映射数据类型到富途SDK的订阅类型
     */
    private mapDataTypes(dataTypes: DataType[]): number[] {
        const typeMap: Record<DataType, number> = {
            [DataType.Quote]: 1, // 基础报价
            [DataType.OrderBook]: 2, // 买卖盘
            [DataType.Ticker]: 4, // 逐笔
            [DataType.BrokerQueue]: 3, // 经纪队列
            [DataType.KLine]: 5 // K线（分时）
        };

        return dataTypes.map((type) => typeMap[type]).filter(Boolean);
    }

    /**
     * 获取活跃订阅列表
     */
    getActiveSubscriptions(): SubscriptionInfo[] {
        return Array.from(this.subscriptions.values()).filter((sub) => sub.isActive);
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus(): ConnectionStatus {
        return this.connectionStatus;
    }
}
