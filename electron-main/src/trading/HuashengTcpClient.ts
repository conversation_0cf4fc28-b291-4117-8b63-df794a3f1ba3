/**
 * 华盛通 TCP 客户端
 * ==================
 * 基于 Node.js net 模块实现的华盛通交易API TCP客户端
 * 支持连接管理、消息发送接收、自动重连等功能
 */

import * as net from "net";
import { EventEmitter } from "events";
import { HuashengConfig, ConnectionStatus, BaseApiRequest, BaseApiResponse, RealtimeDataCallback, ErrorCallback } from "./types";

export class HuashengTcpClient extends EventEmitter {
    private socket: net.Socket | null = null;
    private config: HuashengConfig;
    private connectionStatus: ConnectionStatus = { connected: false };
    private requestId: number = 1;
    private reconnectTimer: NodeJS.Timeout | null = null;
    private heartbeatTimer: NodeJS.Timeout | null = null;
    private pendingRequests: Map<
        string,
        {
            resolve: (value: any) => void;
            reject: (reason?: any) => void;
            timeout: NodeJS.Timeout;
        }
    > = new Map();

    // 重连控制
    private reconnectInterval: number = 1000; // 恢复5秒重连间隔

    constructor(config: HuashengConfig) {
        super();
        this.config = {
            timeout: 30000,
            ...config
        };
    }

    /**
     * 连接到华盛通服务器
     */
    async connect(): Promise<void> {
        return new Promise((resolve, reject) => {
            if (this.connectionStatus.connected) {
                resolve();
                return;
            }

            try {
                // 创建 TCP Socket
                this.socket = new net.Socket();

                // 设置连接超时
                this.socket.setTimeout(this.config.timeout!);

                // 连接成功事件
                this.socket.on("connect", () => {
                    console.log(`华盛通 TCP 连接已建立: ${this.config.host}:${this.config.port}`);
                    this.connectionStatus = {
                        connected: true,
                        connectionTime: Date.now()
                    };

                    // 开始心跳检测
                    this.startHeartbeat();

                    this.emit("connected");
                    resolve();
                });

                // 数据接收事件
                this.socket.on("data", (buffer: Buffer) => {
                    this.handleIncomingData(buffer);
                });

                // 连接关闭事件
                this.socket.on("close", (hadError: boolean) => {
                    console.log(`华盛通 TCP 连接已关闭${hadError ? " (有错误)" : ""}`);
                    this.connectionStatus.connected = false;
                    this.emit("disconnected", hadError);

                    // 自动重连
                    this.scheduleReconnect();
                });

                // 错误处理
                this.socket.on("error", (error: Error) => {
                    console.error("华盛通 TCP 连接错误:", error);
                    this.connectionStatus = {
                        connected: false,
                        error: error.message
                    };
                    this.emit("error", error);
                    reject(error);
                });

                // 超时处理
                this.socket.on("timeout", () => {
                    console.warn("华盛通 TCP 连接超时");
                    this.socket?.destroy();
                });

                // 发起连接
                this.socket.connect(this.config.port, this.config.host);
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 断开连接
     */
    async disconnect(): Promise<void> {
        return new Promise((resolve) => {
            // 清理定时器
            if (this.reconnectTimer) {
                clearTimeout(this.reconnectTimer);
                this.reconnectTimer = null;
            }

            if (this.heartbeatTimer) {
                clearInterval(this.heartbeatTimer);
                this.heartbeatTimer = null;
            }

            // 清理待处理请求
            this.pendingRequests.forEach(({ reject, timeout }) => {
                clearTimeout(timeout);
                reject(new Error("Connection closed"));
            });
            this.pendingRequests.clear();

            if (this.socket) {
                this.socket.once("close", () => {
                    this.connectionStatus.connected = false;
                    resolve();
                });
                this.socket.destroy();
            } else {
                this.connectionStatus.connected = false;
                resolve();
            }
        });
    }

    /**
     * 发送请求并等待响应
     */
    async sendRequest<T extends BaseApiResponse>(request: BaseApiRequest): Promise<T> {
        if (!this.connectionStatus.connected || !this.socket) {
            throw new Error("未连接到华盛通服务器");
        }

        // 设置请求ID
        request.RequestId = this.requestId++;

        return new Promise((resolve, reject) => {
            try {
                // 序列化请求数据
                const jsonStr = JSON.stringify(request);
                const bodyBytes = Buffer.from(jsonStr, "utf-8");

                // 创建消息头（4字节，小端序）
                const headerBytes = Buffer.allocUnsafe(4);
                headerBytes.writeUInt32LE(bodyBytes.length, 0);

                // 拼接消息头和消息体
                const messageBytes = Buffer.concat([headerBytes, bodyBytes]);

                // 设置请求超时
                // 统一转换 RequestId 为字符串
                const requestIdString = String(request.RequestId);

                const timeout = setTimeout(() => {
                    this.pendingRequests.delete(requestIdString);
                    reject(new Error(`请求超时: RequestId ${request.RequestId}`));
                }, this.config.timeout!);

                // 保存待处理请求
                this.pendingRequests.set(requestIdString, { resolve, reject, timeout });

                // 发送消息
                this.socket!.write(messageBytes, (error) => {
                    if (error) {
                        this.pendingRequests.delete(requestIdString);
                        clearTimeout(timeout);
                        reject(error);
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus(): ConnectionStatus {
        return { ...this.connectionStatus };
    }

    /**
     * 处理接收到的数据
     */
    private handleIncomingData(buffer: Buffer): void {
        try {
            let offset = 0;

            // 可能接收到多个消息，需要循环处理
            while (offset < buffer.length) {
                // 确保至少有4字节的消息头
                if (buffer.length - offset < 4) {
                    console.warn("接收到不完整的消息头");
                    break;
                }

                // 读取消息头（消息体长度）
                const bodyLength = buffer.readUInt32LE(offset);
                offset += 4;

                // 确保消息体完整
                if (buffer.length - offset < bodyLength) {
                    console.warn("接收到不完整的消息体");
                    break;
                }

                // 读取消息体
                const bodyBytes = buffer.subarray(offset, offset + bodyLength);
                offset += bodyLength;

                // 解析JSON消息
                const jsonStr = bodyBytes.toString("utf-8");
                const response: BaseApiResponse = JSON.parse(jsonStr);

                this.handleResponse(response);
            }
        } catch (error) {
            console.error("处理接收数据时出错:", error);
            this.emit("error", error);
        }
    }

    /**
     * 处理响应消息
     */
    private handleResponse(response: BaseApiResponse): void {
        const { RequestId, RequestType, ResponseCode, ResponseType } = response;

        // 如果是推送消息（RequestType = 0 或 ResponseType = 10001 统计数据推送）
        // 或者服务器主动发送的数据（RequestId为字符串且不在我们的待处理列表中）
        if (RequestType === 0 || ResponseType === 10001 || (typeof RequestId === "string" && !this.pendingRequests.has(String(RequestId)))) {
            console.log("收到推送消息:", response);
            this.emit("push", response);
            return;
        }

        // 处理请求响应 - 统一转换 RequestId 为字符串进行匹配
        const requestIdString = String(RequestId);
        const pendingRequest = this.pendingRequests.get(requestIdString);
        if (pendingRequest) {
            clearTimeout(pendingRequest.timeout);
            this.pendingRequests.delete(requestIdString);

            if (ResponseCode === 0) {
                // 成功响应
                pendingRequest.resolve(response);
            } else {
                // 错误响应
                const error = new Error(`API 错误: ${response.ResponseMsg} (Code: ${ResponseCode})`);
                pendingRequest.reject(error);
            }
        } else {
            // 可能是主动推送或未匹配的响应
            console.log("收到未匹配的响应 (可能是服务器主动推送):", response);
            this.emit("unmatched-response", response);
        }
    }

    /**
     * 开始心跳检测
     */
    private startHeartbeat(): void {
        // 每60秒发送一次心跳（减少频率避免过多干扰）
        this.heartbeatTimer = setInterval(() => {
            if (this.connectionStatus.connected) {
                this.connectionStatus.lastHeartbeat = Date.now();
                // 心跳检测的具体实现可以根据API文档调整
                console.log("发送心跳检测");
                this.emit("heartbeat");
            }
        }, 60000);
    }

    /**
     * 安排重连
     */
    private scheduleReconnect(): void {
        if (this.reconnectTimer) {
            return; // 已经在重连中
        }

        console.log(`${this.reconnectInterval / 1000}秒后尝试重连华盛通服务器...`);
        this.reconnectTimer = setTimeout(async () => {
            this.reconnectTimer = null;
            try {
                await this.connect();
                console.log("华盛通服务器重连成功");
            } catch (error) {
                console.error("华盛通服务器重连失败:", error);
                // 继续安排下次重连
                this.scheduleReconnect();
            }
        }, this.reconnectInterval);
    }
}
