/**
 * 策略工厂
 * ========
 * 提供策略模板管理、策略创建和配置验证的统一接口
 */

import { EventEmitter } from "events";
import {
    StrategyType,
    StrategyConfig,
    StrategyConfigField,
    DataType,
    BigOrderMonitorConfig,
    BreakoutChaseConfig
} from "./task-types";

// 策略模板接口
export interface StrategyTemplate {
    id: string;
    name: string;
    description: string;
    strategyType: StrategyType;
    configFields: StrategyConfigField[];
    defaultConfig: Record<string, any>;
    requiredDataTypes: DataType[];
    category: 'momentum' | 'reversal' | 'arbitrage' | 'fundamental' | 'technical';
    riskLevel: 'low' | 'medium' | 'high';
    complexity: 'beginner' | 'intermediate' | 'advanced';
}

export class StrategyFactory extends EventEmitter {
    private templates: Map<StrategyType, StrategyTemplate> = new Map();

    constructor() {
        super();
        this.registerDefaultTemplates();
    }

    /**
     * 注册默认策略模板
     */
    private registerDefaultTemplates(): void {
        // 注册大单监控策略模板
        this.registerTemplate({
            id: 'big_order_monitor',
            name: '大单监控策略',
            description: '监控主力经纪商大额买单，在特定条件下跟单',
            strategyType: StrategyType.BigOrderMonitor,
            configFields: [
                {
                    name: 'monitorThreshold',
                    label: '监控阈值',
                    type: 'number',
                    required: true,
                    defaultValue: 10000,
                    validation: { min: 1000, max: 100000000, step: 1000 },
                    helpText: '单笔买盘超过此数量时触发监控（股）'
                },
                {
                    name: 'durationSeconds',
                    label: '持续时间',
                    type: 'number',
                    required: true,
                    defaultValue: 300,
                    validation: { min: 60, max: 3600, step: 30 },
                    helpText: '大单需要持续的时间（秒）'
                },
                {
                    name: 'targetBrokers',
                    label: '目标经纪商',
                    type: 'multiselect',
                    required: true,
                    defaultValue: ['高盛', '摩根士丹利'],
                    options: [
                        { label: '高盛', value: '高盛' },
                        { label: '摩根士丹利', value: '摩根士丹利' },
                        { label: '瑞银', value: '瑞银' },
                        { label: '中信建投', value: '中信建投' },
                        { label: '招商证券', value: '招商证券' },
                        { label: '中金公司', value: '中金公司' }
                    ],
                    helpText: '监控这些经纪商的大单行为'
                },
                {
                    name: 'orderSize',
                    label: '跟单数量',
                    type: 'number',
                    required: true,
                    defaultValue: 500,
                    validation: { min: 100, max: 10000, step: 100 },
                    helpText: '检测到大单后的跟单股数'
                },
                {
                    name: 'priceCondition',
                    label: '价格条件',
                    type: 'number',
                    required: false,
                    defaultValue: undefined,
                    validation: { min: 0, step: 0.01 },
                    helpText: '可选：仅在股价低于此价格时执行（留空表示无限制）'
                }
            ],
            defaultConfig: {
                monitorThreshold: 10000,
                durationSeconds: 300,
                targetBrokers: ['高盛', '摩根士丹利'],
                orderSize: 500
            },
            requiredDataTypes: [DataType.Quote, DataType.OrderBook, DataType.BrokerQueue],
            category: 'momentum',
            riskLevel: 'medium',
            complexity: 'intermediate'
        });

        // 注册突破追涨策略模板
        this.registerTemplate({
            id: 'breakout_chase',
            name: '突破追涨策略',
            description: '监控价格突破前期高点，配合成交量放大信号追涨',
            strategyType: StrategyType.BreakoutChase,
            configFields: [
                {
                    name: 'breakoutPeriod',
                    label: '突破周期',
                    type: 'number',
                    required: true,
                    defaultValue: 20,
                    validation: { min: 5, max: 100, step: 1 },
                    helpText: '计算突破的历史周期天数'
                },
                {
                    name: 'volumeMultiplier',
                    label: '成交量放大倍数',
                    type: 'number',
                    required: true,
                    defaultValue: 2.0,
                    validation: { min: 1.1, max: 10.0, step: 0.1 },
                    helpText: '成交量相比历史平均的放大倍数'
                },
                {
                    name: 'pullbackPercent',
                    label: '回踩幅度',
                    type: 'number',
                    required: true,
                    defaultValue: 3.0,
                    validation: { min: 0.5, max: 10.0, step: 0.1 },
                    helpText: '允许的最大回踩幅度（%）'
                }
            ],
            defaultConfig: {
                breakoutPeriod: 20,
                volumeMultiplier: 2.0,
                pullbackPercent: 3.0
            },
            requiredDataTypes: [DataType.Quote, DataType.Ticker],
            category: 'momentum',
            riskLevel: 'high',
            complexity: 'intermediate'
        });

        // 注册均值回归策略模板
        this.registerTemplate({
            id: 'mean_reversion',
            name: '均值回归策略',
            description: '基于统计均值回归原理，在价格偏离均值时进行反向操作',
            strategyType: StrategyType.MeanReversion,
            configFields: [
                {
                    name: 'lookbackPeriod',
                    label: '回望周期',
                    type: 'number',
                    required: true,
                    defaultValue: 30,
                    validation: { min: 10, max: 100, step: 1 },
                    helpText: '计算均值的历史天数'
                },
                {
                    name: 'deviationThreshold',
                    label: '偏离阈值',
                    type: 'number',
                    required: true,
                    defaultValue: 2.0,
                    validation: { min: 1.0, max: 5.0, step: 0.1 },
                    helpText: '触发交易的标准差倍数'
                },
                {
                    name: 'holdingPeriod',
                    label: '持仓周期',
                    type: 'number',
                    required: true,
                    defaultValue: 5,
                    validation: { min: 1, max: 30, step: 1 },
                    helpText: '预期的持仓天数'
                }
            ],
            defaultConfig: {
                lookbackPeriod: 30,
                deviationThreshold: 2.0,
                holdingPeriod: 5
            },
            requiredDataTypes: [DataType.Quote],
            category: 'reversal',
            riskLevel: 'medium',
            complexity: 'intermediate'
        });

        // 注册动量追踪策略模板
        this.registerTemplate({
            id: 'momentum_tracking',
            name: '动量追踪策略',
            description: '追踪价格动量趋势，在趋势确立时进入市场',
            strategyType: StrategyType.Momentum,
            configFields: [
                {
                    name: 'momentumPeriod',
                    label: '动量周期',
                    type: 'number',
                    required: true,
                    defaultValue: 15,
                    validation: { min: 5, max: 50, step: 1 },
                    helpText: '计算动量的历史天数'
                },
                {
                    name: 'strengthThreshold',
                    label: '强度阈值',
                    type: 'number',
                    required: true,
                    defaultValue: 0.7,
                    validation: { min: 0.1, max: 1.0, step: 0.1 },
                    helpText: '动量强度阈值（0-1）'
                },
                {
                    name: 'entrySignal',
                    label: '入场信号',
                    type: 'select',
                    required: true,
                    defaultValue: 'ma_cross',
                    options: [
                        { label: '均线交叉', value: 'ma_cross' },
                        { label: 'MACD金叉', value: 'macd_golden' },
                        { label: 'RSI突破', value: 'rsi_breakout' }
                    ],
                    helpText: '选择动量入场信号类型'
                }
            ],
            defaultConfig: {
                momentumPeriod: 15,
                strengthThreshold: 0.7,
                entrySignal: 'ma_cross'
            },
            requiredDataTypes: [DataType.Quote],
            category: 'momentum',
            riskLevel: 'medium',
            complexity: 'beginner'
        });
    }

    /**
     * 注册策略模板
     */
    registerTemplate(template: StrategyTemplate): void {
        this.templates.set(template.strategyType, template);
        console.log(`[StrategyFactory] 注册策略模板: ${template.name}`);
        this.emit('templateRegistered', template);
    }

    /**
     * 获取策略模板
     */
    getTemplate(strategyType: StrategyType): StrategyTemplate | null {
        return this.templates.get(strategyType) || null;
    }

    /**
     * 获取所有策略模板
     */
    getAllTemplates(): StrategyTemplate[] {
        return Array.from(this.templates.values());
    }

    /**
     * 按分类获取策略模板
     */
    getTemplatesByCategory(category: StrategyTemplate['category']): StrategyTemplate[] {
        return Array.from(this.templates.values()).filter(template => template.category === category);
    }

    /**
     * 按风险等级获取策略模板
     */
    getTemplatesByRiskLevel(riskLevel: StrategyTemplate['riskLevel']): StrategyTemplate[] {
        return Array.from(this.templates.values()).filter(template => template.riskLevel === riskLevel);
    }

    /**
     * 按复杂度获取策略模板
     */
    getTemplatesByComplexity(complexity: StrategyTemplate['complexity']): StrategyTemplate[] {
        return Array.from(this.templates.values()).filter(template => template.complexity === complexity);
    }

    /**
     * 创建策略配置
     */
    createStrategyConfig(strategyType: StrategyType, params: Record<string, any>): StrategyConfig {
        const template = this.getTemplate(strategyType);
        if (!template) {
            throw new Error(`未找到策略模板: ${strategyType}`);
        }

        // 合并默认配置和用户参数
        const mergedParams = { ...template.defaultConfig, ...params };

        // 验证配置
        this.validateStrategyParams(template, mergedParams);

        return {
            strategyType,
            params: mergedParams,
            requiredDataTypes: template.requiredDataTypes
        };
    }

    /**
     * 验证策略参数
     */
    validateStrategyParams(template: StrategyTemplate, params: Record<string, any>): void {
        for (const field of template.configFields) {
            const value = params[field.name];

            // 检查必填字段
            if (field.required && (value === undefined || value === null)) {
                throw new Error(`必填字段缺失: ${field.label}`);
            }

            // 跳过空值的验证
            if (value === undefined || value === null) {
                continue;
            }

            // 类型验证
            switch (field.type) {
                case 'number':
                    if (typeof value !== 'number' || isNaN(value)) {
                        throw new Error(`${field.label} 必须是有效数字`);
                    }
                    
                    // 数值范围验证
                    if (field.validation) {
                        if (field.validation.min !== undefined && value < field.validation.min) {
                            throw new Error(`${field.label} 不能小于 ${field.validation.min}`);
                        }
                        if (field.validation.max !== undefined && value > field.validation.max) {
                            throw new Error(`${field.label} 不能大于 ${field.validation.max}`);
                        }
                        if (field.validation.step !== undefined) {
                            const remainder = (value - (field.validation.min || 0)) % field.validation.step;
                            if (Math.abs(remainder) > 1e-10) {
                                throw new Error(`${field.label} 必须是 ${field.validation.step} 的倍数`);
                            }
                        }
                    }
                    break;

                case 'text':
                    if (typeof value !== 'string') {
                        throw new Error(`${field.label} 必须是文本`);
                    }
                    break;

                case 'select':
                    if (field.options && !field.options.some(option => option.value === value)) {
                        throw new Error(`${field.label} 的值无效`);
                    }
                    break;

                case 'multiselect':
                    if (!Array.isArray(value)) {
                        throw new Error(`${field.label} 必须是数组`);
                    }
                    if (field.options) {
                        const validValues = field.options.map(option => option.value);
                        for (const item of value) {
                            if (!validValues.includes(item)) {
                                throw new Error(`${field.label} 包含无效选项: ${item}`);
                            }
                        }
                    }
                    break;

                case 'checkbox':
                    if (typeof value !== 'boolean') {
                        throw new Error(`${field.label} 必须是布尔值`);
                    }
                    break;
            }
        }
    }

    /**
     * 验证策略配置
     */
    validateStrategyConfig(config: StrategyConfig): boolean {
        try {
            const template = this.getTemplate(config.strategyType);
            if (!template) {
                console.error(`[StrategyFactory] 未找到策略模板: ${config.strategyType}`);
                return false;
            }

            this.validateStrategyParams(template, config.params);
            return true;
        } catch (error) {
            console.error(`[StrategyFactory] 策略配置验证失败:`, error);
            return false;
        }
    }

    /**
     * 获取策略配置摘要
     */
    getConfigSummary(config: StrategyConfig): string {
        const template = this.getTemplate(config.strategyType);
        if (!template) {
            return `未知策略: ${config.strategyType}`;
        }

        const keyParams = template.configFields
            .filter(field => field.required)
            .map(field => {
                const value = config.params[field.name];
                return `${field.label}: ${value}`;
            })
            .join(', ');

        return `${template.name} (${keyParams})`;
    }

    /**
     * 复制策略配置
     */
    cloneStrategyConfig(config: StrategyConfig): StrategyConfig {
        return {
            strategyType: config.strategyType,
            params: JSON.parse(JSON.stringify(config.params)),
            requiredDataTypes: [...config.requiredDataTypes]
        };
    }

    /**
     * 更新策略配置
     */
    updateStrategyConfig(config: StrategyConfig, updates: Partial<StrategyConfig>): StrategyConfig {
        const updatedConfig = this.cloneStrategyConfig(config);
        
        if (updates.strategyType) {
            updatedConfig.strategyType = updates.strategyType;
        }
        
        if (updates.params) {
            updatedConfig.params = { ...updatedConfig.params, ...updates.params };
        }
        
        if (updates.requiredDataTypes) {
            updatedConfig.requiredDataTypes = updates.requiredDataTypes;
        }

        // 验证更新后的配置
        this.validateStrategyConfig(updatedConfig);
        
        return updatedConfig;
    }

    /**
     * 搜索策略模板
     */
    searchTemplates(query: string): StrategyTemplate[] {
        const lowerQuery = query.toLowerCase();
        return Array.from(this.templates.values()).filter(template => 
            template.name.toLowerCase().includes(lowerQuery) ||
            template.description.toLowerCase().includes(lowerQuery) ||
            template.id.toLowerCase().includes(lowerQuery)
        );
    }

    /**
     * 获取策略统计信息
     */
    getStatistics(): {
        totalTemplates: number;
        byCategory: Record<string, number>;
        byRiskLevel: Record<string, number>;
        byComplexity: Record<string, number>;
    } {
        const templates = Array.from(this.templates.values());
        
        const byCategory: Record<string, number> = {};
        const byRiskLevel: Record<string, number> = {};
        const byComplexity: Record<string, number> = {};
        
        templates.forEach(template => {
            byCategory[template.category] = (byCategory[template.category] || 0) + 1;
            byRiskLevel[template.riskLevel] = (byRiskLevel[template.riskLevel] || 0) + 1;
            byComplexity[template.complexity] = (byComplexity[template.complexity] || 0) + 1;
        });
        
        return {
            totalTemplates: templates.length,
            byCategory,
            byRiskLevel,
            byComplexity
        };
    }
}