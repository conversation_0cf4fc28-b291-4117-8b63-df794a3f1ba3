/**
 * Electron 交易系统模块导出
 * ===========================
 * 统一导出所有交易系统相关的类和接口
 */

// 核心系统
export { TradingSystem } from './TradingSystem';
export { TaskManager } from './TaskManager';
export { StrategyEngine } from './StrategyEngine';
export { RiskManager } from './RiskManager';
export { StrategyFactory } from './StrategyFactory';
export { MarketDataManager } from './MarketDataManager';

// 策略实现
export {
    Strategy,
    BaseStrategy,
    BigOrderMonitorStrategy,
    BreakoutChaseStrategy
} from './StrategyEngine';

// 风险管理组件
export {
    RiskConditionChecker,
    LiquidationExecutor,
    PriceRiskChecker,
    PnLRatioRiskChecker,
    StopLossRiskChecker,
    TakeProfitRiskChecker,
    TimeRiskChecker,
    TrailingStopRiskChecker,
    MarketLiquidationExecutor,
    LimitOptimizedLiquidationExecutor,
    SmartLiquidationExecutor,
    TWAPLiquidationExecutor
} from './RiskManager';

// 类型定义
export type {
    // 任务相关类型
    Task,
    TaskStatus,
    TaskEvent,
    TaskEventType,
    TaskStatistics,
    TaskCreateOptions,
    TaskUpdateOptions,
    TaskManagerConfig,
    TaskExecutionContext,
    
    // 策略相关类型
    StrategyConfig,
    StrategyType,
    BigOrderMonitorConfig,
    BreakoutChaseConfig,
    StrategySignal,
    StrategyExecutionResult,
    
    // 风险管理类型
    RiskConfig,
    RiskCondition,
    RiskConditionType,
    RiskCheckResult,
    LiquidationStrategy,
    LiquidationType,
    PriceRiskCondition,
    PnLRatioRiskCondition,
    
    // 策略工厂类型
    StrategyConfigField
} from './task-types';

export type {
    // 市场数据类型
    Market,
    DataType,
    RealtimeData,
    Quote,
    OrderBook,
    Ticker,
    BrokerQueue
} from './market-types';

// 策略模板类型
export type { StrategyTemplate } from './StrategyFactory';

// 系统配置类型
export type { TradingSystemConfig } from './TradingSystem';

// 常量和枚举
export {
    TaskStatus as TaskStatusEnum,
    TaskEventType as TaskEventTypeEnum,
    StrategyType as StrategyTypeEnum,
    RiskConditionType as RiskConditionTypeEnum,
    LiquidationType as LiquidationTypeEnum
} from './task-types';

export {
    Market as MarketEnum,
    DataType as DataTypeEnum
} from './market-types';