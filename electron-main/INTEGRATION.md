# Electron 交易系统集成文档

## 📋 概述

本文档描述了如何在 Electron 主进程中集成和使用交易系统。交易系统已完全集成到 Electron 应用中，提供完整的任务管理、行情数据处理和交易执行功能。

## 🏗️ 架构概览

```
Electron Main Process
├── TradingSystemService        # 交易系统服务管理
├── IPC Handlers               # 渲染进程通信接口
├── Trading System             # 核心交易系统
│   ├── TaskManager           # 任务管理器
│   ├── MarketDataManager     # 行情数据管理器
│   ├── TradingManager        # 交易管理器
│   ├── StrategyEngine        # 策略引擎
│   └── RiskManager          # 风险管理器
└── Configuration             # 环境配置管理
```

## 🚀 快速开始

### 1. 启动应用

```bash
# 开发环境测试
./test-integration.sh

# 或者直接启动
npm start
```

### 2. 自动初始化

应用启动时会自动：
- 创建交易系统服务实例
- 初始化所有子系统
- 建立行情和交易连接
- 注册 IPC 通信接口

## 📡 IPC 通信接口

### 交易系统状态管理

#### 获取系统状态
```typescript
// 渲染进程
const status = await window.electron.ipcRenderer.invoke('trading-system:getStatus');
console.log('系统状态:', status);
```

#### 手动初始化系统
```typescript
const result = await window.electron.ipcRenderer.invoke('trading-system:initialize');
```

#### 关闭系统
```typescript
const result = await window.electron.ipcRenderer.invoke('trading-system:shutdown');
```

### 行情和交易状态

#### 获取行情连接状态
```typescript
const marketStatus = await window.electron.ipcRenderer.invoke('trading-system:getMarketStatus');
```

#### 获取交易连接状态
```typescript
const tradingStatus = await window.electron.ipcRenderer.invoke('trading-system:getTradingStatus');
```

### 任务管理

#### 创建交易任务
```typescript
const taskConfig = {
    stockCode: 'HK.00700',
    strategyType: 'grid_trading',
    strategyParams: {
        gridSpacing: 0.5,
        gridLevels: 10,
        positionSize: 100
    }
};

const result = await window.electron.ipcRenderer.invoke('task:create', taskConfig);
const taskId = result.data.taskId;
```

#### 启动任务
```typescript
const result = await window.electron.ipcRenderer.invoke('task:start', { taskId });
```

#### 停止任务
```typescript
const result = await window.electron.ipcRenderer.invoke('task:stop', { taskId });
```

#### 获取任务列表
```typescript
const result = await window.electron.ipcRenderer.invoke('task:getList');
const tasks = result.data;
```

#### 获取任务状态
```typescript
const result = await window.electron.ipcRenderer.invoke('task:getStatus', { taskId });
const status = result.data.status;
```

#### 删除任务
```typescript
const result = await window.electron.ipcRenderer.invoke('task:delete', { taskId });
```

## 📊 实时事件监听

### 系统状态事件

```typescript
// 监听系统就绪事件
window.electron.ipcRenderer.on('trading-system:service-ready', (event, data) => {
    console.log('交易系统服务就绪:', data);
});

// 监听系统心跳
window.electron.ipcRenderer.on('trading-system:heartbeat', (event, status) => {
    console.log('系统状态更新:', status);
});

// 监听初始化错误
window.electron.ipcRenderer.on('trading-system:initialization-error', (event, error) => {
    console.error('系统初始化失败:', error);
});
```

### 任务事件

```typescript
// 监听任务创建
window.electron.ipcRenderer.on('trading-system:task-created', (event, data) => {
    console.log('任务已创建:', data.taskId);
});

// 监听任务启动
window.electron.ipcRenderer.on('trading-system:task-started', (event, data) => {
    console.log('任务已启动:', data.taskId);
});

// 监听任务停止
window.electron.ipcRenderer.on('trading-system:task-stopped', (event, data) => {
    console.log('任务已停止:', data.taskId);
});
```

### 连接状态和实时数据

```typescript
// 监听连接状态变化
window.electron.ipcRenderer.on('event:connectionStatus', (event, data) => {
    console.log('连接状态变化:', data);
});

// 监听实时行情数据
window.electron.ipcRenderer.on('event:realtimeData', (event, data) => {
    console.log('实时数据:', data);
});

// 监听错误事件
window.electron.ipcRenderer.on('event:error', (event, data) => {
    console.error('系统错误:', data);
});
```

## ⚙️ 配置管理

### 环境配置

系统支持三种环境配置：

1. **开发环境** (`development`)
2. **测试环境** (`test`)
3. **生产环境** (`production`)

通过 `NODE_ENV` 环境变量控制：

```bash
# 开发环境
NODE_ENV=development npm start

# 测试环境
NODE_ENV=test npm start

# 生产环境
NODE_ENV=production npm start
```

### 配置文件位置

- 主配置：`src/trading/config.ts`
- 服务配置：`src/services/TradingSystemService.ts`

## 🔧 开发和调试

### 启用调试日志

```bash
export DEBUG=trading-system:*
npm start
```

### 开发者工具

在渲染进程中可以使用快捷键：
- `F12` 或 `Ctrl+Shift+I` - 开启开发者工具
- `F5` 或 `Ctrl+R` - 重新加载应用

### 日志监控

主进程日志会输出到控制台，包含：
- 系统初始化状态
- 连接建立和断开
- 任务状态变化
- 错误信息

## 📈 任务配置示例

### 网格交易策略

```typescript
const gridTradingTask = {
    stockCode: 'HK.00700',
    strategyType: 'grid_trading',
    strategyParams: {
        gridSpacing: 0.5,      // 网格间距
        gridLevels: 10,        // 网格层数
        positionSize: 100,     // 每格仓位
        centerPrice: 420.0     // 中心价格
    }
};
```

### 均线交叉策略

```typescript
const maCrossTask = {
    stockCode: 'HK.00700',
    strategyType: 'ma_cross',
    strategyParams: {
        fastPeriod: 5,         // 快线周期
        slowPeriod: 20,        // 慢线周期
        positionSize: 200      // 交易仓位
    }
};
```

### 动量突破策略

```typescript
const momentumTask = {
    stockCode: 'HK.00700',
    strategyType: 'momentum_breakout',
    strategyParams: {
        period: 14,            // 计算周期
        threshold: 0.02,       // 突破阈值
        positionSize: 150      // 交易仓位
    }
};
```

## 🛡️ 风险管理

系统内置风险管理功能：

```typescript
const taskWithRisk = {
    stockCode: 'HK.00700',
    strategyType: 'grid_trading',
    strategyParams: {
        // 策略参数...
    },
    riskControl: {
        maxLoss: 1000,         // 最大亏损
        maxPosition: 5000,     // 最大仓位
        stopLossPercent: 0.05, // 止损百分比
        takeProfitPercent: 0.1 // 止盈百分比
    }
};
```

## 🚨 错误处理

### 常见错误和解决方案

1. **系统初始化失败**
   - 检查富途 OpenD 是否运行 (WebSocket端口33333)
   - 验证WebSocket密钥: 232df6441fb2bbdd
   - 检查华盛通交易服务连接 (端口7777)
   - 查看控制台错误日志

2. **任务创建失败**
   - 验证股票代码格式
   - 检查策略参数完整性
   - 确认系统已初始化

3. **连接断开**
   - 系统会自动重连
   - 监听连接状态事件
   - 必要时手动重新初始化

### 错误码说明

- `TRADING_SYSTEM_NOT_INITIALIZED` - 交易系统未初始化
- `INVALID_TASK_CONFIG` - 无效的任务配置
- `CONNECTION_FAILED` - 连接失败
- `TASK_NOT_FOUND` - 任务不存在

## 🎯 最佳实践

1. **系统启动**
   - 等待系统初始化完成再创建任务
   - 监听服务就绪事件

2. **任务管理**
   - 定期检查任务状态
   - 实现任务状态变化的 UI 反馈
   - 妥善处理任务错误

3. **资源管理**
   - 应用退出前停止所有任务
   - 清理事件监听器
   - 关闭系统连接

4. **错误处理**
   - 始终检查 API 响应的 success 字段
   - 实现重试机制
   - 提供用户友好的错误信息

## 📝 API 响应格式

所有 IPC 调用都返回统一格式：

```typescript
interface ApiResponse<T> {
    success: boolean;    // 操作是否成功
    message: string;     // 响应消息
    data: T | null;      // 返回数据
    error?: string;      // 错误信息（如有）
}
```

## 🔗 相关链接

- [交易系统架构文档](src/trading/README.md)
- [任务管理器文档](src/trading/__tests__/README.md)
- [API 接口文档](src/ipc/types.ts)
- [配置文件说明](src/trading/config.ts)