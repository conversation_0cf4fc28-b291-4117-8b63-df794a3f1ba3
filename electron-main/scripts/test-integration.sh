#!/bin/bash

# Electron 主进程交易系统集成测试脚本
# ========================================

echo "🚀 启动 Electron 主进程交易系统集成测试..."
echo ""

# 检查当前目录
if [[ ! -f "src/index.ts" ]]; then
    echo "❌ 请在 electron-main 根目录下运行此脚本"
    exit 1
fi

# 设置环境变量
export NODE_ENV=development
export DEBUG=trading-system:*

echo "🔧 设置环境变量:"
echo "   NODE_ENV=$NODE_ENV"
echo "   DEBUG=$DEBUG"
echo ""

# 编译 TypeScript
echo "🔨 编译 TypeScript 代码..."
npx tsc --build

if [[ $? -ne 0 ]]; then
    echo "❌ TypeScript 编译失败"
    exit 1
fi

echo "✅ TypeScript 编译完成"
echo ""

# 启动 Electron 应用
echo "🎯 启动 Electron 应用..."
echo "   - 集成交易系统服务"
echo "   - 启用 IPC 通信接口"
echo "   - 自动初始化交易系统"
echo ""

npm start

echo ""
echo "🎉 测试完成"