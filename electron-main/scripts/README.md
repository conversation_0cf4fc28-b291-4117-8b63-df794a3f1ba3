# 开发脚本说明

## 📁 目录结构

```
scripts/
├── test-integration.sh      # 交易系统集成测试启动脚本
├── test-ipc.sh             # IPC通信测试脚本（如存在）
└── README.md               # 本文件
```

## 🚀 脚本说明

### `test-integration.sh` - 交易系统集成测试

**用途**: 以开发模式启动Electron应用并测试交易系统集成

**功能**:
- 设置开发环境变量 (`NODE_ENV=development`)
- 启用调试日志 (`DEBUG=trading-system:*`)
- 编译TypeScript代码
- 启动Electron应用并加载交易系统

**使用方法**:
```bash
# 在 electron-main 根目录执行
./scripts/test-integration.sh
```

**适用场景**:
- 开发期间测试交易系统集成
- 验证IPC通信接口
- 调试系统初始化过程
- 演示完整功能

### `test-ipc.sh` - IPC通信测试（如存在）

**用途**: 测试IPC通信接口的响应性和正确性

## 🎯 使用建议

### 开发阶段
- 使用 `test-integration.sh` 进行日常开发测试
- 观察控制台输出验证系统状态
- 通过调试日志排查问题

### 部署前验证
- 在不同环境下运行脚本
- 确保所有组件正常初始化
- 验证错误处理机制

### CI/CD集成
这些脚本可以集成到自动化构建流程中：

```yaml
# 示例 GitHub Actions 配置
- name: Test Trading System Integration
  run: |
    cd electron-main
    chmod +x scripts/test-integration.sh
    timeout 60s scripts/test-integration.sh || true
```

## ⚠️ 注意事项

1. **依赖服务**: 运行前确保必要的服务已启动：
   - 富途OpenD (WebSocket端口33333, 密钥: 232df6441fb2bbdd)
   - 华盛通交易服务 (端口7777)

2. **环境变量**: 可根据需要修改脚本中的环境设置

3. **超时处理**: 在自动化环境中建议设置超时时间

## 🔧 脚本维护

- 定期更新环境变量设置
- 根据新功能调整测试步骤
- 保持与主应用启动方式的一致性