/**
 * IPC 处理器 CLI 测试程序
 * =========================
 * 独立的 Node.js 程序，用于测试 Electron IPC 处理器的功能
 * 不依赖 Electron，直接调用交易管理器进行测试
 */

import * as readline from 'readline';
import { TradingManager } from '@/trading/TradingManager';
import { configManager } from '@/utils/configManager';
import { Side, OrderType } from '@/trading/types';

class IPCHandlersTester {
    private tradingManager: TradingManager | null = null;
    private rl: readline.Interface;

    constructor() {
        this.rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });
    }

    async start(): Promise<void> {
        console.log('华盛通 IPC 处理器测试程序');
        console.log('==========================');

        try {
            console.log('初始化配置...');
            const config = configManager.getConfig();
            const huashengConfig = config.trading.huasheng;
            if (!huashengConfig) {
                throw new Error('华盛通配置未找到');
            }
            console.log(`使用配置: ${huashengConfig.host}:${huashengConfig.port}`);

            console.log('初始化交易管理器...');
            const tradingConfig = {
                huasheng: huashengConfig
            };
            
            this.tradingManager = new TradingManager(tradingConfig);
            console.log('交易管理器初始化完成');

            // 自动连接到华盛通
            console.log('\\n自动连接到华盛通服务器...');
            const connectResult = await this.tradingManager.connect('huasheng');
            console.log('✅ 连接结果:', JSON.stringify(connectResult, null, 2));

            this.showMenu();
        } catch (error) {
            console.error('启动失败:', error);
            process.exit(1);
        }
    }

    showMenu(): void {
        console.log('\\n可用的测试命令:');
        console.log('login      - 测试登录功能');
        console.log('getFunds   - 获取资金信息');
        console.log('getPositions - 获取持仓信息');
        console.log('getOrders  - 获取订单列表');
        console.log('exit       - 退出程序');

        this.rl.setPrompt('\\n请输入命令: ');
        this.rl.prompt();
        
        this.rl.once('line', (answer) => {
            this.handleCommand(answer.trim());
        });
    }

    async handleCommand(command: string): Promise<void> {
        if (!this.tradingManager) {
            console.error('交易管理器未初始化');
            this.showMenu();
            return;
        }

        try {
            switch (command.toLowerCase()) {
                case 'login':
                    await this.testLogin();
                    break;
                case 'getfunds':
                    await this.testGetFunds();
                    break;
                case 'getpositions':
                    await this.testGetPositions();
                    break;
                case 'getorders':
                    await this.testGetOrders();
                    break;
                case 'exit':
                    console.log('程序退出');
                    if (this.tradingManager) {
                        await this.tradingManager.disconnect('huasheng');
                    }
                    process.exit(0);
                    break;
                default:
                    console.log('未知命令');
                    break;
            }
        } catch (error) {
            console.error('执行命令失败:', error);
        }

        this.showMenu();
    }

    async testLogin(): Promise<void> {
        console.log('\\n测试登录功能...');
        try {
            const result = await this.tradingManager!.login('huasheng');
            console.log('✅ 登录结果:', JSON.stringify(result, null, 2));
        } catch (error) {
            console.error('❌ 登录失败:', error);
        }
    }

    async testGetFunds(): Promise<void> {
        console.log('\\n💰 测试获取资金信息...');
        try {
            const result = await this.tradingManager!.getFunds('huasheng');
            console.log('✅ 资金信息:', JSON.stringify(result, null, 2));
        } catch (error) {
            console.error('❌ 获取资金信息失败:', error);
        }
    }

    async testGetPositions(): Promise<void> {
        console.log('\\n📈 测试获取持仓信息...');
        try {
            const result = await this.tradingManager!.getPositions('huasheng');
            console.log('✅ 持仓信息:', JSON.stringify(result, null, 2));
        } catch (error) {
            console.error('❌ 获取持仓信息失败:', error);
        }
    }

    async testGetOrders(): Promise<void> {
        console.log('\\n📋 测试获取订单列表...');
        try {
            const result = await this.tradingManager!.getOrders('huasheng');
            console.log('✅ 订单列表:', JSON.stringify(result, null, 2));
        } catch (error) {
            console.error('❌ 获取订单列表失败:', error);
        }
    }
}

if (require.main === module) {
    const tester = new IPCHandlersTester();

    process.on('SIGINT', async () => {
        console.log('\\n程序被用户中断');
        process.exit(0);
    });

    tester.start().catch((error) => {
        console.error('程序启动失败:', error);
        process.exit(1);
    });
}