import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(async () => ({
    plugins: [react()],

    // Electron 环境使用相对路径，确保资源能正确加载
    base: "./",

    resolve: {
        alias: {
            "@": path.resolve(__dirname, "./src")
        }
    },

    // 开发服务器配置
    clearScreen: false,
    server: {
        port: 1420,
        strictPort: true,
        host: true, // 允许外部访问
        watch: {
            ignored: ["**/src-tauri/**", "**/electron-main/**"]
        }
    },

    // 构建配置
    build: {
        // 确保构建输出目录正确
        outDir: "dist-web",
        emptyOutDir: true
    }
}));
